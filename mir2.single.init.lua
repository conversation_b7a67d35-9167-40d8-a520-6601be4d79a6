﻿-- chunkname: @mir2\\single\\init.lua

game = import(".game")
res = import(".res")
socketTCP = require("an.overwrite.SocketTCP")
sound = import(".sound")
cache = import(".cache")

if not m2debug then
	m2debug = import(".m2debug")
end

m2spr = import(".m2spr")
gplus = import(".gplus")
gameEvent = import(".gameEvent")
watchdog = import(".watchdog")

if DEBUG > 0 then
	clientRsbQueue = import(".clientRsbQueue")
end
