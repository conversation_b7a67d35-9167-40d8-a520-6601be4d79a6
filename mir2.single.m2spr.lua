﻿-- chunkname: @mir2\\single\\m2spr.lua

local m2sprMgr = {
	aniIdCnt = 0,
	texIdCnt = 0,
	debuginfo = "",
	texQueue = {},
	aniQueue = {},
	lasttime = socket.gettime()
}
local m2spr

function m2sprMgr.new(...)
	return m2spr.new(...)
end

function m2sprMgr.playAnimation(...)
	return m2spr.playAnimation(...)
end

local syncLoads = {}
local syncMustLoads = {}
local asyncLoads = {}

setmetatable(syncLoads, {
	__mode = "kv"
})
setmetatable(syncMustLoads, {
	__mode = "kv"
})
setmetatable(asyncLoads, {
	__mode = "kv"
})

function m2sprMgr:loop()
	local nowtime = socket.gettime()

	self.lasttime = nowtime

	local needUpt = m2spr.needUpt
	local upt = m2spr.upt
	local aniUpt = m2spr.aniUpt
	local aniuptStart = ycFunction.getClock()
	local texQueue = self.texQueue

	for v, k in pairs(self.aniQueue) do
		if nowtime >= v.aniNextTime then
			local start = v.aniStartTime
			local delay = aniUpt(v, nowtime - start)

			v.aniNextTime = nowtime + delay
		end
	end

	local aniuptEnd = ycFunction.getClock()
	local texuptStart = aniuptEnd

	syncLoads.num = 0
	syncMustLoads.num = 0
	asyncLoads.num = 0

	local texQueueNums

	if DEBUG > 0 then
		texQueueNums = table.nums(texQueue)
	end

	local insert = table.insert

	for v, k in pairs(texQueue) do
		local info = res.getinfo(v.imgid, v.idx)

		if info then
			if info.loading then
				v.asyncRequested = true
			else
				upt(v, info)

				texQueue[v] = nil
			end
		elseif v.asyncPriority then
			if not v.asyncRequested then
				asyncLoads.num = asyncLoads.num + 1
				asyncLoads[asyncLoads.num] = v
			end
		elseif v.ani and not v.ani.noForever then
			syncLoads.num = syncLoads.num + 1
			syncLoads[syncLoads.num] = v
		else
			syncMustLoads.num = syncMustLoads.num + 1
			syncMustLoads[syncMustLoads.num] = v
		end
	end

	local resGetTex = res.gettex
	local resGetInfo = res.getinfo
	local begin = socket.gettime()

	for k = 1, syncMustLoads.num do
		local v = syncMustLoads[k]
		local _, info = resGetTex(v.imgid, v.idx)

		upt(v, info)

		texQueue[v] = nil
	end

	for k = 1, syncLoads.num do
		local v = syncLoads[k]
		local _, info = resGetTex(v.imgid, v.idx)

		upt(v, info)

		texQueue[v] = nil
		syncLoads[k] = nil

		if socket.gettime() - begin > 0.02 then
			break
		end
	end

	for k = 1, syncLoads.num do
		local v = syncLoads[k]

		if v then
			local info = resGetInfo(v.imgid, v.idx)

			if info then
				upt(v, info)

				texQueue[v] = nil
			end
		end
	end

	for k = 1, asyncLoads.num do
		local v = asyncLoads[k]

		v.asyncRequested = true

		local _, info = resGetTex(v.imgid, v.idx, v.asyncPriority)

		if not info.loading then
			upt(v, info)

			texQueue[v] = nil
		end
	end

	local texuptEnd = ycFunction.getClock()

	if DEBUG > 0 then
		self.debuginfo = {
			aniuptEnd - aniuptStart,
			texuptEnd - texuptStart
		}
		self.debuginfo = table.concat(self.debuginfo, "-")
	end
end

function m2sprMgr.addTexQueue(node)
	m2sprMgr.texQueue[node] = true
end

function m2sprMgr.removeTexQueue(node)
	m2sprMgr.texQueue[node] = nil
end

function m2sprMgr.addAniQueue(node)
	m2sprMgr.aniQueue[node] = true
end

function m2sprMgr.removeAniQueue(node)
	m2sprMgr.aniQueue[node] = nil
end

function m2sprMgr.removeAllSchedule()
	if m2sprMgr.loopListener then
		cc.Director:getInstance():getEventDispatcher():removeEventListener(m2sprMgr.loopListener)

		m2sprMgr.loopListener = nil
	end
end

m2spr = class("m2spr")

local __setVisible = cc.Node.setVisible
local __getIsInsideBounds = ycM2Sprite.getIsInsideBounds
local __setCenterOffset = ycM2Sprite.setCenterOffset
local __setContentSize = cc.Node.setContentSize
local __getContentSize = cc.Node.getContentSize
local __setPosition = cc.Node.setPosition
local __setBlendFunc = cc.Sprite.setBlendFunc
local __setTextureAutoSetRect = ycM2Sprite.setTextureAutoSetRect
local __setFlippedX = cc.Sprite.setFlippedX
local __setAnchorPoint = cc.Node.setAnchorPoint
local __addTextureFrame = ycM2Sprite.addTextureFrame
local __playAniAction = ycM2Sprite.playAniAction
local __setNodeEventEnabled = cc.Node.setNodeEventEnabled
local __setSpriteFrame = cc.Sprite.setSpriteFrame
local __setKeepBlendFunc = ycM2Sprite.setKeepBlendFunc

table.merge(m2spr, {
	imgid,
	idx,
	setOffset,
	blend,
	syncPriority,
	asyncPriority,
	asyncRequested,
	unknowSize,
	texQueueID,
	aniQueueID,
	ani,
	isShow
})

local etcVer = "//ver\nattribute vec4 a_position;\nattribute vec2 a_texCoord;\nattribute vec4 a_color;\n\n#ifdef GL_ES\nvarying lowp vec4 v_fragmentColor;\nvarying mediump vec2 v_texCoord;\nvarying mediump vec2 v_alphaCoord;  \n#else\nvarying vec4 v_fragmentColor;\nvarying vec2 v_texCoord;\nvarying vec2 v_alphaCoord;  \n#endif\n\nvoid main()\n{\n    gl_Position = CC_PMatrix * a_position;\n    v_fragmentColor = a_color;\n    v_texCoord = a_texCoord;\n    v_alphaCoord = v_texCoord + vec2(0.0, 0.5);  \n}\n"
local etcFrag = "//Frag\n\n#ifdef GL_ES\nvarying lowp vec4 v_fragmentColor;\nvarying mediump vec2 v_texCoord;\nvarying mediump vec2 v_alphaCoord;\n#else\nvarying vec4 v_fragmentColor;\nvarying vec2 v_texCoord;\nvarying vec2 v_alphaCoord;  \n#endif\n\nvoid main()\n{\n    if(v_texCoord.y >= 0.5){\n    \tgl_FragColor = vec4(0,0,0,0);\n    \treturn;\n    }\n    vec4 texColor = texture2D(CC_Texture0, v_texCoord);\n    texColor.a = texture2D(CC_Texture0, v_alphaCoord).r;\n    gl_FragColor = texColor * v_fragmentColor;\n}\n"
local etcProgram = cc.GLProgram:createWithByteArrays(etcVer, etcFrag)

etcProgram:bindAttribLocation(cc.ATTRIBUTE_NAME_POSITION, 4)
etcProgram:bindAttribLocation(cc.ATTRIBUTE_NAME_COLOR, cc.VERTEX_ATTRIB_COLOR)
etcProgram:bindAttribLocation(cc.ATTRIBUTE_NAME_TEX_COORD, cc.VERTEX_ATTRIB_TEX_COORDS)
etcProgram:link()
etcProgram:updateUniforms()
etcProgram:retain()

function m2spr:add2(...)
	self.spr:add2(...)

	return self
end

function m2spr:addto(...)
	self.spr:add2(...)

	return self
end

function m2spr:addTo(...)
	self.spr:add2(...)

	return self
end

function m2spr:hide()
	self:setVisible(false)

	return self
end

function m2spr:show()
	self:setVisible(true)

	return self
end

function m2spr:anchor(...)
	__setAnchorPoint(self.spr, ...)

	return self
end

function m2spr:runs(...)
	self.spr:runs(...)

	return self
end

function m2spr:pos(x, y)
	__setPosition(self.spr, x, y)

	return self
end

function m2spr:flipX(...)
	__setFlippedX(self.spr, ...)

	return self
end

function m2spr:flipY(...)
	__setFlippedY(self.spr, ...)

	return self
end

function m2spr:run(...)
	self.spr:run(...)

	return self
end

function m2spr:runs(...)
	self.spr:runs(...)

	return self
end

function m2spr:setScaleX(...)
	self.spr:setScaleX(...)

	return self
end

function m2spr:setScaleY(...)
	self.spr:setScaleY(...)

	return self
end

function m2spr:setFilter(...)
	self.spr:setFilter(...)

	return self
end

function m2spr:removeSelf()
	self.spr:removeSelf()
end

function m2spr:setColor(c)
	self.spr:setColor(c)

	return self
end

local function __m2spr_setFixArtifactsByStrechingTexel(p, f)
	local pu_ret, pu = pcall(function()
		p:setFixArtifactsByStrechingTexel(f)

		return true
	end)
end

function m2spr:setFixArtifactsByStrechingTexel(f)
	__m2spr_setFixArtifactsByStrechingTexel(self.spr, f)

	return self
end

function m2spr:ctor(imgid, idx, params)
	local inst = ycM2Sprite:create(res.default2(), params and params.setOffset, params and params.blend)

	function inst.onCleanup()
		self:onCleanup()
	end

	self.spr = inst
	params = params or {}
	self.imgid = imgid
	self.idx = idx
	self.setOffset = params.setOffset
	self.blend = params.blend
	self.asyncPriority = params.asyncPriority
	self.isShow = true

	__setNodeEventEnabled(inst, true)

	self.onctor = true

	self:texChanged()
end

function m2spr:onCleanup()
	m2sprMgr.removeTexQueue(self)
	m2sprMgr.removeAniQueue(self)
end

function m2spr:needUpt()
	return __getIsInsideBounds(self.spr)
end

function m2spr:setVisible(b)
	__setVisible(self.spr, b)

	self.isShow = b

	if b then
		if self.ani then
			m2sprMgr.addAniQueue(self)
		else
			self:texChanged()
		end
	end
end

function m2spr:getContentSize()
	if self.unknowSize then
		self.unknowSize = nil

		local info = res.getinfo(self.imgid, self.idx, true)

		if info and not info.err then
			__setContentSize(self.spr, info.w, info.h)
		else
			__setContentSize(self.spr, 0, 0)
		end
	end

	return __getContentSize(self.spr)
end

function m2spr:setBlend(blend)
	if self.blend ~= blend then
		self.blend = blend

		self:updateBlendFunc()
	end
end

function m2spr:updateBlendFunc()
	if self.blend then
		__setBlendFunc(self.spr, gl.SRC_ALPHA, gl.ONE)
		__setKeepBlendFunc(self.spr, true)
	else
		__setBlendFunc(self.spr, gl.ONE, gl.ONE_MINUS_SRC_ALPHA)
		__setKeepBlendFunc(self.spr, false)
	end
end

function m2spr:texChanged()
	if self.imgid and self.idx then
		self.unknowSize = true
		self.asyncRequested = nil

		m2sprMgr.addTexQueue(self)
	end
end

local __setGLProgram = cc.Node.setGLProgram
local upt_result = {
	success = 0,
	textureErr = 1,
	sprErr = 2
}

function m2spr:upt(info)
	if not info or info.err then
		__setTextureAutoSetRect(self.spr, res.default2())

		return upt_result.textureErr
	end

	local tex, x, y, w, h = info.tex, info.x, info.y, info.w, info.h

	if not tolua.isnull(self.spr) then
		__setSpriteFrame(self.spr, tex)
	else
		return upt_result.sprErr
	end

	if tex.isDownloading and tex:isDownloading() and main_scene and main_scene.ui then
		main_scene.ui:tip("资源下载中...")
	end

	self.unknowSize = nil

	return upt_result.success
end

function m2spr:setImg(imgid, idx)
	if self.imgid ~= imgid or self.idx ~= idx then
		self.imgid = imgid
		self.idx = idx

		self:texChanged()
	end
end

function m2spr:setDelay(delay)
	self.ani.delay = delay
end

function m2spr:resetAndPlay()
	local nt = game.loopBegin or socket.gettime()

	self.aniStartTime = nt
	self.aniNextTime = nt

	m2sprMgr.addAniQueue(self)
end

_G.useLuaAni = true

function m2spr:playAni(img, begin, frame, delay, blend, autoRemove, noForever, callback, asyncPriority, nextIdxSpace)
	self.asyncPriority = asyncPriority

	if _G.useLuaAni then
		self.ani = {
			dt = 0,
			img = img,
			begin = begin,
			frame = frame,
			delay = delay or 0.1,
			noForever = noForever,
			callback = callback or noForever and autoRemove and handler(self, self.removeSelf),
			nextIdxSpace = nextIdxSpace or 1
		}

		m2sprMgr.removeTexQueue(self)

		local nt = game.loopBegin or socket.gettime()

		self.aniStartTime = nt
		self.aniNextTime = nt

		self:setBlend(blend)
		m2sprMgr.addAniQueue(self)
	else
		nextIdxSpace = nextIdxSpace or 1
		delay = delay or 0.1

		for idx = begin, begin + frame * nextIdxSpace do
			local frame = res.getframe(img, idx, self.setOffset, 1)

			__addTextureFrame(self.spr, frame, idx)
		end

		__playAniAction(self.spr, begin, frame, delay, false, nextIdxSpace, not noForever, autoRemove, callback and cc.CallFunc:create(callback))
	end

	return self
end

function m2spr.playAnimation(img, begin, frame, delay, blend, autoRemove, noForever, callback, noSetOffset, asyncPriority, nextIdxSpace)
	local ani = m2spr.new(nil, nil, {
		setOffset = not noSetOffset,
		blend = blend
	}):playAni(img, begin, frame, delay, blend, autoRemove, noForever, callback, asyncPriority, nextIdxSpace)

	return ani.spr, ani
end

function m2spr:stopAnimation()
	m2sprMgr.removeAniQueue(self)
end

function m2spr:aniUpt(dt)
	local data = self.ani
	local delay = data.delay
	local idx = math.floor(dt / delay)

	if idx > data.frame - 1 then
		if data.noForever then
			self:stopAnimation()

			if data.callback then
				data.callback(self)
			end

			return 0
		else
			idx = idx % data.frame

			local nt = game.loopBegin or socket.gettime()

			self.aniStartTime = nt
			self.aniNextTime = nt
		end
	end

	if self.isShow then
		if data.lastIdx == idx then
			return delay - dt % delay
		end

		data.lastIdx = idx

		local imgIdx = data.begin + idx * data.nextIdxSpace

		self:setImg(data.img, imgIdx)
	end

	return delay - dt % delay
end

do
	local listener = cc.EventListenerCustom:create("director_after_update", handler(m2sprMgr, m2sprMgr.loop))

	cc.Director:getInstance():getEventDispatcher():addEventListenerWithFixedPriority(listener, 1)

	m2sprMgr.loopListener = listener
end

return m2sprMgr
