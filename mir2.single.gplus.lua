﻿-- chunkname: @mir2\\single\\gplus.lua

local gplus = {
	phone,
	userid,
	ticket,
	appid = "791000255"
}

function gplus.setListenner(listenner)
	gplus.listenner = listenner
end

function gplus.removeListenner()
	gplus.listenner = nil
end

function gplus.call(key, ...)
	if gplus.listenner and gplus.listenner[key] then
		gplus.listenner[key](gplus.listenner, ...)
	end
end

function gplus.init()
	if device.platform == "ios" then
		local ok, ret = luaoc.callStaticMethod("gplusSDK", "call", {
			type = "initSDK:",
			appid = gplus.appid,
			callback = function(dic)
				gplus.call("gplusInitEnd", dic.code, dic.msg)
			end
		})
	elseif device.platform == "android" then
		luaj.callStaticMethod(platformSdk:getPackageName() .. "gplusSDK", "initSDK", {
			gplus.appid,
			function(msg)
				local dic = json.decode(msg)

				gplus.call("gplusInitEnd", dic.code, dic.msg)
			end
		})
	end
end

function gplus.login()
	if device.platform == "ios" then
		local ok, ret = luaoc.callStaticMethod("gplusSDK", "call", {
			type = "login:",
			callback = function(dic)
				gplus.call("gplusLoginEnd", dic.code, dic.msg, dic.ticket, dic.userid, dic.phone)
			end
		})
	elseif device.platform == "android" then
		luaj.callStaticMethod(platformSdk:getPackageName() .. "gplusSDK", "login", {
			function(msg)
				local dic = json.decode(msg)

				gplus.call("gplusLoginEnd", dic.code, dic.msg, dic.ticket, dic.userid, dic.phone)
			end
		})
	end
end

function gplus.logout()
	gplus.phone = nil
	gplus.userid = nil
	gplus.ticket = nil

	if device.platform == "ios" then
		local ok, ret = luaoc.callStaticMethod("gplusSDK", "call", {
			type = "loginOut:",
			callback = function(dic)
				gplus.call("gplusLogoutEnd", dic.code, dic.msg)
			end
		})
	elseif device.platform == "android" then
		luaj.callStaticMethod(platformSdk:getPackageName() .. "gplusSDK", "logout", {
			function(msg)
				local dic = json.decode(msg)

				gplus.call("gplusLogoutEnd", dic.code, dic.msg)
			end
		})
	end
end

function gplus.getTicket()
	if device.platform == "ios" then
		local ok, ret = luaoc.callStaticMethod("gplusSDK", "call", {
			type = "getTicket:",
			appid = gplus.appid,
			areaid = tostring(def.areaID),
			callback = function(dic)
				gplus.call("gplusGetTicketEnd", dic.code, dic.msg, dic.ticket)
			end
		})
	elseif device.platform == "android" then
		luaj.callStaticMethod(platformSdk:getPackageName() .. "gplusSDK", "getTicket", {
			gplus.appid,
			tostring(def.areaID),
			function(msg)
				local dic = json.decode(msg)

				dump(dic)
				gplus.call("gplusGetTicketEnd", dic.code, dic.msg, dic.ticket)
			end
		})
	end
end

function gplus.pay(productid, gameOrderId, extendInfo)
	if device.platform == "ios" then
		local ok, ret = luaoc.callStaticMethod("gplusSDK", "call", {
			type = "pay:",
			appid = gplus.appid,
			areaid = tostring(def.areaID),
			productid = productid,
			gameOrderid = gameOrderId,
			extendInfo = extendInfo,
			callback = function(dic)
				print("IOS 支付结果:", dic.msg, dic.code)
				gplus.call("gplusPayEnd", dic.code, dic.msg)
			end
		})
	elseif device.platform == "android" then
		luaj.callStaticMethod(platformSdk:getPackageName() .. "gplusSDK", "pay", {
			tostring(def.areaID),
			tostring(productid),
			tostring(gameOrderId),
			tostring(extendInfo),
			function(msg)
				local dic = json.decode(msg)

				print("android 支付结果", msg)
				gplus.call("gplusPayEnd", dic.code, dic.msg)
			end
		})
	end
end

function gplus.extendFunction(func, parameter)
	if device.platform == "ios" then
		local ok, ret = luaoc.callStaticMethod("gplusSDK", "call", {
			type = "extendFunction:",
			func = func,
			parameter = parameter,
			callback = function(dic)
				print("IOS 检查结果:")
				dump(dic)
				gplus.call("gplusCheckPaidOrderEnd", dic.code, dic.msg)
			end
		})
	elseif device.platform == "android" then
		-- block empty
	end
end

return gplus
