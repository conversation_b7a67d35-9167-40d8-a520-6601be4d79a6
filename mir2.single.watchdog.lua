﻿-- chunkname: @mir2\\single\\watchdog.lua

local socket = require("socket")
local watchdog = {
	beginTime = 0,
	isChecking = false,
	sockets = {},
	timeFunc = socket.gettime
}

function watchdog:beforeCheck()
	self.isChecking = true
	self.beginTime = self.timeFunc()
end

function watchdog:afterCheck(elapsedSeconds)
	local dt = self.timeFunc() - self.beginTime
	local speed = dt / elapsedSeconds

	if type(self.listener) == "function" then
		self.listener(speed)
	end

	self.isChecking = false

	return speed
end

function watchdog:setListener(cb)
	self.listener = cb
end

local testThreadNum = 40

function watchdog:testSocket()
	if DEBUG < 1 then
		return
	end

	for i = 1, testThreadNum do
		local socket = self.sockets[i]

		if not socket or tolua.isnull(socket) then
			socket = MirTcpClient:newInstance()

			socket:setIsFreeOnTerminate(false)
			socket:setIsLoopConnected(false)
			socket:subscribeOnState(function(state, msg)
				return
			end)
			socket:addRemoteHost("***************", 7000)
			socket:setOption(1, 1)

			self.sockets[i] = socket
		end

		socket:connect()
	end
end

function watchdog:checkSpeedWithSocket()
	if self.isChecking then
		return
	end

	self:beforeCheck()

	local socket = self.socket

	if not socket or tolua.isnull(socket) then
		socket = MirTcpClient:newInstance()

		socket:setIsFreeOnTerminate(false)
		socket:setIsLoopConnected(false)
		socket:subscribeOnState(function(state, msg)
			if state == TcpClientState.ecsDisconnected then
				self:afterCheck(1)
			end
		end)

		self.socket = socket
	end

	socket:addRemoteHost("***************", 7000)
	socket:setOption(1, 1)
	socket:connect()
end

function watchdog:checkSpeedWithSleep()
	if self.isChecking then
		return
	end

	self:beforeCheck()
	socket.sleep(0.01)
	self:afterCheck(0.01)
end

return watchdog
