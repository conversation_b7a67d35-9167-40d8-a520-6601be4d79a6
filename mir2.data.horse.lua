-- chunkname: @mir2\\data\\horse.lua

local horseModel = import(".model.horseModel")
local horse = {
	FCurrIdent = 0,
	state = 0,
	FList = {},
	qiShuData = {
		lv = 0,
		exp = 0
	},
	horseSkillData = {}
}

function horse:setInfo(result)
	self.FCurrIdent = result.FCurrHorseID
	self.FList = result.FHorseList

	g_data.eventDispatcher:dispatch("M_HORSE_LIST_CHG")
	g_data.eventDispatcher:dispatch("M_HORSE_DATA_CHG")
end

function horse:isHave(result)
	if result.FHorseList[1] then
		for i, v in ipairs(result.FHorseList) do
			if v.FID > 0 and tonumber(v.FInvalidTime) < 0 and v.FLevel ~= -1 then
				self.Have = true

				return
			else
				self.Have = false
			end
		end
	end
end

function horse:getisHave()
	return self.Have
end

function horse:get(id)
	for i, v in ipairs(self.FList) do
		if v.FID == id then
			return v
		end
	end

	return nil
end

function horse:add(data)
	self.FList[#self.FList + 1] = data

	g_data.eventDispatcher:dispatch("M_HORSE_LIST_CHG")
	g_data.eventDispatcher:dispatch("M_HORSE_DATA_CHG")
end

function horse:del(id)
	local isdel = false

	for i, v in ipairs(self.FList) do
		if v.FID == id then
			isdel = true

			table.remove(self.FList, i)

			break
		end
	end

	if self.FCurrIdent == id then
		self.FCurrIdent = 0
	end

	if isdel then
		g_data.eventDispatcher:dispatch("M_HORSE_LIST_CHG")
		g_data.eventDispatcher:dispatch("M_HORSE_DATA_CHG")
	end
end

function horse:update(id, info)
	local data = self:get(id)

	if data then
		for i, v in pairs(info) do
			if data[i] ~= nil then
				data[i] = v
			end
		end
	end

	return data
end

function horse:setCurrentIdent(ident)
	self.FCurrIdent = ident

	g_data.eventDispatcher:dispatch("M_HORSE_DATA_CHG")
end

function horse:getHorseById(id)
	local info = horseModel.new({
		id = id,
		base = self
	})

	return info
end

function horse:isHaveHorse(id)
	for i, v in ipairs(self.FList) do
		if v.FID == id then
			return true
		end
	end

	return false
end

function horse:getDataById(id)
	for i, v in ipairs(self.FList) do
		if v.FID == horseid then
			return v
		end
	end

	return nil
end

function horse:getDataByIdent(horseIdent)
	for i, v in ipairs(self.FList) do
		if v.FCliHorseIdent == horseid then
			return v
		end
	end

	return nil
end

function horse:getCurrentIdent()
	return self.FCurrIdent
end

function horse:getCurrentData()
	return self:getDataByIdent(self:getCurrentIdent())
end

function horse:setRideState(state)
	self.state = state
end

function horse:setQiShuData(data)
	if data then
		self.qiShuData.lv = data.FHorseSkillLevel or 0
		self.qiShuData.exp = data.FHorseSkillExpCount or 0
	end
end

function horse:getQiShuData()
	return self.qiShuData
end

function horse:setHorseSkillData(data)
	if data and data.FSkillType then
		local horseId = def.horse:getHorseSkillIdByTpye(data.FSkillType)

		if horseId then
			self.horseSkillData[horseId] = data.FSkillLevel or 0
		end
	end
end

function horse:getHorseSkillData(horseId)
	return self.horseSkillData[horseId] or 0
end

function horse:getAddSpeed()
	local result = 0
	local horseId = 83
	local lv = self.horseSkillData[horseId] or 0

	if lv > 0 then
		local info = def.horse:getHorseSkillCfgByIdAndLv(horseId, lv)

		self.arrtiSp = self:getSpEquipSpeed() or 0
		self.spEquipSP = 0

		if self.arrtiSp ~= 0 then
			self.spEquipSP = self.arrtiSp._item.AC
		end

		if info then
			local attr = string.split(info.GivePro, ";")

			for i, v in ipairs(attr) do
				if string.find(v, "移动速度") then
					local speed = string.split(v, "=")[2]

					result = tonumber(speed) or 0
					result = (result + 10000 + def.horse:getBaseCfgByID(self.FCurrIdent).SpecialMove + main_scene.ui.newHorseSpeed + self.spEquipSP) / 10000

					break
				end
			end
		end
	end

	return result
end

function horse:getSpEquipSpeed()
	local spe = g_data.horse:getSpEquip()
	local resultItems = {}

	for i, v in ipairs(spe) do
		if v.FHorseID == self:getCurrentIdent() then
			resultItems[#resultItems + 1] = v.FEquipList[1].FCliEquip
		end
	end

	self.resultItems = def.horse:setSpEquipP(resultItems)

	return self.resultItems
end

function horse:getHorseSpeed()
	if self.FCurrIdent and def.horse:getBaseCfgByID(self.FCurrIdent) then
		self.arrtiSp = self:getSpEquipSpeed() or 0
		self.spEquipSP = 0

		if self.arrtiSp ~= 0 then
			self.spEquipSP = self.arrtiSp._item.AC
		end

		local speed = def.horse:getBaseCfgByID(self.FCurrIdent).MoveSpeed / (1 + (def.horse:getBaseCfgByID(self.FCurrIdent).SpecialMove + main_scene.ui.newHorseSpeed + main_scene.ui.moveSpeed + self.spEquipSP) / 10000)

		return speed
	end

	return 600
end

function horse:appendMoveSpeedDes(descriptionTable, moveSpeed)
	if moveSpeed ~= 600 then
		local speed = math.floor(600 / (moveSpeed > 0 and moveSpeed or -1) * 100 - 100)

		table.insert(descriptionTable, {
			"骑乘移速: ",
			"+" .. speed .. "%"
		})
	end
end

function horse:setSpEquip(result)
	self.spEquip = result
end

function horse:getSpEquip()
	return self.spEquip
end

function horse:setOtherSpEquip(result)
	self.spEquipOther = result
end

function horse:getOtherSpEquip()
	return self.spEquipOther
end

return horse
