﻿-- chunkname: @upt\\assetUpter.lua

local assetUpter = class("uptScene")

table.merge(assetUpter, {
	assetMgr,
	eventAssetManagerlistener,
	listener,
	hasNewVersion
})

assetUpter.maxRetryTimes = 30

function assetUpter:ctor(versionManifest, storagePath)
	self._versionManifest = versionManifest
	self._storagePath = storagePath

	self:reset()
end

function assetUpter:destroy()
	if self.assetMgr then
		self.assetMgr:release()
		cc.Director:getInstance():getEventDispatcher():removeEventListener(self.eventAssetManagerlistener)

		self.assetMgr = nil
	end
end

function assetUpter:reset(isRetry)
	self:destroy()

	self.errAssetCnt = 0

	local assetMgr = cc.AssetsManagerEx:create(self._versionManifest, self._storagePath)

	assetMgr:retain()

	self.assetMgr = assetMgr
	self.eventAssetManagerlistener = cc.EventListenerAssetsManagerEx:create(self.assetMgr, handler(self, self.onUpdateEvent))

	cc.Director:getInstance():getEventDispatcher():addEventListenerWithFixedPriority(self.eventAssetManagerlistener, 1)

	versionaUrl = self.assetMgr:getLocalManifest():getVersionFileUrl()
	projectUrl = self.assetMgr:getLocalManifest():getManifestFileUrl()
	packageUrl = self.assetMgr:getLocalManifest():getPackageUrl()

	print("assetUpter:reset versionaUrl---------------" .. versionaUrl)
	print("assetUpter:reset projectUrl---------------" .. projectUrl)
	print("assetUpter:reset packageUrl---------------" .. packageUrl)

	if UPT_AFTER_SEL_SERVER and isRetry and g_data.login.localLastSer.serverUptKey and g_data.login.localLastSer.serverUptKey ~= "" then
		self:setRemoteAddressWithKey(g_data.login.localLastSer.serverUptKey)
	end

	self.downloading = false
	self.downloadingFilesSize = {}
	self.downloadFileCnt = 0
end

function assetUpter:checkUpt()
	self.assetMgr:checkUpdate()
end

function assetUpter:startUpt()
	self.downloading = true

	self.assetMgr:update()
end

function assetUpter:retryUptFaildAssets()
	self.assetMgr:downloadFailedAssets()
end

function assetUpter:getCurVersion()
	return self.assetMgr:getLocalManifest():getVersion()
end

function assetUpter:getRemoteVersion()
	local manifest = self.assetMgr:getRemoteManifest()

	if manifest then
		return manifest:getVersion()
	else
		return ""
	end
end

function assetUpter:checkNative()
	local cur = string.split(self:getCurVersion(), ".")
	local newVersion = self:getRemoteVersion()

	if newVersion == "" then
		print("没有更新服务器版本号")

		return false
	else
		local new = string.split(newVersion, ".")

		if tonumber(cur[2]) < tonumber(new[2]) then
			print("检测到大版本")

			return true
		end
	end
end

function assetUpter:getDownloadingFilenames()
	return self.assetMgr:getDownloadingFilenames()
end

function assetUpter:getDiffFileTotalSize()
	local manifest = self.assetMgr:getRemoteManifest()
	local downloading = self.assetMgr:getDownloadingFilenames()

	for k, v in ipairs(downloading) do
		if not self.downloadingFilesSize[v] then
			local sz = manifest:getAssetSize(v)

			self.downloadingFilesSize[v] = sz
		end

		self.downloadFileCnt = self.downloadFileCnt + 1
	end

	local totalSize = 0

	for k, v in pairs(self.downloadingFilesSize) do
		totalSize = totalSize + v
	end

	return totalSize
end

function assetUpter:getDownloadSize()
	return self.assetMgr:getDownloadSize()
end

assetUpter.EventCode = {
	UPDATE_PROGRESSION = 5,
	ERROR_NO_LOCAL_MANIFEST = 0,
	ERROR_PARSE_MANIFEST = 2,
	UPDATE_FAILED = 9,
	UPDATE_FINISHED = 8,
	ERROR_DECOMPRESS = 10,
	ERROR_DOWNLOAD_MANIFEST = 1,
	ERROR_UPDATING = 7,
	ALREADY_UP_TO_DATE = 4,
	ASSET_UPDATED = 6,
	NEW_VERSION_FOUND = 3
}
assetUpter.AssetsManagerExStatic = {
	VERSION_ID = "@version",
	MANIFEST_ID = "@manifest"
}

function assetUpter:onUpdateEvent(event)
	local eventCode = event

	if type(event) == "table" or type(event) == "userdata" then
		eventCode = event:getEventCode()
	else
		print(event)
	end

	local ok = false

	for k, v in pairs(assetUpter.EventCode) do
		if v == eventCode then
			print(k)

			break
		end
	end

	if eventCode == assetUpter.EventCode.ERROR_NO_LOCAL_MANIFEST then
		print("No local manifest file found. state: faild")
		self.listener:onAssetError(eventCode, event:getMessage())
	elseif eventCode == assetUpter.EventCode.ERROR_DOWNLOAD_MANIFEST or eventCode == assetUpter.EventCode.ERROR_PARSE_MANIFEST then
		print("Fail to download manifest file. state: faild, errorCode:" .. eventCode)
		self.listener:onAssetError(eventCode, event:getMessage(), event:getCURLECode(), event:getCURLMCode())
	elseif eventCode == assetUpter.EventCode.ERROR_DECOMPRESS then
		self.listener:onAssetError(eventCode, event:getMessage())
	elseif eventCode == assetUpter.EventCode.UPDATE_PROGRESSION or eventCode == assetUpter.EventCode.ASSET_UPDATED then
		local assetId = event:getAssetId()
		local percent = event:getPercent()
		local percentByFile = event:getPercentByFile()

		if assetId == assetUpter.AssetsManagerExStatic.VERSION_ID then
			print("Version file: %d%%", percent)
		elseif assetId == assetUpter.AssetsManagerExStatic.MANIFEST_ID then
			print(string.format("Manifest file: %d%%", percent))
		end

		self.listener:onAssetUpdating(eventCode, assetId, percent)
	elseif eventCode == assetUpter.EventCode.ALREADY_UP_TO_DATE or eventCode == assetUpter.EventCode.UPDATE_FINISHED then
		print("Update finished.", event:getAssetId(), eventCode)
		self.listener:onAssetSuccess(eventCode, self.errAssetCnt)
	elseif eventCode == assetUpter.EventCode.ERROR_UPDATING or eventCode == assetUpter.EventCode.UPDATE_FAILED then
		print("Asset ", event:getAssetId(), ", ", event:getMessage(), event:getCURLECode(), event:getCURLMCode())

		self.errAssetCnt = self.errAssetCnt + 1

		local retryTimes = self.downloadFileCnt

		if retryTimes <= 0 then
			retryTimes = assetUpter.maxRetryTimes
		end

		if retryTimes > self.errAssetCnt then
			self:retryUptFaildAssets()
			self.listener:onUpdatingError(eventCode, event:getMessage())
		else
			self.listener:onAssetError(eventCode, event:getMessage())
		end
	elseif eventCode == assetUpter.EventCode.NEW_VERSION_FOUND then
		if not self.downloading then
			self.listener:onAssetNewVersion(true)
		end

		self.hasNewVersion = true
	else
		print("unknow event:", eventCode)
	end
end

if DEBUG > 0 then
	assetUpter.salt = "yMnNhbHRkJTNha2Zq"

	function assetUpter:setCachePath(storagePath, key)
		ycFunction:mkdir(storagePath)

		self.cachePath = storagePath .. crypto.encodeBase64("uptAssetUpter" .. key)
	end

	function assetUpter:saveRemoteAddress(addr)
		local cache = crypto.encodeBase64(addr)
		local cache = crypto.encodeBase64(assetUpter.salt .. cache)
		local cache = crypto.encodeBase64(cache)

		io.writefile(self.cachePath, cache)
	end

	function assetUpter:getFileServerAddr()
		local data = io.readfile(self.cachePath)

		if data then
			local cache = crypto.decodeBase64(data)
			local cache = crypto.decodeBase64(cache)
			local addr = string.sub(cache, string.len(assetUpter.salt) + 1)

			addr = crypto.decodeBase64(addr)

			return addr
		end
	end

	function assetUpter:updateRemoteUrl()
		local addr = self:getFileServerAddr()

		if addr then
			self:setRemoteAddress(addr)
		end
	end
end

function assetUpter:setRemoteAddress(addr)
	print("setRemoteAddress", addr)
	self.assetMgr:getLocalManifest():setRemoteAddress(addr)
end

function assetUpter:setRemoteAddressWithKey(serverKey)
	print("setRemoteAddressWithKey", serverKey)

	local addr = self.assetMgr:getLocalManifest():getVersionFileUrl()

	addr = string.gsub(addr, "version.manifest", serverKey .. "/")

	print("setRemoteAddressWithKey", addr)
	self.assetMgr:getLocalManifest():setRemoteAddress(addr)

	if DEBUG > 0 then
		addr = self.assetMgr:getLocalManifest():getVersionFileUrl()

		print("getVersionFileUrl", addr)

		addr = self.assetMgr:getLocalManifest():getManifestFileUrl()

		print("getManifestFileUrl", addr)

		addr = self.assetMgr:getLocalManifest():getPackageUrl()

		print("getPackageUrl", addr)
	end
end

function assetUpter:setRemoteAddressWithServerUrl(url)
	print("assetUpter:setRemoteAddressWithServerUrl url=", url)

	local versionServerUrl = self.assetMgr:getLocalManifest():getVersionFileUrl()

	print("assetUpter:setRemoteAddressWithServerUrl versionServerUrl=", versionServerUrl)

	local serverUrl = string.gsub(versionServerUrl, "version.manifest", "")

	print("assetUpter:setRemoteAddressWithServerUrl serverUrl=", serverUrl)

	local function genNewUptUrlWithServerUrl(oldUrl)
		local splitUrl = oldUrl
		local splitStr = "://"
		local s_start, s_end = string.find(splitUrl, splitStr)

		splitUrl = string.sub(splitUrl, s_end + 1)
		splitStr = "/"
		s_start, s_end = string.find(splitUrl, splitStr)

		local newUrl
		local endUrl = ""

		if s_start ~= nil then
			endUrl = string.sub(splitUrl, s_start)
			newUrl = url .. endUrl
		end

		return newUrl
	end

	local newUrl = genNewUptUrlWithServerUrl(serverUrl)

	if newUrl == nil then
		print("assetUpter:setRemoteAddressWithServerUrl set url failed，serverUrl invalid! serverUrl = ", serverUrl)

		return
	end

	print("assetUpter:setRemoteAddressWithServerUrl newUrl=", newUrl)
	self.assetMgr:getLocalManifest():setRemoteAddress(newUrl)

	if DEBUG > 0 then
		addr = self.assetMgr:getLocalManifest():getVersionFileUrl()

		print("getVersionFileUrl", addr)

		addr = self.assetMgr:getLocalManifest():getManifestFileUrl()

		print("getManifestFileUrl", addr)

		addr = self.assetMgr:getLocalManifest():getPackageUrl()

		print("getPackageUrl", addr)
	end
end

function assetUpter:setListener(l)
	self.listener = l
end

return assetUpter
