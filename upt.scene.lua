﻿-- chunkname: @upt\\scene.lua

local lfs = require("lfs")
local def = import(".def")
local assetUpter = import(".assetUpter")
local msgbox = import(".msgbox")
local scene = class("upt", function()
	return display.newScene("upt")
end)
local socket = require("socket")
local scheduler = require("framework.scheduler")

table.merge(scene, {
	endFunc,
	skipFunc,
	newVerConfig,
	tasks,
	curBytes,
	allBytes,
	errCount,
	layer,
	bar,
	text,
	newVer,
	hasNewVersion = false
})

scene.storagePath = device.writablePath
scene.packageManifest = "project.manifest"

function scene:ctor(endFunc, force, skipFunc)
	g_data.login:setLoginState(GameStateType.upt)
	self:checkResourceVersion()

	self.force = force
	self.skipFunc = skipFunc or endFunc

	local n = display.newColorLayer(cc.c4b(255, 255, 255, 255)):size(display.width, display.height):addTo(self)

	n:setLocalZOrder(99999999)
	n:setTouchSwallowEnabled(true)
	n:setTouchEnabled(true)
	n:setCascadeOpacityEnabled(true)
	n:addNodeEventListener(cc.NODE_TOUCH_EVENT, function()
		return true
	end)

	local function copyResCallback()
		n:removeAllNodeEventListeners()
		n:removeSelf()

		if device.platform == "android" and self.copyRes then
			print("安卓拷资源出来")
		end
	end

	local function uptAnimCallback()
		local bg = self.bg

		display.newSprite("public/smoke/0_00000.png"):addTo(bg):pos(bg:getContentSize().width / 2 - 325, 335)
		display.newSprite("public/hero/0_00000.png"):addTo(bg):pos(bg:getContentSize().width / 2 + 89, 270):scale(0.88)
		display.newSprite("public/patricle/0_00000.png"):addTo(bg):pos(bg:getContentSize().width / 2 + 224, 237):setBlendFunc(gl.ONE, gl.ONE_MINUS_SRC_COLOR)
		display.newSprite("public/fire/0_00000.png"):addTo(bg):pos(bg:getContentSize().width / 2 + 15, 70):setBlendFunc(gl.ONE, gl.ONE_MINUS_SRC_COLOR)
	end

	local function endFuncCallback()
		scheduler.performWithDelayGlobal(self.skipFunc, 0)
	end

	local function uptCallback()
		scheduler.performWithDelayGlobal(function()
			if SKIP_UPT and not self.force then
				print("upt/scene: 由于配置跳过了更新 ")
				n:stopAllActions()
				endFuncCallback()
			else
				n:runAction(cca.seq({
					cca.callFunc(uptAnimCallback),
					cca.callFunc(copyResCallback)
				}))
			end
		end, 0)
	end

	local function initAssetUpter()
		self.endFunc = endFunc
		self.assetUpter = assetUpter.new(scene.packageManifest, scene.storagePath)

		self.assetUpter:setListener(self)

		if DEBUG > 0 then
			self.assetUpter:setCachePath(scene.storagePath, "globalUpdate")
			self.assetUpter:updateRemoteUrl()
		end
	end

	initAssetUpter()

	if ALWAYS_PLAY_LOGO then
		print("播放下logo")
		bg1:runAction(cca.seq({
			cca.fadeIn(1),
			cca.delay(1),
			cca.fadeOut(1),
			cca.callFunc(function()
				return
			end)
		}))
	else
		uptCallback()
	end

	if SKIP_UPT and not self.force then
		return
	end

	self.layer = display.newNode():addTo(self)

	local bg = display.newSprite("public/uptbg.png"):addTo(self.layer):center()

	self.bg = bg

	display.newSprite("public/p1.png", bg:getContentSize().width / 2, 64):addTo(bg):setLocalZOrder(1)

	self.bar = display.newSprite("public/progress/0_00000.png", bg:getContentSize().width / 2 - 319, 64):addTo(bg)

	self.bar:setLocalZOrder(1)
	self.bar:setAnchorPoint(cc.p(0, 0.5))

	local curVer = display.newTTFLabel({
		y = 630,
		size = 20,
		x = 5,
		text = "当前版本: " .. self.assetUpter:getCurVersion(),
		color = cc.c3b(222, 222, 150)
	}):addTo(self.layer)

	curVer:setAnchorPoint(cc.p(0, 1))

	self.curVer = curVer
	self.newVer = display.newTTFLabel({
		text = "",
		y = 600,
		size = 20,
		x = 5,
		color = cc.c3b(222, 222, 150)
	}):addTo(self.layer)

	self.newVer:setAnchorPoint(cc.p(0, 1))

	self.text = display.newTTFLabel({
		text = "",
		y = 120,
		size = 20,
		color = cc.c3b(222, 222, 150),
		x = display.cx
	}):addTo(self.layer)
	self.debugText = display.newTTFLabel({
		text = "",
		y = 200,
		size = 22,
		color = cc.c3b(222, 222, 150),
		x = display.cx
	}):addTo(self.layer)
	self.err = display.newTTFLabel({
		text = "",
		y = 570,
		size = 20,
		x = 5,
		color = cc.c3b(222, 222, 150)
	}):addTo(self.layer)
	self.label = display.newTTFLabel({
		text = "如遇到重复下载，建议预留5GB或以上的存储空间",
		y = 30,
		size = 20,
		color = cc.c3b(222, 222, 150),
		x = display.cx
	}):addTo(self.layer)

	self.err:setAnchorPoint(cc.p(0, 1))
end

function scene:saveRemoteAddress(addr)
	self.assetUpter:saveRemoteAddress(addr)
end

function scene:setRemoteAddressWithKey(serverKey)
	self.assetUpter:setRemoteAddressWithKey(serverKey)
end

function scene:setRemoteAddressWithServerUrl(serverUrl)
	self.assetUpter:setRemoteAddressWithServerUrl(serverUrl)
end

function scene:playAni(parent, pattern, frame, delay, blend, isProg)
	if not parent or not pattern then
		return
	end

	local texs = {}
	local textureCache = cc.Director:getInstance():getTextureCache()

	for i = 1, frame do
		local index = i

		textureCache:addImageAsync(string.format(pattern .. "0_%05d.png", i - 1), function(tex)
			if tex then
				texs[index] = tex
			end
		end)
	end

	local texIdx = 1
	local sprite = display.newSprite(string.format(pattern .. "0_%05d.png", 1)):addTo(parent)

	local function uptBlendFunc()
		if blend then
			sprite:setBlendFunc(gl.ONE, gl.ONE_MINUS_SRC_COLOR)
		end
	end

	uptBlendFunc()
	sprite:addNodeEventListener(cc.NODE_ENTER_FRAME_EVENT, function(dt)
		if sprite.lasttime then
			local nowtime = socket.gettime()

			if nowtime - sprite.lasttime >= (delay or 0.3) then
				sprite.lasttime = nowtime
				texIdx = texIdx + 1
				texIdx = texIdx > frame and 1 or texIdx

				if texs[texIdx] then
					sprite:setTexture(texs[texIdx])
				end

				uptBlendFunc()
			end
		else
			sprite.lasttime = socket.gettime()
		end
	end)
	sprite:scheduleUpdate()

	return sprite
end

function scene:onAssetError(code, msg, curle, curlm)
	if DEBUG > 0 then
		local errorStr = code .. "--" .. (msg or "") .. "--" .. (curle or "") .. "--" .. (curlm or "")

		self:setTitle(errorStr)
	end

	local errMsg = ""

	if code == assetUpter.EventCode.ERROR_NO_LOCAL_MANIFEST then
		self:setTitle("获取本地资源信息失败")

		errMsg = "ERROR_NO_LOCAL_MANIFEST"
	elseif code == assetUpter.EventCode.ERROR_DOWNLOAD_MANIFEST or code == assetUpter.EventCode.ERROR_PARSE_MANIFEST then
		local box
		local str = "获取版本信息失败, 请检查网络连接, 是否重试? "

		if DEBUG > 0 then
			str = str .. "\n(当前为调试版本,取消可跳过更新.)"
		end

		box = msgbox.new(str, function(isRetry)
			if isRetry then
				self.assetUpter:reset(isRetry)
				self.assetUpter:checkUpt()
				box:removeSelf()

				errMsg = "MANIFEST_ERROR, retry update now"
			elseif DEBUG > 0 then
				self.skipFunc()
			else
				errMsg = "MANIFEST_ERROR, exit"

				os.exit(0)
			end
		end)

		return
	elseif code == assetUpter.EventCode.ERROR_UPDATING or code == assetUpter.EventCode.UPDATE_FAILED then
		local box

		box = msgbox.new("网络状态不佳或远程服务器繁忙, 是否重试? ", function(isRetry)
			if isRetry then
				self.assetUpter:reset(isRetry)
				self.assetUpter:checkUpt()
				box:removeSelf()

				errMsg = "DOWNLOAD_RES_FAILED, retry update now"
			else
				errMsg = "DOWNLOAD_RES_FAILED, exit"

				os.exit(0)
			end
		end)

		return
	else
		msgbox.new(string.format("网络异常 error :%s. %d,%d", msg or "update faild", curle or -1, curlm or -1), function(b)
			errMsg = "unknown update error"

			os.exit(0)
		end)
	end

	local logMsg = "ver_new: " .. self.assetUpter:getRemoteVersion()

	luaReportException("update failed", errMsg, logMsg)
end

function scene:onAssetUpdating(eventCode, assetId, percent)
	if assetId == assetUpter.AssetsManagerExStatic.VERSION_ID then
		self:setTitle(string.format("获取最新版本信息... %d%%", 100 - percent))
	elseif assetId == assetUpter.AssetsManagerExStatic.MANIFEST_ID then
		self:setTitle(string.format("获取本地版本信息... %d%%", 100 - percent))
		self:setProgress(100 - percent)
	else
		self.allBytes = self.assetUpter:getDiffFileTotalSize() or 0

		local downloaded = self.assetUpter:getDownloadSize()

		self:setTitle(string.format("下载更新资源中... %s / %s", self:fileSizeFormat(downloaded), self:fileSizeFormat(self.allBytes or 0)))
		self:setProgress(downloaded / self.allBytes * 100)
	end
end

function scene:onUpdatingError(eventCode, msg)
	local downloaded = self.assetUpter:getDownloadSize()
	local str = string.format("更新异常,重试中...当前已更新: %s / %s", self:fileSizeFormat(downloaded), self:fileSizeFormat(self.allBytes or 0))

	if DEBUG > 0 then
		str = str .. " -eventCode: " .. eventCode or " " .. " -eventMsg: " .. msg or " "
	end

	self:setTitle(str)
end

function scene:onAssetSuccess(eventCode, errCnt)
	self:setProgress(100)

	if assetUpter.EventCode.UPDATE_FINISHED == eventCode then
		if self.assetUpter.hasNewVersion then
			self:setTitle(string.format("更新 %s / %s", self:fileSizeFormat(self.allBytes or 0), self:fileSizeFormat(self.allBytes or 0)))
			self:downloadEnd()

			MIR2_UPT_VERSION = self.assetUpter:getRemoteVersion()

			if self.endFunc then
				self.endFunc()
				print("scene:onAssetSuccess: 更新成功了重启lua")
			else
				self.skipCheck = true

				print("scene:onAssetSuccess: 更新成功了但是没有endfunc")
			end
		else
			print("scene:onAssetSuccess: 更新完成但其实并没有新版本")
		end
	elseif assetUpter.EventCode.ALREADY_UP_TO_DATE == eventCode then
		self.skipFunc()
		print("scene:onAssetSuccess: 已经是最新版本直接进游戏")
	else
		print("scene:onAssetSuccess: 更新未完成")
	end
end

function scene:getLoginSdk()
	local curLoginSdk = 1
	local pu_ret, pu = pcall(function()
		return PlatformUtils:getInstance()
	end)

	if pu_ret and pu ~= nil then
		pu_ret, curLoginSdk = pcall(function()
			return pu:getLoginSdk()
		end)

		if pu_ret == false then
			curLoginSdk = 1
		end
	end

	return curLoginSdk
end

local function compareVersion(ver1, ver2)
	verArr1 = string.split(ver1, ".")
	verArr2 = string.split(ver2, ".")

	local flag

	flag = tonumber(verArr1[1]) > tonumber(verArr2[1]) and 1 or tonumber(verArr1[1]) < tonumber(verArr2[1]) and -1 or tonumber(verArr1[2]) > tonumber(verArr2[2]) and 1 or tonumber(verArr1[2]) < tonumber(verArr2[2]) and -1 or tonumber(verArr1[3]) > tonumber(verArr2[3]) and 1 or tonumber(verArr1[3]) < tonumber(verArr2[3]) and -1 or tonumber(verArr1[4]) > tonumber(verArr2[4]) and 1 or tonumber(verArr1[4]) < tonumber(verArr2[4]) and -1 or 0

	return flag
end

function scene:onAssetNewVersion(newVersionFound)
	local baseVersion = MIR2_VERSION_BASE
	local newVersion = self.assetUpter:getRemoteVersion()
	local flagbase = compareVersion(newVersion, baseVersion)
	local flagcur = compareVersion(MIR2_VERSION, baseVersion)

	if newVersionFound and flagbase < 0 then
		newVersionFound = false

		print("base ver is larger than server ver!")
	end

	self.hasNewVersion = newVersionFound

	local box

	if newVersionFound then
		local str = "有新版本,是否更新?"
		local nativeNew = self.assetUpter:checkNative()

		if nativeNew then
			str = "(*)存在大版本更新，请重新安装最新版本客户端."
		end

		if DEBUG > 0 then
			str = str .. "\n(当前为调试版本,取消可跳过更新.)"
		end

		box = msgbox.new(str, function(isOk)
			if isOk then
				if nativeNew then
					if self:getLoginSdk() == 1 and device.platform == "ios" then
						device.openURL("https://itunes.apple.com/cn/app/id1347643792?mt=8")
						os.exit(0)

						return
					else
						os.exit(0)

						return
					end
				end

				self.assetUpter:startUpt()

				self.allBytes = self.assetUpter:getDiffFileTotalSize() or 0

				self:setTitle("更新 0 / " .. self:fileSizeFormat(self.allBytes))
			elseif DEBUG > 0 then
				self.skipFunc()
			else
				os.exit(0)
			end

			box:removeSelf()
		end)

		self.newVer:setString("最新版本: " .. self.assetUpter:getRemoteVersion())
	else
		self.skipFunc()
	end
end

function scene:onCleanup()
	if self.assetUpter then
		self.assetUpter:destroy()
	end
end

function scene:checkUpt()
	self:setProgress(0)
	self:setTitle("获取版本信息...")
	self.assetUpter:checkUpt()
end

function scene:onEnter()
	if not io.exists(device.writablePath .. "res/") then
		ycFunction:mkdir(device.writablePath .. "res/")
	end

	if SKIP_UPT and not self.force then
		return
	end

	self:checkUpt()
end

function scene:onExit()
	return
end

function scene:removeOldRes()
	print("login", "################## REMOVE OLE RESOURCE ##################")
	self:rmdir(scene.storagePath)
	cc.FileUtils:getInstance():purgeCachedEntries()

	MIR2_VERSION = MIR2_VERSION_BASE
end

function scene:checkResourceVersion()
	local function getManifestVersion(manifestPath)
		local str = io.readfile(cc.FileUtils:getInstance():fullPathForFilename(manifestPath))
		local data = json.decode(str)

		return data and data.version
	end

	MIR2_VERSION_BASE = getManifestVersion("res/project.manifest")

	local versionPath = scene.storagePath .. "project.manifest"

	if cc.FileUtils:getInstance():isFileExist(versionPath) then
		MIR2_VERSION = getManifestVersion(versionPath)

		print("login", "Current Version:", MIR2_VERSION, "Base Version:", MIR2_VERSION_BASE, versionPath)
		self:checkRemoveOldRes()
	else
		MIR2_VERSION = MIR2_VERSION_BASE
	end
end

function scene:checkRemoveOldRes()
	local base = string.split(MIR2_VERSION_BASE, ".")
	local cur = string.split(MIR2_VERSION, ".")

	if #base ~= 4 or #cur ~= 4 then
		return
	end

	for i = 1, 4 do
		local b = tonumber(base[i])
		local c = tonumber(cur[i])

		if b and c then
			if c < b then
				return self:removeOldRes()
			elseif b < c then
				return
			end
		end
	end
end

function scene:setProgress(progress)
	progress = math.min(math.max(progress, 0), 100)
	progress = progress / 100

	self.bar:setTextureRect(cc.rect(0, 0, progress * self.bar:getTexture():getContentSize().width, self.bar:getContentSize().height))
end

function scene:setTitle(text)
	self.text:setString(text)
end

function scene:setDebugTitle(text)
	self.debugText:setString(text)
end

function scene:downloadEnd()
	MIR2_VERSION = self.assetUpter:getCurVersion()
end

function scene:rmdir(path)
	print("rmdir - ", path)

	if io.exists(path) then
		local function _rmdir(path)
			local iter, dir_obj = lfs.dir(path)

			while true do
				local dir = iter(dir_obj)

				if dir == nil then
					break
				end

				xpcall(function()
					if dir ~= "." and dir ~= ".." and dir ~= "" then
						local curDir = path .. dir
						local mode = lfs.attributes(curDir, "mode")

						print(mode, curDir)

						if mode == "directory" then
							_rmdir(curDir .. "/")
						elseif mode == "file" and curDir ~= "" then
							os.remove(curDir)
						end
					end
				end, function(err)
					print("err", err)
				end)
			end

			local succ, des = os.remove(path)

			if des then
				print(des)
			end

			return succ
		end

		_rmdir(path)
	end

	return true
end

function scene:fileSizeFormat(size)
	size = size or 0

	if size < 1048576 then
		return string.format("%.2f", size / 1024) .. "KB"
	end

	return string.format("%.2f", size / 1024 / 1024) .. "MB"
end

return scene
