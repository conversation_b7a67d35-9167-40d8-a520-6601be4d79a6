-- chunkname: @mir2\\scenes\\main\\role\\role.lua

local magic = import("..common.magic")
local mapDef = import("..map.def")
local ani = import(".ani")
local info = import(".info")
local role = class("role")
local __position = cc.Node.setPosition
local panguEffect = import("..console.btnCallbacks")
local controllerPanGu = import("..console.controller")
local autoRatPanGU = import("..console.autoRat")

table.merge(role, {
	isUnderOtherRole,
	notifyYinni = false,
	unrealBannerEffect = 0,
	actFastNum = 2,
	trailingLeft = false,
	trailingT = 0,
	trailingEffect = 0,
	playingOtherEffect = false,
	shieldRing = 0,
	bodyEffect = {},
	trailingOffeset = {}
})

function role:ctor(params)
	params = params or {}
	self._auraNode = display.newNode():addTo(main_scene.ground.map.layers.obj)

	self._auraNode:setVisible(false)

	self.node = display.newNode()

	function self.node.onEnter(node)
		self:onEnter()
	end

	function self.node.onExit()
		if self._auraNode then
			self._auraNode:setVisible(false)
		end
	end

	if params.roleParams and params.roleParams.flag and params.roleParams.flag >= 0 and params.roleParams.flag <= 2 then
		self.die = true
	end

	self.map = params.map
	self.isPlayer = params.isPlayer
	self.roleid = params.roleid
	self.x, self.y = params.x or 0, params.y or 0
	self.dir = params.dir or def.role.dir.bottom
	self.feature = params.feature
	self.state = params.state or {}
	self.level = params.level or 1
	self.hitSpeed = avoidPlugValue(0)
	self.shield = nil
	self.skyStarShield = nil
	self.panGuFoot = nil
	self.bladeshadow = nil
	self.trainMon = nil
	self.mjdtBladeshadow = nil
	self.sounds = {}
	self.filters = {}
	self.acts = {}
	self.parts = {}
	self.sprites = {}
	self.cur = {}
	self.last = {
		x = self.x,
		y = self.y,
		dir = self.dir,
		state = self.state,
		pos = cc.p(-1, -1)
	}
	self.lock = {
		execute = false
	}
	self.waits = {}
	self.actions = nil

	self.node:setCascadeOpacityEnabled(true)

	local size = def.role.size

	self.node:size(size.w, size.h)
	self._auraNode:size(size.w, size.h)

	self.info = info.new(self, self.map)

	self.node:setNodeEventEnabled(true)

	self.isIgnore = false
	self.miniMapHide = false
	self.lastSpeEffect = {}
	self.magicWeaponShowID = 0
	self.__magicWeaponEffect = nil

	self:firstEnter()
end

function role:firstEnter()
	if self.isPlayer and #self.acts <= 0 then
		if g_data.player.isDeath then
			self:addAct({
				loadMap = true,
				type = "die",
				corpse = true,
				x = self.x,
				y = self.y,
				dir = self.dir
			})
		else
			self:addAct({
				loadMap = true,
				type = "stand",
				dir = self.dir,
				x = self.x,
				y = self.y
			})
		end
	end
end

function role:initEnd()
	return
end

function role:onEnter()
	self:changeFeature(self.feature, true)

	if #self.acts <= 0 then
		self:addAct({
			loadMap = true,
			type = "stand",
			dir = self.dir,
			x = self.x,
			y = self.y
		})
	end

	self:uptInfoShow()
	self:uptSelfShow()
	self:uptFXShow()
end

function role:uptIsIgnore()
	local isIgnore = not self.isInScreen or self.isUnderOtherRole and not self.die and not self.isPlayer

	if isIgnore ~= self.isIgnore then
		self.isIgnore = isIgnore

		self:uptInfoShow()
		self:uptSelfShow()
	end
end

function role:setIsUnderOtherRole(b)
	if self.isUnderOtherRole ~= b then
		self.isUnderOtherRole = b
	end
end

function role:uptInfoShow()
	local yinniHide = false

	if self:isYinni() and self.roleid ~= g_data.player.roleid and not g_data.player:isYinniFriendly(self.roleid) then
		yinniHide = true
	end

	if self.noInfo or self.isIgnore or self.die or g_data.login:isChangeSkinCheckServer() or yinniHide then
		self.info:hide()
	else
		self.info:show()
	end
end

function role:uptSelfShow()
	local show = not self.isIgnore

	if show and self.die then
		show = not g_data.setting.base.hideCorpse
	end

	if self:isYinni() and self.roleid ~= g_data.player.roleid and not g_data.player:isYinniFriendly(self.roleid) then
		show = false
	end

	for k, v in pairs(self.sprites) do
		if checkExist(k, "godAspects") then
			-- block empty
		else
			v:setVisible(show)
		end

		if self.die and show then
			v:play({
				corpse = true,
				type = "die",
				dir = self.dir
			})
		end
	end

	if self._auraNode then
		self._auraNode:setVisible(show)
	end

	if self.node then
		self.node:setVisible(show)
	end
end

function role:uptSelfOpacity()
	local halfOpacity = 128
	local emptyOpacity = 255
	local state = self.last.state or {}

	for k, v in pairs(self.sprites) do
		if def.role.stateHas(state, "stHidden") then
			v.spr:setOpacity(halfOpacity)
		elseif self:isYinni() then
			if self.roleid == g_data.player.roleid then
				v.spr:setOpacity(halfOpacity)
			elseif g_data.player:isYinniFriendly(self.roleid) then
				v.spr:setOpacity(halfOpacity)
			end
		else
			v.spr:setOpacity(emptyOpacity)
		end
	end
end

function role:clearLock()
	if not main_scene then
		return
	end

	local lock = main_scene.ui.console.controller.lock

	if lock.target.skill == self.roleid then
		lock.target.skill = nil
	end

	if lock.target.select == self.roleid then
		lock.target.select = nil
	end

	if type(tonumber(lock.target.attack)) == "number" then
		if lock.target.attack == self.roleid then
			lock.target.attack = nil
		end
	elseif lock.target.attack == self then
		lock.target.attack = nil
	end
end

function role:getDis(other)
	local x, y = math.abs(self.x - other.x), math.abs(self.y - other.y)

	return math.sqrt(x * x + y * y)
end

function role:getRace()
	return self.feature.race
end

function role:getAppr()
	return self.feature.dress
end

function role:getWeapon()
	return self.feature.weapon
end

function role:openFilter(name)
	if not self.filters[name] then
		self.filters[name] = true
	end

	self:checkFilter()
end

function role:closeFilter(name)
	if self.filters[name] then
		self.filters[name] = nil
	end

	self:checkFilter()
end

function role:checkFilter()
	if table.nums(self.filters) == 0 then
		for k, v in pairs(self.sprites) do
			v.spr:clearFilter()
		end

		return
	end

	local f

	if self.filters.die or self.filters.gray then
		f = res.getFilter("gray")
	elseif self.filters.outline then
		f = res.getFilter("outline_role")
	end

	if f then
		for k, v in pairs(self.sprites) do
			v.spr:setFilter(f)
		end
	end
end

function role:getParts(feature)
	return {}, 0
end

function role:changeFeature(newFeature, force)
	local oldSpeEffect = self.lastSpeEffect or {}
	local newSpeEffect = newFeature.speEffect or {}

	self.magicWeaponShowID = newFeature.magicWeaponShowID or 0

	for oldKey, oldEffectId in ipairs(oldSpeEffect) do
		local bStillEffect = false

		for newKey, newEffectId in ipairs(newSpeEffect) do
			if newEffectId == oldEffectId then
				bStillEffect = true
			end
		end

		if not bStillEffect then
			force = true

			self:stopSpecialEffect(oldEffectId)
		end
	end

	if self.isPlayer then
		g_data.magicWeapon.showHlId = self.magicWeaponShowID
	end

	self:showMagicWeapon()

	for newKey, newEffectId in ipairs(newSpeEffect) do
		local bHasEffect = false

		for oldKey, oldEffectId in ipairs(oldSpeEffect) do
			if newEffectId == oldEffectId then
				bHasEffect = true
			end
		end

		if not bHasEffect then
			force = true

			self:playSpecialEffect(newEffectId)
		end
	end

	self.lastSpeEffect = newFeature.speEffect
	self.lastSpeEffect = newFeature.speEffect

	if newFeature.godAspects then
		self.feature.godAspects = self.feature.godAspects or 0
	end

	local diff = false

	for k, v in pairs(self.feature) do
		if type(v) == "number" and v ~= newFeature[k] then
			diff = true

			break
		end
	end

	if not diff and not force then
		return
	end

	local parts, sex = self:getParts(newFeature)

	for k, v in pairs(parts) do
		if not self.parts[k] or v.delete or self.parts[k].imgid ~= v.imgid or self.parts[k].id ~= v.id then
			v.type = k

			self:addAct(v)
		end

		if v.delete then
			parts[k] = nil
		end
	end

	self.parts = parts
	self.sex = sex
	self.feature = newFeature
end

function role:showMagicWeapon()
	local id = self.magicWeaponShowID

	if id > 0 and g_data.setting.base.magicWeapon then
		if self.node:getChildByName("MagicWeapon") then
			self.node:getChildByName("MagicWeapon"):removeSelf()
		end

		local dir = self.dir
		local posX = 0

		if dir == 0 or dir == 5 or dir == 6 or dir == 7 then
			posX = 60
		elseif dir == 1 or dir == 2 or dir == 3 or dir == 4 then
			posX = -10
		end

		ccs.ArmatureDataManager:getInstance():addArmatureFileInfo(string.format("animation/magicWeapon/fabao_0%d_ingame/fabao_0%d_ingame.csb", id, id))

		local animation = ccs.Armature:create(string.format("fabao_0%d_ingame", id))

		animation:add2(self.node):pos(posX, 80):setName("MagicWeapon")
		animation:getAnimation():play(string.format("fabao_0%d_ingame", id))
	elseif self.node:getChildByName("MagicWeapon") then
		self.node:getChildByName("MagicWeapon"):removeSelf()
	end
end

function role:changeMagicWeaponDir()
	local dir = self.dir
	local posX = 0

	if dir == 0 or dir == 5 or dir == 6 or dir == 7 then
		posX = 60
	elseif dir == 1 or dir == 2 or dir == 3 or dir == 4 then
		posX = -10
	end

	if self.node:getChildByName("MagicWeapon") then
		self.node:getChildByName("MagicWeapon"):pos(posX, 80)
	end
end

function role:updateSpriteForState(type, sprite)
	local function update(t, spr)
		local state = self.last.state or {}
		local hasBodyEffect, hasColor

		if t == "weaponbaseef" and self.sprites.weaponbaseef.act.imgid == "weapon22" then
			spr:setPosition(24, 48)
		end

		if def.role.stateHas(state, "stNoDie") then
			self:closeFilter("gray")
			self:playBodyEffect("stNoDie")

			hasBodyEffect = true
		elseif def.role.stateHas(state, "stAntiDebuff") then
			self:closeFilter("gray")
			self:playBodyEffect("stAntiDebuff")

			hasBodyEffect = true
		elseif def.role.stateHas(state, "stReviveSate") then
			self:closeFilter("gray")
			self:playBodyEffect("stReviveSate")

			hasBodyEffect = true
		elseif def.role.stateHas(state, "stDefenceAC") then
			self:closeFilter("gray")
			self:playBodyEffect("stDefenceAC")

			hasBodyEffect = true
		elseif def.role.isRoleStone(state) then
			self:openFilter("gray")
		elseif def.role.stateHas(state, "stEntangled") then
			self:playBodyEffect("stEntangled")

			hasBodyEffect = true
		elseif def.role.stateHas(state, "stAttSky") then
			self:playBodyEffect("stAttSky")

			hasBodyEffect = true
		elseif def.role.stateHas(state, "stComAttSky") then
			self:playBodyEffect("stComAttSky")

			hasBodyEffect = true
		elseif def.role.stateHas(state, "stPoisonBlue") then
			self:closeFilter("gray")

			if t == "dress" or t == "hair" then
				spr:setColor(cc.c3b(0, 255, 255))

				hasColor = true
			end
		elseif def.role.stateHas(state, "stGod") then
			self:playBodyEffect("stGod")

			hasBodyEffect = true
		elseif def.role.stateHas(state, "stSpeed") then
			self:playBodyEffect("stSpeed")

			hasBodyEffect = true
		elseif def.role.stateHas(state, "stBGZ_Shield") then
			self:playBodyEffect("stBGZ_Shield")

			hasBodyEffect = true
		elseif def.role.stateHas(state, "stSkillComboing") then
			self:playBodyEffect("stSkillComboing")

			hasBodyEffect = true
		elseif def.role.stateHas(state, "stSeaWarBossDieBuff_CXZL") then
			self:playBodyEffect("stSeaWarBossDieBuff_CXZL")

			hasBodyEffect = true
		elseif def.role.stateHas(state, "stGod_TGZF") then
			self:playBodyEffect("stGod_TGZF")

			hasBodyEffect = true
		elseif def.role.stateHas(state, "stRecover_QJK") then
			self:playBodyEffect("stRecover_QJK")

			hasBodyEffect = true
		elseif def.role.stateHas(state, "stBuff_BSHF") then
			self:playBodyEffect("stBuff_BSHF")

			hasBodyEffect = true
		elseif def.role.stateHas(state, "stZJEffect") then
			self:playBodyEffect("stZJEffect")

			hasBodyEffect = true
		elseif def.role.stateHas(state, "stReviveExtSate") then
			self:playBodyEffect("stReviveExtSate")

			hasBodyEffect = true
		elseif def.role.stateHas(state, "stJLExtSate") then
			-- block empty
		elseif def.role.stateHas(state, "stPAL_BMYZ_MainUP") then
			self:playBodyEffect("stPAL_BMYZ_MainUP")

			hasBodyEffect = true
		else
			self:closeFilter("gray")

			if def.role.stateHas(state, "stPoisonFuchsia") or def.role.stateHas(state, "stPosion_FireBoneDemon") then
				spr:setColor(cc.c3b(255, 60, 255))

				hasColor = true
			end

			if def.role.stateHas(state, "stPoisonRed") or def.role.stateHas(state, "stPoisonRed4th") or def.role.stateHas(state, "stPoisonRed5th") or def.role.stateHas(state, "stPoisonRed6th") then
				if t == "dress" or t == "hair" then
					spr:setColor(display.COLOR_RED)

					hasColor = true
				end
			elseif (def.role.stateHas(state, "stPoisonGreen") or def.role.stateHas(state, "stPoisonGreen4th") or def.role.stateHas(state, "stPoisonGreen5th") or def.role.stateHas(state, "stPoisonGreen6th") or def.role.stateHas(state, "stPoison_OrcShaman") or def.role.stateHas(state, "stPoison_PlagueLord") or def.role.stateHas(state, "stPosion_WildSorcer")) and (t == "dress" or t == "hair") then
				spr:setColor(display.COLOR_GREEN)

				hasColor = true
			end
		end

		if not hasBodyEffect then
			self:removeBodyEffect()
		end

		if not hasColor then
			spr:setColor(display.COLOR_WHITE)
		end

		if not def.role.isRoleStone(state) then
			self:closeFilter("gray")
		end

		self:uptInfoShow()
		self:uptSelfShow()
		self:uptSelfOpacity()

		local miniMapHide = false

		if self:isYinni() then
			if self.roleid == g_data.player.roleid then
				self:addStandAct()
			elseif not g_data.player:isYinniFriendly(self.roleid) then
				miniMapHide = true
			end
		end

		if miniMapHide ~= self.miniMapHide then
			self.miniMapHide = miniMapHide

			g_data.eventDispatcher:dispatch("M_YINNI_STATE_CHG", self.roleid, self.miniMapHide)
		end

		if def.role.stateHas(state, "stGodRing_PJ") then
			self.info:showPoJun(true)
		else
			self.info:showPoJun(false)
		end

		if def.role.stateHas(state, "stMagicShield") then
			if not self.shield then
				self.shield = m2spr.playAnimation("magic", 3890, 2, 0.15, true):add2(self.node, 2)

				__position(self.shield, 0, mapDef.tile.h)
			end

			self.shield:show()

			local hasShiledEx = def.role.stateHas(state, "stMagicShieldEx")

			if hasShiledEx then
				if not self.shieldEx then
					self.shieldEx = m2spr.playAnimation("magic6", 730, 2, 0.15, true):add2(self.node, 2)

					__position(self.shieldEx, 0, mapDef.tile.h)
				end

				self.shieldEx:show()
			end
		else
			if self.shield then
				self.shield:hide()
			end

			if self.shieldEx then
				self.shieldEx:hide()
			end
		end

		if def.role.stateHas(state, "stSkyStarShieldth") or def.role.stateHas(state, "stSkyStarShield2th") or def.role.stateHas(state, "stSkyStarShield3th") then
			if not self.skyStarShield then
				self.skyStarShield = m2spr.playAnimation("horse5", 9088, 2, 0.15, true):add2(self.node, 2)

				__position(self.skyStarShield, 0, mapDef.tile.h)
			end

			self.skyStarShield:show()
		elseif self.skyStarShield then
			self.skyStarShield:hide()
		end

		if def.role.stateHas(state, "stGod_PanGu") then
			if self.node then
				self.node:setScale(1.7)
				controllerPanGu:panGu(1)
				panguEffect:panGu(1)
				autoRatPanGU:panGu(1)
			end
		elseif self.node then
			self.node:setScale(1)
			panguEffect:panGu(0)
			controllerPanGu:panGu(0)
			autoRatPanGU:panGu(0)
		end

		self:bindWeaponEffectByState("stFireBall", 40, 8)
		self:bindWeaponEffectByState("stDragonRecov", 20, 8)
		self:bindWeaponEffectByState("stRiseAndDown", 0, 8)
		self:bindWeaponEffectByState("stTrumpBleed")
		self:bindExEffectByState("magic7", "stManaSurge", 980, 25)

		if def.role.stateHas(state, "stskillbladeshadow") or def.role.stateHas(state, "stskillbladeshadow46") or def.role.stateHas(state, "stskillbladeshadow79") or def.role.stateHas(state, "stBarbarianKing_JRFB") then
			local skillid, skilllv = 68, 0

			if def.role.stateHas(state, "stskillbladeshadow46") then
				skilllv = 4
			end

			if def.role.stateHas(state, "stskillbladeshadow79") then
				skilllv = 7
			end

			local config = def.magic.getMagicConfigByUid(skillid, skilllv)

			if config.buffEffect and config.buffEffect[1] then
				local buffdata = config.buffEffect[1]

				if self.bladeshadow and skilllv > self.bladeshadow.skilllv then
					self.bladeshadow:removeSelf()

					self.bladeshadow = nil
				end

				if not self.bladeshadow then
					self.bladeshadow = m2spr.playAnimation(buffdata.rsc, buffdata.begin, buffdata.frame, 0.15, true):add2(self.node, 2)

					__position(self.bladeshadow, 0, mapDef.tile.h)

					self.bladeshadow.skilllv = skilllv
				end

				self.bladeshadow:show()
			end
		elseif self.bladeshadow then
			main_scene.ui.console:call("skill68", "durationEnd")
			self.bladeshadow:removeSelf()

			self.bladeshadow = nil
		end

		if def.role.stateHas(state, "stskillelfmondec") then
			if not self.trainMon then
				local intervals = 7

				self.trainMon = m2spr.new(nil, nil, {
					blend = true,
					setOffset = true
				}):add2(self.node, 2)

				__position(self.trainMon.spr, 0, mapDef.tile.h)

				if self.trainMon.spr then
					self.trainMon.spr:setOpacity(178.5)
				end

				self.trainMon.spr:runForever(transition.sequence({
					cc.CallFunc:create(function()
						self.trainMon:playAni("magic21", 0, 8, 0.15, true, false, true, function()
							self.trainMon:hide()
						end)
					end),
					cc.DelayTime:create(intervals),
					cc.CallFunc:create(function()
						self.trainMon:show()
					end)
				}))
			end
		elseif self.trainMon then
			self.trainMon:removeSelf()

			self.trainMon = nil
		end

		if def.role.stateHas(state, "stNoSkill") then
			if not self.stNoSkill then
				self.stNoSkill = m2spr.playAnimation("magic10", 1090, 8, 0.15, true):add2(self.node, 2)

				__position(self.stNoSkill, 0, mapDef.tile.h)
			end

			self.stNoSkill:show()

			if self.roleid == g_data.player.roleid then
				main_scene.ui.console:setAllSkillIconState("ban")
			end
		else
			if self.stNoSkill then
				self.stNoSkill:hide()
			end

			if self.roleid == g_data.player.roleid then
				main_scene.ui.console:setAllSkillIconState("normal")
			end
		end

		if def.role.stateHas(state, "stKillHit") then
			if not self.stKillHit then
				self.stKillHit = m2spr.playAnimation("magic7", 90, 10, 0.15, true):add2(self.node, 2)

				__position(self.stKillHit, 0, mapDef.tile.h)
			end

			self.stKillHit:show()
		elseif self.stKillHit then
			self.stKillHit:removeSelf()

			self.stKillHit = nil
		end

		if def.role.stateHas(state, "stDemon_WarlordBoss_JRFB") then
			if not self.mjdtBladeshadow then
				self.mjdtBladeshadow = m2spr.playAnimation("magic10", 1622, 4, 0.15, true):add2(self.node, 2)

				__position(self.mjdtBladeshadow, 0, mapDef.tile.h)
			end

			self.mjdtBladeshadow:show()
		elseif self.mjdtBladeshadow then
			self.mjdtBladeshadow:removeSelf()

			self.mjdtBladeshadow = nil
		end

		self:bindExEffectByState("magic21", "stBleed", 184, 8, function()
			self.info:showExLabel("stBleed")
		end)
		self:bindExEffectByState("magic21", "stTrigerLighting", 192, 8, function()
			self.info:showExLabel("stTrigerLighting")
		end)
		self:bindExEffectByState("magic21", "stGuardBreak", 200, 8, function()
			self.info:showExLabel("stGuardBreak")
		end)
		self:bindExEffectByState("magic21", "stBuff_JSWK", 0, 0, function()
			self.info:showExLabel("stBuff_JSWK")
		end)
		self:bindExEffectByState("magic21", "stBuff_XRFD", 0, 0, function()
			self.info:showExLabel("stBuff_XRFD")
		end)
		self:bindExEffectByState("magic21", "stBuff_LHLY", 0, 0, function()
			self.info:showExLabel("stBuff_LHLY")
		end)
		self:bindExEffectByState("magic21", "stBuff_HNBC", 0, 0, function()
			self.info:showExLabel("stBuff_HNBC")
		end)
		self:bindExEffectByState("magic21", "stBuff_BDRS", 0, 0, function()
			self.info:showExLabel("stBuff_BDRS")
		end)

		if self.sprites and self.sprites.godAspects then
			self.sprites.godAspects:setVisible(def.role.stateHas(state, "stBoInAspect"))
		end

		self.info:showEscorting(def.role.stateHas(state, "stCanGildMonster"))
	end

	if type and sprite then
		return update(type, sprite.spr)
	end

	for k, v in pairs(self.sprites) do
		update(k, v.spr)
	end
end

function role:bindExEffectByState(fileName, state, start, frame, func)
	if def.role.stateHas(self.last.state or {}, state) then
		if not self.node:getChildByName(state) then
			m2spr.playAnimation(fileName, start, frame, 0.1, true, false, false):addTo(self.node):pos(2, 22):setName(state)
		end

		if func then
			func()
		end
	elseif self.node:getChildByName(state) then
		self.node:removeChildByName(state)
	end
end

function role:bindWeaponEffectByState(state, start, frame)
	if def.role.stateHas(self.last.state or {}, state) then
		if self.node:getChildByName(state) then
			self.node:removeChildByName(state)
		end

		if state == "stTrumpBleed" then
			ccs.ArmatureDataManager:getInstance():addArmatureFileInfo("animation/magicWeapon/buff_liuxue/buff_liuxue.csb")

			local animation = ccs.Armature:create("buff_liuxue")

			animation:getAnimation():play("Animation2")

			self.__magicWeaponEffect = animation

			self.__magicWeaponEffect:addTo(self.node):pos(32, 22):setName(state)
		else
			self.__magicWeaponEffect = m2spr.playAnimation("magic_fabao", start, frame, 0.08, true, true, false)

			self.__magicWeaponEffect:addTo(self.node):pos(2, 22):setName(state)
		end
	elseif self.node:getChildByName(state) then
		self.node:removeChildByName(state)
	end
end

function role:selected()
	if not self.selectedSpr and not WIN32_OPERATE then
		local x, y = self.node:centerPos()

		self.selectedSpr = res.get2("pic/common/selectRole.png"):add2(self.node, -1):pos(x, 15)
	end
end

function role:unselected()
	if not tolua.isnull(self.selectedSpr) then
		self.selectedSpr:removeFromParent()

		self.selectedSpr = nil
	end
end

function role:highLight()
	for _, sprite in pairs(self.sprites) do
		sprite:setFilter(res.getFilter("high_light"))
	end
end

function role:unHighLight()
	for _, sprite in pairs(self.sprites) do
		sprite:clearFilter()
	end
end

function role:getSize()
	if self.parts.dress and self.parts.dress.ani then
		return self.parts.dress.ani:getContentSizeInPixels()
	end

	return self.node:getContentSize()
end

function role:isHeroForPlayer()
	return g_data.hero == self.roleid
end

function role:isLocked()
	if self.lock.execute and self.cur.act and self.cur.act.type == "struck" then
		return false
	end

	return self.lock.execute
end

function role:forceUnlock()
	self.lock.execute = false

	local acts = self.acts

	self.acts = {}

	self:executeEnd()

	for k, v in ipairs(acts) do
		if v.type == "state" then
			self.acts[#self.acts + 1] = v
		end
	end

	if self.isPlayer and #self.acts >= 1 then
		self:executeAct()
	end

	p2("error", "role:forceUnlock!!!!!")
end

g_data.speedUpOther = true

local rushTimeSpace = 2.73

function role:executeAct()
	self.lock.execute = true
	self.curActEnd = false
	self.cur.act = self.acts[1]

	local act = self.cur.act
	local checkExist = checkExist
	local lastPX, lastPY = self.last.x, self.last.y

	self.last.x = act.x or self.last.x
	self.last.y = act.y or self.last.y
	self.last.dir = act.dir or self.last.dir
	self.last.state = act.state or self.last.state

	if checkExist(act.type, "weapon", "weaponbaseef", "weaponef", "hair", "wing") and g_data.login:isChangeSkinCheckServer() then
		return self:executeEnd()
	end

	if act.type == "state" then
		self:updateSpriteForState()

		return self:executeEnd()
	end

	if checkExist(act.type, "dress", "weapon", "weaponbaseef", "weaponef", "hair", "humEffect", "monEffect", "godAspects", "wing") then
		if self.sprites[act.type] then
			self.sprites[act.type]:removeSelf()

			self.sprites[act.type] = nil
		end

		if not act.delete then
			local z = checkExist(act.type, "hair", "humEffect") and 1 or 0
			local spr = ani.new(act, self, act.delay and tonumber(act.delay) or nil):addto(self.node, z):pos(0, mapDef.tile.h)

			self.sprites[act.type] = spr

			self:updateSpriteForState(act.type, self.sprites[act.type])

			if self.isIgnore then
				self.sprites[act.type]:hide()
			end

			if act.spriteScale then
				self.sprites[act.type].spr:setScale(act.spriteScale)
			end

			if act.spritePosX then
				self.sprites[act.type].spr:setPositionX(act.spritePosX)
			elseif spritePosY then
				self.sprites[act.type].spr:setPositionY(act.spritePosY)
			end

			if act.effectX then
				self.sprites[act.type].spr:setPositionX(act.effectX)
			end

			if act.effectY then
				self.sprites[act.type].spr:setPositionY(act.effectY)
			end
		end

		self:executeSound()

		return self:executeEnd()
	end

	if act.type == "aura" then
		if self.showAura then
			self:showAura(act)
		end

		return self:executeEnd()
	end

	local delay
	local speed = def.role.speed
	local speedUpOther = g_data.speedUpOther

	if speedUpOther and self.__cname == "hero" and not self.isPlayer then
		speed = def.role.speedOtherPlayer
	end

	if self:isExecuteFast() then
		delay = speed.fast

		if DEBUG > 0 then
			-- block empty
		end
	elseif checkExist(act.type, "rushLeft", "rushRight") then
		if act.rushEffect and act.rushEffect.effectID == 10000 then
			delay = speed.normal
		else
			delay = speed.rush
		end
	elseif act.type == "rushKung" then
		delay = speed.rushKung
	elseif checkExist(act.type, "run", "walk") then
		delay = speed.normal

		if g_data.player.ability and self.isPlayer then
			delay = math.floor(speed.normal / (1 + g_data.player.ability.FMoveSpeed_WFB / 10000) * 1000) / 1000
		elseif act.tick and act.tick > 0 then
			delay = act.tick / 1000
		end

		if g_data.player.horseInfo.state == 1 then
			delay = g_data.horse:getHorseSpeed() / 1000
		end

		if def.role.stateHas(self.last.state or {}, "stBoDGLY") then
			if self.isPlayer then
				delay = speed.normal / g_data.horse:getAddSpeed()
			elseif act.tick and act.tick > 0 then
				delay = act.tick / 1000
			end
		end
	elseif checkExist(act.type, "hit", "spell", "heavyHit", "bigHit") then
		local spspeed = main_scene.ui.attsp
		local retainThree = math.round(1600 / (spspeed + 1600) * 1000) / 1000

		delay = speed.normal * retainThree or speed.normal

		if g_data.player.horseInfo.state == 1 then
			delay = g_data.horse:getHorseSpeed() / 1000
		end

		if def.role.stateHas(self.last.state or {}, "stBoDGLY") then
			if self.isPlayer then
				delay = speed.normal / g_data.horse:getAddSpeed()
			elseif act.tick and act.tick > 0 then
				delay = act.tick / 1000
			end
		end
	end

	local recordType

	for k, v in pairs(self.sprites) do
		if checkExist(k, "godAspects") then
			if act.type == "run" then
				recordType = {
					k = "type",
					v = act.type
				}
				act.type = "walk"
			elseif checkExist(act.type, "hit", "spell", "heavyHit", "bigHit") then
				recordType = {
					k = "type",
					v = act.type
				}
				act.type = "attack"
			end
		end

		delay = v:play(act, delay)

		if recordType then
			act[recordType.k] = recordType.v
			recordType = nil
		end
	end

	delay = delay or speed.normal

	if self.sprites.weapon then
		self.sprites.weapon.spr:setLocalZOrder((act.dir > def.role.dir.rightBottom or act.dir == def.role.dir.up) and -1 or 1)
	end

	if self.sprites.weaponbaseef then
		self.sprites.weaponbaseef.spr:setLocalZOrder((act.dir > def.role.dir.rightBottom or act.dir == def.role.dir.up) and -1 or 2)
	end

	if self.sprites.weaponef then
		self.sprites.weaponef.spr:setLocalZOrder((act.dir > def.role.dir.rightBottom or act.dir == def.role.dir.up) and -1 or 2)
	end

	if self.sprites.wing then
		local zOrder = act.dir >= def.role.dir.right and act.dir <= def.role.dir.left and -1 or 1
		local spr = self.sprites.wing.spr

		spr:setLocalZOrder(zOrder)
	end

	if not self.isIgnore then
		if act.hitEffect then
			magic.showHitEffect(act.hitEffect.magicId, {
				x = act.x,
				y = act.y,
				dir = act.dir,
				delay = delay,
				type = act.hitEffect.type,
				effectID = act.hitEffect.effectID,
				effectType = act.hitEffect.effectType,
				role = self
			})
		end

		if act.effect then
			local dir

			if act.hasDir and act.dir then
				dir = act.dir
			end

			magic.showSpellEffect(act.effect.effectID, {
				x = act.x,
				y = act.y,
				dir = dir,
				delay = delay,
				job = self.job
			})
		end

		local canShowRushEffect = true

		if self.lastRushTime and rushTimeSpace > socket.gettime() - self.lastRushTime then
			canShowRushEffect = false
		end

		if act.rushEffect and canShowRushEffect then
			self.lastRushTime = socket.gettime()

			if act.rushEffect.effectID == 10000 then
				local sx, sy = self:getPosition()

				magic.showHorseFireEffect(act.rushEffect.effectID, {
					sx = lastPX,
					sy = lastPY,
					ex = act.x or act.rushx,
					ey = act.y or act.rushy,
					dir = act.dir,
					delay = delay,
					job = self.job
				})
			else
				magic.showRushEffect(act.rushEffect.effectID, {
					x = act.x or act.rushx,
					y = act.y or act.rushy,
					dir = act.dir,
					delay = delay,
					job = self.job
				})
			end
		end
	end

	if act.flyaxe then
		local params = {
			role = self
		}

		table.merge(params, act.flyaxe)
		self.map:showEffectForName("flyaxe", params)
	end

	if act.otherEffect then
		if self.playingOtherEffect == true then
			return
		end

		local begin

		if act.otherEffect.isFixed then
			begin = act.otherEffect.begin
		else
			begin = act.otherEffect.begin + act.dir * (act.otherEffect.frame + act.otherEffect.skip)
		end

		local spr = m2spr.new(nil, nil, {
			blend = true,
			setOffset = true
		}):addto(self.node):pos(0, mapDef.tile.h)
		local noForever = true

		if act.otherEffect.noForever ~= nil then
			noForever = act.otherEffect.noForever

			if noForever == false then
				self.playingOtherEffect = true
			end
		end

		if act.otherEffect.ftime ~= nil then
			delay = act.otherEffect.ftime / 1000 * act.otherEffect.frame * (delay / speed.normal) * 2
		end

		if act.otherEffect.delayFrame and act.otherEffect.delayMax then
			spr:runs({
				cc.DelayTime:create(delay / act.otherEffect.delayMax * act.otherEffect.delayFrame),
				cc.Show:create(),
				cc.CallFunc:create(function()
					spr:playAni(act.otherEffect.img, begin, act.otherEffect.frame, delay / act.otherEffect.frame, true, true, noForever)
				end)
			})
		else
			spr:playAni(act.otherEffect.img, begin, act.otherEffect.frame, delay / act.otherEffect.frame, true, true, noForever)
		end
	end

	local acttype = act.type

	if acttype == "stand" then
		__position(self.node, self.map:getMapPos(act.x, act.y))
		self:executeEnd()
	elseif acttype == "walk" or acttype == "run" or acttype == "rushLeft" or acttype == "rushRight" then
		local disx, disy = math.abs(self.last.x - act.x), math.abs(self.last.y - act.y)

		if disx <= 2 and disy <= 2 and (disx == disy or disx == 0 or disy == 0) then
			local x, y = self:getPosition()
			local destx, desty = self.map:getMapPos(act.x, act.y)

			self:addAction({
				{
					"moveto",
					delay,
					x,
					y,
					destx,
					desty
				},
				{
					"function",
					handler(self, self.executeEnd)
				}
			})
			self.map:uptRoleXY(self, true)

			if g_data.player.horseInfo.state ~= 1 and self.feature.footPoint and self.feature.footPoint > 0 and (acttype == "walk" or acttype == "run") then
				local cnf = def.fashion.getEffectById(self.feature.footPoint)

				if cnf then
					local imgid = cnf.atlasName
					local begin = cnf.frameBegin
					local frame = cnf.frames
					local f_time = tonumber(cnf.delay)

					m2spr.playAnimation(imgid, begin, frame, f_time, false, true, true):pos(x + mapDef.tile.w / 2 + cnf.offsetX, y + mapDef.tile.h / 2 + cnf.offsetY):addto(self.map.layers.obj, y + mapDef.tile.h / 2)
				end
			end
		else
			act.type = "stand"

			self.node:pos(self.map:getMapPos(act.x, act.y))
			self:executeEnd()
		end
	elseif acttype == "hit" or acttype == "attack" or acttype == "heavyHit" or acttype == "bigHit" then
		self.node:pos(self.map:getMapPos(act.x, act.y))
		self:addAction({
			{
				"delay",
				delay
			},
			{
				"function",
				handler(self, self.executeEnd)
			}
		})
	elseif acttype == "rushKung" then
		local x, y = self:getPosition()
		local destx, desty = self.map:getMapPos(act.x, act.y)

		self:addAction({
			{
				"moveto",
				delay / 2,
				x,
				y,
				destx,
				desty
			},
			{
				"moveto",
				delay / 2,
				destx,
				desty,
				x,
				y
			},
			{
				"function",
				handler(self, self.executeEnd)
			}
		})
		self.map:uptRoleXY(self, true)
	elseif acttype == "digdown" and self.__cname == "mon" then
		self:addAction({
			{
				"delay",
				delay
			},
			{
				"function",
				function()
					self.readyRemove = true
				end
			}
		})
	elseif acttype == "spell" and self.isPlayer then
		if act.x and act.y then
			self.node:pos(self.map:getMapPos(act.x, act.y))
		end

		self:addAction({
			{
				"delay",
				delay
			},
			{
				"function",
				handler(self, self.executeEnd)
			}
		})
	elseif act == "struck" then
		self:addAction({
			{
				"delay",
				delay
			},
			{
				"function",
				function()
					if act == self.cur.act and self.cur.act.type == "struck" then
						self:executeEnd()
					end
				end
			}
		})
	else
		if act.x and act.y then
			__position(self.node, self.map:getMapPos(act.x, act.y))
		end

		self:addAction({
			{
				"delay",
				delay
			},
			{
				"function",
				handler(self, self.executeEnd)
			}
		})
	end

	self:executeSound(delay)
end

function role:addAction(params)
	self.actions = params
	self.actionsCache = {}
end

function role:executeActions(dt)
	local v = self.actions[1]

	if v[1] == "moveto" then
		if DEBUG > 0 and g_data.openRealTimeAction and self.lastPostTime == nil then
			self.lastPostTime = socket.gettime()
		end

		local delay, x, y, destx, desty = v[2], v[3], v[4], v[5], v[6]

		self.actionsCache.dt = (self.actionsCache.dt or 0) + dt

		if DEBUG > 0 and g_data.openRealTimeAction then
			self.actionsCache.dt = socket.gettime() - self.lastPostTime
		end

		self.isMoving = true

		local cur = self.actionsCache.dt

		if delay <= cur then
			if DEBUG > 0 and g_data.openRealTimeAction and self.lastPostTime then
				self.lastPostTime = nil
			end

			self.isMoving = false

			self.node:pos(destx, desty)

			self.actionsCache = {}

			table.remove(self.actions, 1)

			if #self.actions > 0 then
				self:executeActions(cur - delay)
			end
		else
			self.actionsCache.speed = self.actionsCache.speed or {
				(destx - x) / delay,
				(desty - y) / delay
			}

			__position(self.node, x + self.actionsCache.dt * self.actionsCache.speed[1], y + self.actionsCache.dt * self.actionsCache.speed[2])
		end
	elseif v[1] == "delay" then
		if DEBUG > 0 and g_data.openRealTimeAction and self.lastPostTime == nil then
			self.lastPostTime = socket.gettime()
		end

		local delay = v[2]

		self.actionsCache.dt = (self.actionsCache.dt or 0) + dt

		if DEBUG > 0 and g_data.openRealTimeAction then
			self.actionsCache.dt = socket.gettime() - self.lastPostTime
		end

		local cur = self.actionsCache.dt

		if delay <= cur then
			if DEBUG > 0 and g_data.openRealTimeAction and self.lastPostTime then
				self.lastPostTime = nil
			end

			self.actionsCache = {}

			table.remove(self.actions, 1)

			if #self.actions > 0 then
				self:executeActions(cur - delay)
			end
		end
	elseif v[1] == "function" then
		table.remove(self.actions, 1)
		v[2]()
	end
end

function role:isExecuteFast()
	local actNum = #self.acts

	return actNum >= self.actFastNum
end

function role:executeSound(delay)
	local act = self.cur.act

	if not act then
		return
	end

	if self.isPlayer and checkExist(act.type, "walk", "run", "rushLeft", "rushRight", "rushKung") then
		sound.play("footStep", {
			role = self,
			map = self.map,
			delay = delay
		})

		return
	end

	if self.__cname == "npc" then
		-- block empty
	elseif self.__cname == "mon" then
		sound.play("mon", {
			role = self,
			act = act,
			map = self.map
		})
	elseif self.__cname == "hero" and not self.isIgnore then
		if act.type == "hit" or act.type == "heavyHit" or act.type == "bigHit" then
			sound.play("hit", {
				role = self,
				effect = act.hitEffect,
				delay = delay
			})
		elseif act.type == "spell" and act.effect then
			sound.play("skillSpell", {
				role = self,
				magicId = act.effect.magicId
			})
		end
	end
end

function role:spellDone()
	if not main_scene then
		return
	end

	local controller = main_scene.ui.console.controller
	local map = main_scene.ground.map

	if controller.stopAttack then
		controller.stopAttack = false
	end
end

function role:executeEnd(act)
	self.curActEnd = true

	if #self.waits > 0 then
		return
	end

	self.actions = nil
	self.lock.execute = false
	self.last.act = act or self.cur.act
	self.cur.act = nil

	table.remove(self.acts, 1)
	self.map:uptRoleXY(self, false, self.last.x, self.last.y)

	if self.isPlayer then
		if self.last.act and (self.last.act.type == "spell" or self.last.act.type == "state" or self.last.act.type == "immediateMagicHit") then
			self:spellDone()
		end

		if #self.acts > 0 then
			self:executeAct()
		end
	elseif #self.acts == 0 and not self.isExecuteEnd then
		self:allExecuteEnd()
	end
end

function role:allExecuteEnd()
	self.isExecuteEnd = true

	if self.last.act.type ~= "stand" then
		self:addStandAct()
	end
end

function role:executeFail(x, y, dir)
	local act

	if #self.waits > 0 then
		act = self.waits[1]
		self.x, self.y, self.dir = x or act.wait.x or self.x, y or act.wait.y or self.y, dir or act.wait.dir or self.dir
		self.waits = {}
	end

	for k, v in pairs(self.parts) do
		if v.ani then
			v.ani:play("stand", self.dir)
		end
	end

	self.node:stopAllActions()

	self.lastPostTime = nil

	self.node:pos(self.map:getMapPos(self.x, self.y))
	self:executeEnd(act)
end

function role:executeSuccess()
	table.remove(self.waits, 1)

	if self.curActEnd then
		self:executeEnd()
	end
end

function role:addAct(params)
	if self.die and params.type ~= "die" and not params.gutou then
		return
	elseif params.type == "die" and not self.node:isRunning() then
		self:onEnter()
	end

	if self.cur.act and self.cur.act.type == "struck" then
		self:executeEnd()
	elseif params.type == "struck" and #self.acts > 0 then
		return
	end

	local function loadMapTest()
		if self.isPlayer and params.x and params.y then
			if params.type == "walk" or params.type == "run" or params.type == "rushLeft" or params.type == "rushRight" then
				self.map:load(self.x, self.y, params.x - self.x, params.y - self.y)
			elseif self.x ~= params.x or self.y ~= params.y or params.loadMap then
				self.map:load(params.x, params.y)
			end
		end
	end

	loadMapTest()

	params.x = params.x or self.x
	params.y = params.y or self.y
	params.dir = params.dir or self.dir

	if params.wait then
		self.waits[#self.waits + 1] = params
	end

	self.acts[#self.acts + 1] = params
	self.dir = params.dir
	self.x = params.x
	self.y = params.y
	self.isExecuteEnd = false

	if params.type == "die" then
		self.die = true

		self:uptInfoShow()
		self:uptSelfShow()
		self:clearLock()

		if main_scene and main_scene.ui.panels.minimap then
			main_scene.ui.panels.minimap:removePoint(self.roleid)
		end

		if self:isHeroForPlayer() and main_scene and main_scene.ui.panels.heroHead and main_scene.ui.panels.heroHead.headshot then
			main_scene.ui.panels.heroHead.headshot:setFilter(res.getFilter("gray"))
		end
	end

	self:changeMagicWeaponDir()

	if self.isPlayer and #self.acts == 1 then
		self:executeAct()
	end
end

function role:addStandAct()
	self:addAct({
		type = "stand",
		dir = self.dir,
		x = self.x,
		y = self.y
	})
end

function role:processMsg(ident, x, y, dir, feature, state, params, tick)
	if SM_Turn == ident then
		self:addAct({
			type = "stand",
			x = x,
			y = y,
			dir = dir,
			stone = state and def.role.stateHas(state, "stStone")
		})
	elseif SM_Appear == ident then
		self:addAct({
			type = "stand",
			x = x,
			y = y,
			dir = dir,
			stone = state and def.role.stateHas(state, "stStone")
		})
	elseif SM_WALK == ident or SM_NPCWALK == ident then
		self:addAct({
			type = "walk",
			x = x,
			y = y,
			dir = dir,
			tick = tick
		})
	elseif SM_RUN == ident then
		self:addAct({
			type = "run",
			x = x,
			y = y,
			dir = dir,
			tick = tick
		})
	elseif SM_BACKSTEP == ident then
		self:addAct({
			type = "walk",
			x = x,
			y = y,
			dir = dir
		})
	elseif SM_DEATH == ident then
		if params == nil then
			params = {
				flag = 0
			}
		end

		if params.flag == 0 then
			self:addAct({
				type = "die",
				x = x,
				y = y,
				dir = dir
			})
		elseif params.flag == 1 then
			self:addAct({
				type = "die",
				corpse = true,
				x = x,
				y = y,
				dir = dir
			})
		elseif params.flag == 2 then
			self:addAct({
				gutou = true,
				type = "die",
				dir = 0,
				x = x,
				y = y
			})
		end
	elseif AttackType.ATT_HIT == ident then
		self:addAct({
			type = self.__cname == "hero" and "hit" or "attack",
			x = x,
			y = y,
			dir = dir
		})
	elseif AttackType.ATT_HEAVYHIT == ident then
		self:addAct({
			type = "heavyHit",
			x = x,
			y = y,
			dir = dir
		})
	elseif AttackType.ATT_BIGHIT == ident then
		self:addAct({
			type = "bigHit",
			x = x,
			y = y,
			dir = dir
		})
	elseif AttackType.ATT_POWERHIT == ident then
		self:addAct({
			type = "hit",
			x = x,
			y = y,
			dir = dir,
			hitEffect = {
				magicId = 7,
				type = "pow"
			}
		})
	elseif AttackType.ATT_LONGHIT == ident then
		self:addAct({
			type = "hit",
			x = x,
			y = y,
			dir = dir,
			hitEffect = {
				magicId = 12,
				type = "long",
				effectID = params.effectId,
				effectType = params.effectType
			}
		})
	elseif AttackType.ATT_WIDEHIT == ident then
		self:addAct({
			type = "hit",
			x = x,
			y = y,
			dir = dir,
			hitEffect = {
				magicId = 25,
				type = "wide",
				effectID = params.effectId,
				effectType = params.effectType
			}
		})
	elseif AttackType.ATT_FIREHIT == ident then
		self:addAct({
			type = "hit",
			x = x,
			y = y,
			dir = dir,
			hitEffect = {
				magicId = 26,
				type = "fire",
				effectID = params.effectId,
				effectType = params.effectType
			}
		})
	elseif SM_4FIREHIT == ident then
		self:addAct({
			type = "hit",
			x = x,
			y = y,
			dir = dir,
			hitEffect = {
				magicId = 26,
				type = "fire4"
			}
		})
	elseif SM_HERO_LONGHIT == ident then
		self:addAct({
			type = "hit",
			x = x,
			y = y,
			dir = dir,
			hitEffect = {
				magicId = 34,
				type = "twn1"
			}
		})
	elseif SM_HERO_LASTHIT == ident then
		self:addAct({
			type = "hit",
			x = x,
			y = y,
			dir = dir,
			hitEffect = {
				magicId = 34,
				type = "twn2"
			}
		})
	elseif AttackType.ATT_SWORD_HIT == ident then
		self:addAct({
			type = "bigHit",
			x = x,
			y = y,
			dir = dir,
			hitEffect = {
				magicId = 58,
				type = "sword",
				effectID = params.effectId or 75,
				effectType = params.effectType
			}
		})
	elseif AttackType.ATT_BIGHEARTHIT == ident then
		self:addAct({
			type = "hit",
			x = x,
			y = y,
			dir = dir,
			hitEffect = {
				magicId = 26,
				type = "fire"
			}
		})
	elseif AttackType.ATT_COMBO_SJS == ident then
		self:addAct({
			type = "bigHit",
			x = x,
			y = y,
			dir = dir,
			hitEffect = {
				magicId = 114,
				type = "sjs",
				effectID = params.effectId or 166,
				effectType = params.effectType
			}
		})
	elseif AttackType.ATT_COMBO_SLZ == ident then
		self:addAct({
			type = "bigHit",
			x = x,
			y = y,
			dir = dir,
			hitEffect = {
				magicId = 115,
				type = "slz",
				effectID = params.effectId or 156,
				effectType = params.effectType
			}
		})
	elseif AttackType.ATT_COMBO_HSQJ == ident then
		self:addAct({
			type = "heavyHit",
			x = x,
			y = y,
			dir = dir,
			hitEffect = {
				magicId = 116,
				type = "hsqj",
				effectID = params.effectId or 157,
				effectType = params.effectType
			}
		})
	elseif SM_RUSH == ident then
		local rushEffect

		if params and params.effectId and params.effectId ~= 0 then
			rushEffect = {
				type = "rushEffect",
				effectID = params.effectId
			}
		end

		self:addAct({
			type = self.lastRushLeft and "rushRight" or "rushLeft",
			x = x,
			y = y,
			dir = dir,
			rushEffect = rushEffect
		})

		self.lastRushLeft = not self.lastRushLeft
	elseif SM_RUSHKUNG == ident then
		local rushEffect

		if params and params.effectId and params.effectId ~= 0 then
			rushEffect = {
				type = "rushEffect",
				effectID = params.effectId
			}
		end

		self:addAct({
			type = "rushKung",
			x = self.x,
			y = self.y,
			dir = self.dir,
			rushEffect = rushEffect
		})
	elseif SM_STRUCK == ident then
		self:addAct({
			type = "struck",
			hiter = x
		})
	elseif SM_FEATURE_CHANGED == ident then
		self:changeFeature(feature)
	elseif SM_CHGSTATUS == ident then
		self.state = state

		self:addAct({
			type = "state",
			state = self.state
		})
	elseif SM_DIGUP == ident then
		self:addAct({
			type = "digup"
		})
		sound.play("appr", self.sounds.appr)
	elseif SM_DIGDOWN == ident then
		self:addAct({
			type = "digdown",
			x = x,
			y = y
		})
	elseif SM_RELIVE == ident then
		self.die = false

		self:changeFeature(feature)
		self:uptInfoShow()
		self:addAct({
			type = "death"
		})
		sound.play("born", self.sounds.born)
	elseif SM_SPACEMOVE_SHOW == ident or SM_SPACEMOVE_SHOW2 == ident then
		self:addAct({
			type = "spacemove",
			x = x,
			y = y,
			dir = dir
		})
		self:spellDone()
	elseif SM_FLYAXE == ident then
		self:addAct({
			type = "attack",
			x = x,
			y = y,
			dir = def.role.getMoveDir(x, y, params.x, params.y),
			flyaxe = params
		})
	elseif SM_BUTCH == ident then
		self:addAct({
			type = "sitdown"
		})
	elseif SM_SPELL == ident then
		local hasDir = false
		local effect = params.effect

		if params.effect.effectID == 81 then
			hasDir = true
		elseif params.effect.effectID == 221 then
			self.info:showMianyi()

			return
		elseif checkExist(params.effect.effectID, 158, 159, 160, 161, 163, 165, 607, 1002) then
			hasDir = true
		end

		local actType = "spell"

		if params.effect.effectID == 223 then
			actType = "horse_spell"
		end

		if params.effect.effectID == 0 and params.effect.magicLevel then
			actType = "horse_spell"
		end

		if params.effect.magicLevel then
			self.valueEffect = params.effect.magicLevel
		end

		local act = {
			type = actType,
			dir = def.role.getMoveDir(self.x, self.y, params.targetX, params.targetY) or self.dir,
			hasDir = hasDir,
			effect = effect
		}

		if def.magic.isPalMagic(params.effect.magicId) then
			local monsterId = def.role.getRoleId(self.race, self.feature.dress)
			local frame = def.role.getFrame(monsterId)

			if frame and frame.spell and frame.spell.otherEffect then
				act.otherEffect = frame.spell.otherEffect
			end
		end

		if act.effect.effectID == 240 then
			act.dir = self.dir
		end

		self:addAct(act)
	elseif SM_HERO_LOGON == ident then
		-- block empty
	elseif SM_HEALTHSPELLCHANGED == ident then
		-- block empty
	elseif SM_UNITEHIT0 == ident then
		self:addAct({
			type = "hit",
			x = x,
			y = y,
			dir = dir,
			hitEffect = {
				magicId = 50,
				type = "zz"
			}
		})
	elseif SM_UNITEHIT1 == ident then
		self:addAct({
			type = "hit",
			x = x,
			y = y,
			dir = dir,
			hitEffect = {
				magicId = 52,
				type = "zf"
			}
		})
	elseif SM_UNITEHIT2 == ident then
		self:addAct({
			type = "hit",
			x = x,
			y = y,
			dir = dir,
			hitEffect = {
				magicId = 51,
				type = "zd"
			}
		})
	elseif SM_ResetBroadEffect == ident then
		self.state = state

		self:addAct({
			type = "state",
			state = self.state
		})
	end

	return self
end

local __getPosition = cc.Node.getPosition

function role:getPosition()
	return __getPosition(self.node)
end

function role:update(dt)
	if not self.isPlayer and not self.lock.execute and #self.acts > 0 then
		self:executeAct()
	end

	if self.actions and #self.actions ~= 0 then
		self:executeActions(dt)
	end

	if self.readyRemove then
		self.map:addMsg({
			remove = true,
			roleid = self.roleid
		})

		return
	end

	local x, y = self:getPosition()
	local lpos = self.last.pos

	if lpos.x ~= x or lpos.y ~= y then
		self:uptTrailingEffect(dt, lpos.x, lpos.y)

		lpos.x = x
		lpos.y = y

		self.info:uptPos(x, y)
		__position(self._auraNode, x, y)

		local _, y = self.map:getGamePos(x, y)

		self.node:setLocalZOrder(y)

		if main_scene and main_scene.ui.panels.minimap then
			main_scene.ui.panels.minimap:pointUpt(self.map, self)
		end

		if self.isPlayer then
			self.map:scroll()

			if main_scene.ui.panels.minimap then
				main_scene.ui.panels.minimap:scroll(self.map, self)
			end

			if main_scene.ui.panels.bigmap then
				main_scene.ui.panels.bigmap:pointUpt(self.map, self)
			end

			if main_scene.ui.panels.bigmapOther then
				main_scene.ui.panels.bigmapOther:pointUpt(self.map, self)
			end
		end
	end

	self:uptUnrealBannerEffect(dt)
end

function role:playWaterEffect()
	m2spr.playAnimation("prguse2", 670, 20, 0.12, true, true, true):addto(self.node, 2)
end

function role:playBodyEffect(type)
	local be, spr = self.bodyEffect[self.roleid]

	if be and be.spr then
		spr = be.spr
	end

	if be and be.type and type == be.type and spr then
		return
	end

	self:removeBodyEffect()

	self.bodyEffect[self.roleid] = {}
	self.bodyEffect[self.roleid].type = type

	local newSpr

	if type == "stNoDie" then
		if self.feature.race == 213 then
			newSpr = m2spr.playAnimation("mon60", 458, 8, 0.12, true, true, false):addto(self.node, 2)
		elseif self.feature.race == 214 then
			newSpr = m2spr.playAnimation("mon62", 448, 8, 0.12, true, true, false):addto(self.node, 2)
		else
			newSpr = m2spr.playAnimation("effect_wudi", 0, 11, 0.12, true, true, false):addto(self.node, 2):scale(0.7)
		end
	elseif type == "stDefenceAC" then
		newSpr = m2spr.playAnimation("effectshenjie", 0, 7, 0.12, true, true, false):addto(self.node, 2)
	elseif type == "stReviveSate" then
		local myRole = main_scene.ground.map:findRole(g_data.player.roleid)

		ccs.ArmatureDataManager:getInstance():addArmatureFileInfo("animation/fuhuo/fuhuo.csb")

		newSpr = ccs.Armature:create("fuhuo")

		newSpr:addTo(self.node):pos(15, 20)
		newSpr:getAnimation():play("fuhuo")
	elseif type == "stEntangled" then
		newSpr = m2spr.playAnimation("magic_hb", 0, 7, 0.08, false, true, false)

		newSpr:addTo(self.node, 2):pos(2, 22)
	elseif type == "stAttSky" then
		newSpr = m2spr.playAnimation("magic_shenqi", 0, 7, 0.08, true, true, false)

		newSpr:addTo(self.node, 2):pos(2, 22)
	elseif type == "stComAttSky" then
		newSpr = m2spr.playAnimation("magic_shenqi", 40, 49, 0.08, true, true, false)

		newSpr:addTo(self.node, 2):pos(2, 30)
	elseif type == "stSpeed" then
		newSpr = m2spr.playAnimation("effect_mon", 0, 8, 0.08, true, true, false)

		newSpr:addTo(self.node, 2)
	elseif type == "stGod" then
		newSpr = m2spr.playAnimation("effect_mon", 16, 8, 0.08, true, true, false)

		newSpr:addTo(self.node, 2)
	elseif type == "stBGZ_Shield" then
		newSpr = m2spr.playAnimation("magic20", 10, 8, 0.08, true, true, false)

		newSpr:addTo(self.node, 2):pos(2, 22)
	elseif type == "stSkillComboing" then
		-- block empty
	elseif type == "stSeaWarBossDieBuff_CXZL" then
		newSpr = m2spr.playAnimation("magic10", 310, 20, 0.08, true, true, false)

		newSpr:addTo(self.node, 2)
	elseif type == "stGod_TGZF" then
		newSpr = m2spr.playAnimation("cboeffect", 3990, 20, 0.08, true, true, false)

		newSpr:addTo(self.node, 2):pos(2, 25)
	elseif type == "stRecover_QJK" then
		newSpr = m2spr.playAnimation("magic10", 2152, 10, 0.08, true, true, false)

		newSpr:addTo(self.node, 2):pos(1, 27)
	elseif type == "stBuff_BSHF" then
		newSpr = m2spr.playAnimation("magic10", 1130, 13, 0.08, true, true, false)

		newSpr:addTo(self.node, 2):pos(2, 25)
	elseif type == "stZJEffect" then
		newSpr = m2spr.playAnimation("magic", 600, 10, 0.08, true, true, true)

		newSpr:addTo(self.node, 2):pos(2, 25)
	elseif type == "stReviveExtSate" then
		newSpr = m2spr.playAnimation("magic", 4010, 8, 0.08, true, true, true)

		newSpr:addTo(self.node, 2):pos(2, 25)
	elseif type == "stAntiDebuff" then
		newSpr = m2spr.playAnimation("effect_wudi1", 15, 6, 0.08, true, true, false)

		newSpr:addTo(self.node, 2):pos(25, 50)
	elseif type == "stPAL_BMYZ_MainUP" then
		newSpr = display.newSprite(res.gettex2("pic/panels/gemstones/effect_33.png")):addTo(self.node, 999):pos(30, 90):fadeOut(2):runs({
			cc.MoveBy:create(2, cc.p(0, 80)),
			cc.CallFunc:create(function()
				newSpr:removeSelf()
			end)
		})
	end

	self.bodyEffect[self.roleid].spr = newSpr
end

function role:playLhEffect(state)
	if self.node:getChildByName(state) then
		self.node:removeChildByName(state)
	end

	local newSpr

	newSpr = m2spr.playAnimation("effect_wudi1", 136, 9, 0.08, true, true, true)

	newSpr:addTo(self.node, 2):pos(20, 10):scale(0.7):setName(state)
end

function role:removeBodyEffect()
	local x = self.bodyEffect[self.roleid]

	if x and x.spr then
		x.spr:removeSelf()

		self.bodyEffect[self.roleid] = nil
	end
end

function role:playSpecialEffect(effectId)
	local ani = common.getSpeEffect(effectId)

	if not ani then
		return
	end

	local offsetX, offsetY = 0, 0

	if effectId == 11 then
		offsetX, offsetY = 25, 45
	elseif effectId == 12 then
		offsetX, offsetY = 30, 60
	end

	ani:addto(self.node, 2):pos(offsetX, offsetY):setName("SpeEffect" .. effectId)
end

function role:stopSpecialEffect(effectId)
	local effect = self.node:getChildByName("SpeEffect" .. effectId)

	if effect then
		effect:removeSelf()
	end
end

function role:setTrailingEffect(effectId)
	self.trailingEffect = effectId
end

function role:uptTrailingEffect(dt, x, y)
	if not self.trailingEffect or self.trailingEffect == 0 then
		return
	end

	local effectOffset = {
		[1] = 3872,
		[2] = 3856
	}

	self.trailingT = self.trailingT + dt

	if self.trailingT > 0.25 then
		self.trailingT = 0

		local idx = (self.dir + (self.trailingLeft and -1 or 1)) % 8

		if not self.trailingOffeset[idx] then
			local angle = 45 * idx * math.pi / 180

			self.trailingOffeset[idx] = {
				x = math.sin(angle) * 18,
				y = math.cos(angle) * 18
			}
		end

		m2spr.playAnimation("horse2", effectOffset[self.trailingEffect], 8, 0.13, true, true, true):addto(main_scene.ground.map.layers.obj):pos(x + self.trailingOffeset[idx].x, y + self.trailingOffeset[idx].y + mapDef.tile.h)

		self.trailingLeft = not self.trailingLeft
	end
end

function role:setShieldRing(currentValue, totalValue, wxCurrentValue, wxTotalValue)
	self._shieldRing = currentValue

	self:showShieldRingEffect(currentValue, totalValue, wxCurrentValue, wxTotalValue)
end

function role:showShieldRingEffect(currentValue, totalValue, wxCurrentValue, wxTotalValue, __scale)
	if self.info then
		self.info:setShield(currentValue, totalValue, wxCurrentValue, wxTotalValue)
	end

	__scale = __scale or 1.1

	if currentValue > 0 or wxCurrentValue > 0 then
		self.node:setScale(__scale)
		self._auraNode:setScale(__scale)
	else
		self.node:setScale(1)
		self._auraNode:setScale(1)
	end
end

function role:uptFXShow()
	local state = self.last.state or {}

	if self.sprites and self.sprites.godAspects then
		self.sprites.godAspects:setVisible(def.role.stateHas(state, "stBoInAspect"))
	end
end

function role:setUnrealBannerEffect(effectId)
	self.unrealBannerEffect = effectId
end

function role:uptUnrealBannerEffect(_)
	if not self._unrealBannerEffect then
		self._unrealBannerEffect = {}
	end

	if not self.unrealBannerEffect or self.unrealBannerEffect == 0 then
		local t = self._unrealBannerEffect[self.roleid]

		if t and t.spr then
			t.spr:removeSelf()

			self._unrealBannerEffect[self.roleid] = nil
		end

		return
	end

	local be, spr = self._unrealBannerEffect[self.roleid]

	if be and be.spr then
		spr = be.spr
	end

	if be and be.effectId and self.unrealBannerEffect == be.effectId and spr then
		-- block empty
	else
		local t = self._unrealBannerEffect[self.roleid]

		if t and t.spr then
			t.spr:removeSelf()

			self._unrealBannerEffect[self.roleid] = nil
		end

		self._unrealBannerEffect[self.roleid] = {}
		self._unrealBannerEffect[self.roleid].effectId = self.unrealBannerEffect

		local imagePath, resName, startFrame, during = g_data.unrealBanner:getResByImageId(self.unrealBannerEffect, true)

		if imagePath then
			local newSpr = m2spr.playAnimation(resName, startFrame, during, 0.13, true, false, false):addTo(self.node, 2):pos(25, 50)

			self._unrealBannerEffect[self.roleid].spr = newSpr
		end
	end
end

function role:isYinni()
	local state = self.last.state or {}

	if def.role.stateHas(state, "stYNRingHideState") or def.role.stateHas(state, "stYNRingHideStateNoDamege") or def.role.stateHas(state, "stYNRingHideStateWar") then
		return true
	end

	return false
end

return role
