﻿-- chunkname: @mir2\\single\\m2debug.lua

local current = ...
local common = import("..scenes.main.common.common")
local tags = {
	assert = "断言",
	autoRat = "挂机",
	other = "其他",
	error = "lua错误",
	bag = "背包",
	equip = "装备",
	net = "通讯",
	res = "资源",
	login = "登录",
	normal = "普通"
}
local shows = {
	fps = function(b)
		cc.Director:getInstance():setDisplayStats(b)
	end,
	同屏人数 = function(b)
		cc.Director:getInstance():getNotificationNode().screenNode:setVisible(b)
	end,
	ping值 = function(b)
		cc.Director:getInstance():getNotificationNode().pingNode:setVisible(b)
	end
}

function p2(tag, ...)
	print("_debug_", tag or "normal", ...)
end

function d2(tag, value, desciption, nestin)
	dump("_debug_", tag or "normal", value, desciption, nestin)
end

if DEBUG > 0 then
	local STP = import("...StackTracePlus")

	function __G__TRACKBACK__(errorMessage)
		local msg = tostring(errorMessage)

		if not ErrorLogs[msg] then
			ErrorLogs[msg] = true

			local traceMsg = debug.traceback("", 2)
			local STPMsg = STP.stacktrace("", 2)

			if buglyReportLuaException then
				print("____buglyReportLuaException")
				buglyLog(2, "error_log", msg)
				buglyReportLuaException(msg .. " : LuaVer=[" .. (MIR2_VERSION or "1.0.0") .. "]" .. " : BaseVer=[" .. (MIR2_VERSION_BASE or "1.0.0") .. "]", STPMsg)
			end

			p2("error", "----------------------------------------")
			p2("error", "LUA ERROR: " .. msg .. "\n" .. STPMsg)
			p2("error", "----------------------------------------")

			if device.platform == "windows" then
				showLuaErrorMsg(msg .. "\n" .. traceMsg)
			end
		end
	end

	local _dumpTag
	local _dump = dump

	function dump(mark, tag, value, desciption, nesting)
		if mark == "_debug_" then
			_dumpTag = tag or "normal"

			_dump(value, desciption, nesting)

			_dumpTag = nil
		else
			_dump(mark, tag, value, 1)
		end
	end

	if not _print then
		_print = print

		local function tprint(mark, tag, ...)
			local str

			if mark == "_debug_" then
				local params = {
					...
				}

				for i = 1, select("#", ...) do
					local v = select(i, ...)
					local valueType = type(v)

					if valueType == "boolean" then
						params[i] = v and "true" or "false"
					elseif valueType == "userdata" then
						params[i] = "userdata(" .. (v.__cname or tolua.type(v)) .. ")"
					elseif valueType ~= "string" and valueType ~= "number" then
						params[i] = valueType
					end
				end

				str = table.concat(params, "   ")
			else
				local params = {
					mark,
					tag,
					...
				}
				local arglen = select("#", ...) + 2

				if arglen == 2 and tag == nil then
					arglen = mark == nil and 0 or 1
				end

				for i = 1, arglen do
					local v = params[i]
					local valueType = type(v)

					if valueType == "boolean" then
						params[i] = v and "true" or "false"
					elseif valueType == "userdata" then
						params[i] = "userdata(" .. (v.__cname or tolua.type(v)) .. ")"
					elseif valueType ~= "string" and valueType ~= "number" then
						params[i] = valueType
					end
				end

				str = table.concat(params, "   ")
				tag = _dumpTag or "other"
			end

			if m2debug.enables[tag] then
				_print(string.format("[ %s ] %s", tag, str))
			end

			m2debug.add(tag, str)
		end

		if false then
			scheduler.performWithDelayGlobal(function()
				tprint("---------- test print ----------")
				tprint(true, false)
				tprint(true)
				tprint(false)
				tprint(false, false, false)
				tprint(nil, false)
				tprint(nil, nil, nil)
				tprint(nil, nil, "arg")
				tprint(nil, "arg", "arg")
				tprint("string1", "string2", "string3", "string4", "string5")
				tprint("number", 1, 2, 3, 4, 5)
				tprint("node")
				tprint(display.newNode())
				tprint("_debug_", "other", "---------- test print2 ----------")
				tprint("_debug_", "other", true, false)
				tprint("_debug_", "other", true)
				tprint("_debug_", "other", false)
				tprint("_debug_", "other", false, false, false)
				tprint("_debug_", "other", nil, false)
				tprint("_debug_", "other", nil, nil, "arg")
				tprint("_debug_", "other", nil, "arg", "arg")
				tprint("_debug_", "other", "string1", "string2", "string3", "string4", "string5")
				tprint("_debug_", "other", "number", 1, 2, 3, 4, 5)
				tprint("_debug_", "other", "node")
				tprint("_debug_", "other", display.newNode())
			end, 1)
		end

		print = tprint
	end

	local _replaceScene = display.replaceScene
	local afterDrawListener

	function display.replaceScene(newScene, ...)
		m2debug.show(newScene)

		if afterDrawListener then
			cc.Director:getInstance():getEventDispatcher():removeEventListener(afterDrawListener)

			afterDrawListener = nil
		end

		_replaceScene(newScene, ...)
	end

	local _pushScene = cc.Director.pushScene

	function cc.Director.pushScene(d, newScene, ...)
		if m2debug.node then
			m2debug.node:removeSelf()

			m2debug.node = nil
		end

		m2debug.show(newScene)

		if afterDrawListener then
			cc.Director:getInstance():getEventDispatcher():removeEventListener(afterDrawListener)

			afterDrawListener = nil
		end

		_pushScene(d, newScene, ...)
	end

	local _popScene = cc.Director.popScene

	function cc.Director.popScene(d, ...)
		if m2debug.node then
			m2debug.node:removeSelf()

			m2debug.node = nil
		end

		afterDrawListener = cc.EventListenerCustom:create("director_after_draw", function()
			local dir = cc.Director:getInstance()
			local running = dir:getRunningScene()

			m2debug.show(running)
			dir:getEventDispatcher():removeEventListener(afterDrawListener)

			afterDrawListener = nil
		end)

		d:getEventDispatcher():addEventListenerWithFixedPriority(afterDrawListener, 1)
		_popScene(d, ...)
	end

	local node = display.newNode()
	local screenNode = display.newNode():addTo(node)

	node.screenNode = screenNode

	local fontSize = 20
	local fontColor = display.COLOR_WHITE
	local fontSpace = 24
	local fontstrokeSize = 1
	local mir2LuaMemCnt = an.newLabel("", fontSize, fontstrokeSize, {
		sd = true,
		color = fontColor
	}):pos(0, 65 + 7 * fontSpace):add2(screenNode)
	local roleCnt = an.newLabel("", fontSize, fontstrokeSize, {
		sd = true,
		color = fontColor
	}):pos(0, 65 + 6 * fontSpace):add2(screenNode)
	local mapCnt = an.newLabel("", fontSize, fontstrokeSize, {
		sd = true,
		color = fontColor
	}):pos(0, 65 + 5 * fontSpace):add2(screenNode)
	local msgCnt = an.newLabel("", fontSize, fontstrokeSize, {
		sd = true,
		color = fontColor
	}):pos(0, 65 + 4 * fontSpace):add2(screenNode)
	local labelTexCnt = an.newLabel("", fontSize, fontstrokeSize, {
		sd = true,
		color = fontColor
	}):pos(0, 65 + 3 * fontSpace):add2(screenNode)
	local rsTexCnt = an.newLabel("", fontSize, fontstrokeSize, {
		sd = true,
		color = fontColor
	}):pos(0, 65 + 2 * fontSpace):add2(screenNode)
	local mir2TexCnt = an.newLabel("", fontSize, fontstrokeSize, {
		sd = true,
		color = fontColor
	}):pos(0, 65 + 1 * fontSpace):add2(screenNode)
	local m2sprCnt = an.newLabel("", fontSize, fontstrokeSize, {
		sd = true,
		color = fontColor
	}):pos(0, 65 + 0 * fontSpace):add2(screenNode)

	cc.Director:getInstance():setNotificationNode(node)

	debugInfoScheduler = scheduler.scheduleUpdateGlobal(function()
		if main_scene and main_scene.ground and main_scene.ground.map then
			local roles = {}

			table.merge(roles, main_scene.ground.map.heros)
			table.merge(roles, main_scene.ground.map.mons)
			table.merge(roles, main_scene.ground.map.npcs)

			local allRoleCnt = table.nums(roles)
			local ignoreCnt = 0

			for k, v in pairs(roles) do
				if v.isIgnore then
					ignoreCnt = ignoreCnt + 1
				end
			end

			roleCnt:setString("同屏人数: " .. allRoleCnt - ignoreCnt .. " / " .. allRoleCnt .. " / " .. (main_scene.ground.map.current_frame_updatedRoles or 0))
		end

		if MirAtlasMgr and MirAtlasMgr.getInstance then
			local textureSize = 0

			textureSize = MirAtlasMgr:getInstance():getTotalTextureSize() / 1024

			m2sprCnt:setString(string.format("MirAtlas内存:%.1fMb", textureSize))
		end

		local TextureCache = cc.Director:getInstance():getTextureCache()

		if TextureCache.getCachedTextureSize then
			local textureCachedSize = TextureCache:getCachedTextureSize() / 1024

			mir2TexCnt:setString(string.format("引擎纹理内存:%.1fMb", textureCachedSize))
		end

		local luamalloc = collectgarbage("count")
		local luamalloc = luamalloc / 1024

		mir2LuaMemCnt:setString(string.format("lua内存:%.1fMb", luamalloc))
	end)
	node.pingNode = display.newNode():addTo(node)
	node.pingNode.label = an.newLabel("", 18, 0.8, {
		sd = true,
		color = display.COLOR_GREEN
	}):addTo(node.pingNode):pos(0, 210)

	scheduler.scheduleGlobal(function()
		if main_scene then
			g_data.client:setLastTime("ping", true)
		end
	end, 5)
else
	function print()
		return
	end

	function dump()
		return
	end

	return
end

local debugNode
local m2debug = {
	catch = false,
	allowTouch = true,
	enables = {},
	showEnables = {},
	texts = {},
	cmNames = {},
	smNames = {},
	setting = {}
}

for k, v in pairs(tags) do
	m2debug.enables[k] = true
end

local filter = cache.getDebug("filter")

if filter then
	for k, v in pairs(filter) do
		m2debug.enables[k] = v
	end
end

for k, v in pairs(shows) do
	m2debug.showEnables[k] = false
end

local showEnables = cache.getDebug("shows")

if showEnables then
	for k, v in pairs(showEnables) do
		m2debug.showEnables[k] = v
	end
end

for k, v in pairs(shows) do
	shows[k](m2debug.showEnables[k])
end

local setting = cache.getDebug("setting")

if setting then
	m2debug.setting = setting
end

local roleSpeed = cache.getDebug("roleSpeed")

if roleSpeed then
	m2debug.roleSpeed = roleSpeed
end

for k, v in pairs(_G) do
	if type(v) == "number" then
		if string.find(k, "CM_") == 1 then
			m2debug.cmNames[v] = k
		elseif string.find(k, "SM_") == 1 then
			m2debug.smNames[v] = k
		end
	end
end

function m2debug.add(tag, str)
	m2debug.texts[#m2debug.texts + 1] = {
		tag,
		str
	}

	if m2debug.enables[tag] and m2debug.node then
		m2debug.node:addLog(tag, str)
	end
end

function m2debug.show(scene)
	if not m2debug.hideNode then
		m2debug.node = debugNode.new():add2(scene, an.z.debug)
	end
end

function m2debug.saveLog()
	if not m2debug.hideNode then
		m2debug.node:saveDebugLog()
	end
end

debugNode = class("debugNode", function()
	return display.newNode()
end)

table.merge(debugNode, {
	btn,
	btns,
	beganPos,
	beganTouchPos,
	hasMove,
	lock,
	content,
	catchNode
})

function debugNode:ctor()
	self.btn = res.get2("pic/console/iconbg8.png")

	self.btn:pos(self.btn:centerPos()):add2(self, 1):setCascadeOpacityEnabled(true)
	res.get2("pic/debug/icon.png"):pos(self.btn:centerPos()):add2(self.btn)
	self:setCascadeOpacityEnabled(true)
	self:size(self.btn:getw(), self.btn:geth()):anchor(0.5, 0.5):pos(self:getw() / 2, display.height - self:geth() / 2):opacity(0):runs({
		cc.FadeIn:create(1),
		cc.DelayTime:create(3),
		cc.CallFunc:create(function()
			self:opacity(128)
		end)
	})
	self.btn:setTouchEnabled(true)
	self.btn:addNodeEventListener(cc.NODE_TOUCH_EVENT, function(event)
		if self.lock then
			return
		end

		if event.name == "began" then
			self.beganPos = cc.p(self:getPosition())
			self.beganTouchPos = cc.p(event.x, event.y)
			self.hasMove = false

			self:opacity(255)
			self:scale(1)
			self:stopAllActions()
		elseif event.name == "moved" then
			if self.hasMove or math.abs(self.beganTouchPos.x - event.x) > 10 or math.abs(self.beganTouchPos.y - event.y) > 10 then
				self.hasMove = true

				local x, y = event.x - self.beganTouchPos.x + self.beganPos.x, event.y - self.beganTouchPos.y + self.beganPos.y

				x = x < 0 and 0 or x
				x = x > display.width and display.width or x
				y = y < 0 and 0 or y
				y = y > display.height and display.height or y

				self:pos(x, y)
			end
		elseif event.name == "ended" then
			local function newx(x)
				x = x < self:getw() / 2 and self:getw() / 2 or x
				x = x > display.width - self:getw() / 2 and display.width - self:getw() / 2 or x

				return x
			end

			local function newy(y)
				y = y < self:geth() / 2 and self:geth() / 2 or y
				y = y > display.height - self:geth() / 2 and display.height - self:geth() / 2 or y

				return y
			end

			local function bothXY(x, y)
				if y < self:geth() then
					x = newx(x)
					y = self:geth() / 2
				elseif y > display.height - self:geth() then
					x = newx(x)
					y = display.height - self:geth() / 2
				elseif x > display.cx then
					x = display.width - self:getw() / 2
					y = newy(y)
				else
					x = self:getw() / 2
					y = newy(y)
				end

				return x, y
			end

			local function goto(x, y)
				if self.content then
					self:moveTo(0.25, x, y)
				else
					self:runs({
						cc.MoveTo:create(0.25, cc.p(x, y)),
						cc.DelayTime:create(3),
						cc.CallFunc:create(function()
							self:opacity(128)
						end)
					})
				end
			end

			if not self.hasMove then
				self.lock = true

				self.btn:runs({
					cc.ScaleTo:create(0.1, 0.01),
					cc.ScaleTo:create(0.1, 1),
					cc.CallFunc:create(function()
						self.lock = nil

						if self.content then
							self.content:removeSelf()

							self.content = nil

							goto(bothXY(self:getPosition()))
						else
							self:createContent()
						end
					end)
				})
			else
				local x, y = event.x - self.beganTouchPos.x + self.beganPos.x, event.y - self.beganTouchPos.y + self.beganPos.y

				if self.content then
					x, y = newx(x), newy(y)
				else
					x, y = bothXY(x, y)
				end

				goto(x, y)
			end
		end

		return true
	end)

	if device.platform == "windows" and IS_PC_SIMUALTOR then
		self:setKeypadEnabled(true)
		self:addNodeEventListener(cc.KEYPAD_EVENT, function(event)
			if event.code == 77 then
				if main_scene and main_scene.ui then
					main_scene.ui:togglePanel("coreManager")
				end
			elseif event.code == 78 then
				if main_scene and main_scene.ui then
					main_scene.ui:togglePanel("quickTest")
				end
			elseif g_data and g_data.eventDispatcher then
				g_data.eventDispatcher:dispatch("CC_KEYPAD_EVENT", event)
			end
		end)
	end
end

function debugNode:createContentBase(type)
	if self.content then
		self.content:removeSelf()
	end

	self.content = display.newNode():anchor(0, 1):pos(self.btn:getw() / 2 + 5, self.btn:geth() / 2 - 5):size(480, 320):add2(self)
	self.content.type = type

	display.newColorLayer(cc.c4b(0, 0, 0, 128)):size(self.content:getContentSize()):add2(self.content)
	display.newScale9Sprite(res.getframe2("pic/scale/scale2.png")):anchor(0, 0):size(self.content:getContentSize()):add2(self.content)
end

function debugNode:saveDebugLog()
	local folder = os.date("%Y-%m-%d")
	local key = os.date("%H-%M-%S") .. ".txt"
	local value = {}

	for i, v in ipairs(m2debug.texts) do
		value[#value + 1] = string.format("[%s]  %s", v[1], v[2])
	end

	cache.saveDebugLog(folder, key, value)
	print("调试球日志已保存到[" .. folder .. "/" .. key .. "]")
end

function debugNode:createContent(hasInput)
	self:createContentBase("main")

	local scroll = an.newScroll(6, 6, self.content:getw() - 16, self.content:geth() - 12, {
		labelM = {
			18,
			0
		}
	}):anchor(0, 0):addTo(self.content)

	self.content.beginpos = 1
	self.content.scroll = scroll

	local loadFront

	scroll:enableTouch(m2debug.allowTouch)
	scroll:setListenner(function(event)
		local x, y = scroll:getScrollOffset()

		if event.name == "moved" then
			if y + scroll.labelM.wordSize.height > scroll:getScrollSize().height - scroll:geth() then
				self:hideNewMark()
			end

			if y < 0 and not loadFront and self.content.beginpos > 1 then
				loadFront = true
			end
		elseif event.name == "ended" and loadFront then
			local source = {}

			for i = self.content.beginpos - 1, 1, -1 do
				local v = m2debug.texts[i]

				if m2debug.enables[v[1]] then
					self.content.beginpos = i

					table.insert(source, 1, v)

					if #source >= 20 then
						break
					end
				end
			end

			if #source > 0 then
				local labelM = an.newLabelM(scroll:getw(), scroll.labelM.fontSize, 0)

				for i, v in ipairs(source) do
					labelM:nextLine():addLabel("[ " .. v[1] .. " ] ", self:getColor(v[1])):addLabel(v[2])
				end

				display.newColorLayer(cc.c4b(255, 255, 0, 255)):size(labelM:getw(), 1):add2(labelM)
				scroll.labelM:insertNodeToFront(labelM, #labelM.lines)
				scroll:setScrollOffset(0, y + labelM:geth() - labelM.wordSize.height / 2)
			end

			loadFront = nil
		end
	end)

	local source = {}

	for i = #m2debug.texts, 1, -1 do
		local v = m2debug.texts[i]

		if m2debug.enables[v[1]] then
			self.content.beginpos = i

			table.insert(source, 1, v)

			if #source >= 20 then
				break
			end
		end
	end

	for i, v in ipairs(source) do
		self:addLog(v[1], v[2])
	end

	an.newBtn(res.gettex2("pic/scale/scale2.png"), function()
		local folder = os.date("%Y-%m-%d")
		local key = os.date("%H-%M-%S") .. ".txt"
		local value = {}

		for i, v in ipairs(m2debug.texts) do
			value[#value + 1] = string.format("[%s]  %s", v[1], v[2])
		end

		cache.saveDebugLog(folder, key, value)
		self:createContentForTips("已保存到[" .. folder .. "/" .. key .. "]")
	end, {
		pressBig = true,
		scale9 = cc.size(80, 40),
		label = {
			"保存",
			18,
			1,
			{
				color = cc.c3b(255, 255, 0)
			}
		}
	}):add2(self.content):pos(self.content:getw() - 50, self.content:geth() - 50)

	if hasInput then
		local function executeLua(text)
			local f = loadstring(text)

			if f then
				f()
			else
				print("lua格式有误.")
			end
		end

		local input
		local mac_use_source_keyboard = true

		if (device.platform == "mac" or device.platform == "windows") and mac_use_source_keyboard then
			input = cc.ui.UIInput.new({
				UIInputType = 1,
				size = cc.size(self.content:getw(), 40),
				image = display.newScale9Sprite(res.getframe2("pic/scale/scale2.png")),
				listener = function(type)
					if type == "changed" then
						local text = input:getText()

						if string.byte(string.reverse(text)) == string.byte("\\") then
							executeLua(string.sub(text, 1, #text - 1))
							input:setText("")
						end
					else
						executeLua(input:getText())
						input:setText("")
					end
				end
			}):anchor(0, 1):opacity(0):fadeIn(0.1):pos(0, 24):moveTo(0.1, 0, 4):add2(self.content)
		else
			input = an.newInput(0, 0, self.content:getw(), 40, 255, {
				label = {
					"",
					22,
					0
				},
				bg = {
					h = 40,
					tex = res.gettex2("pic/scale/scale2.png"),
					offset = {
						-10,
						0
					}
				},
				return_call = function()
					executeLua(input:getText())
					input:setText("")
				end
			}):anchor(0, 1):opacity(0):fadeIn(0.1):pos(10, 24):moveTo(0.1, 10, 4):add2(self.content)
		end

		display.newColorLayer(cc.c4b(0, 0, 0, 128)):size(input:getContentSize()):add2(input, -1)
	end

	local posx = 0

	local function add(text, func)
		local w = 30
		local btn

		btn = an.newBtn(res.gettex2("pic/scale/scale2.png"), function()
			func(btn)
		end, {
			pressBig = true,
			scale9 = cc.size(string.utf8len(text) * w, 40),
			label = {
				text,
				18,
				1,
				{
					color = cc.c3b(255, 255, 0)
				}
			}
		}):add2(self.content, -1):anchor(0, 0):pos(28 + posx, self.content:geth() - 4)

		display.newColorLayer(cc.c4b(0, 0, 0, 128)):size(btn:getContentSize()):add2(btn, -1)

		posx = posx + btn:getw() + 2
	end

	add(m2debug.allowTouch and "可触摸" or "不可触摸", function(btn)
		m2debug.allowTouch = not m2debug.allowTouch

		if m2debug.allowTouch then
			btn.label:setText("可触摸")
		else
			btn.label:setText("不可触摸")
		end

		scroll:enableTouch(m2debug.allowTouch)
	end)
	add("清空", function()
		scroll.labelM:clear()
	end)
	add("过滤", function()
		self:createContentForFilter()
	end)
	add("lua", function()
		self:createContentForLua()
	end)
	add("设置", function()
		self:createContentForSetting()
	end)
	add("GM", function()
		self:createContentForGMCmd()
	end)
end

function debugNode:createContentForFilter()
	self:createContentBase()
	self.content:setNodeEventEnabled(true)

	function self.content.onCleanup()
		cache.saveDebug("filter", m2debug.enables)
	end

	local cnt = 0

	local function add(key, text)
		local col = cnt % 3
		local line = math.modf(cnt / 3)
		local pos = cc.p(20 + col * 160, self.content:geth() - 40 - line * 60)
		local toggle = an.newToggle(res.gettex2("pic/common/toggle10.png"), res.gettex2("pic/common/toggle11.png"), function(b)
			m2debug.enables[key] = b
		end, {
			easy = true,
			default = m2debug.enables[key],
			label = {
				text .. "[" .. key .. "]",
				20,
				1,
				{
					color = self:getColor(key)
				}
			}
		}):anchor(0, 0.5):pos(pos.x, pos.y):add2(self.content)

		cnt = cnt + 1
	end

	for k, v in pairs(tags) do
		add(k, v)
	end

	an.newBtn(res.gettex2("pic/scale/scale2.png"), function()
		self:createContent()
	end, {
		pressBig = true,
		scale9 = cc.size(80, 40),
		label = {
			"返回",
			18,
			1,
			{
				color = cc.c3b(255, 255, 0)
			}
		}
	}):add2(self.content):pos(self.content:getw() - 50, 30)
end

function debugNode:createContentForLua()
	self:createContentBase()

	local config = {
		{
			"执行lua语句..",
			function()
				self:createContent(true)
			end
		},
		{
			"查询全局变量值..",
			function()
				self:createContentForLuaQueryVar()
			end
		},
		{
			"查看常量值..",
			function()
				self:createContentForLuaQueryConst()
			end
		},
		{
			"当前版本:" .. (MIR2_VERSION or ""),
			function()
				return
			end
		}
	}

	for i, v in ipairs(config) do
		an.newLabel(v[1], 22, 0, {
			color = cc.c3b(255, 255, 0)
		}):pos(20, self.content:geth() - 80 - (i - 1) * 40):add2(self.content):addUnderline(cc.c3b(255, 255, 0)):enableClick(v[2], {
			ani = true
		})
	end

	an.newBtn(res.gettex2("pic/scale/scale2.png"), function()
		self:createContent()
	end, {
		pressBig = true,
		scale9 = cc.size(80, 40),
		label = {
			"返回",
			18,
			1,
			{
				color = cc.c3b(255, 255, 0)
			}
		}
	}):add2(self.content):pos(self.content:getw() - 50, 30)
end

function debugNode:createContentForLuaQueryVar()
	self:createContentBase()
	an.newLabel("变量名: ", 22, 1, {
		color = cc.c3b(0, 255, 0)
	}):anchor(0, 0.5):pos(20, self.content:geth() - 50):add2(self.content)

	local input = an.newInput(130, self.content:geth() - 52, 200, 32, 15, {
		label = {
			"g_data",
			22,
			1
		},
		bg = {
			h = 40,
			tex = res.gettex2("pic/scale/scale2.png"),
			offset = {
				-10,
				0
			}
		}
	}):anchor(0, 0.5):add2(self.content)
	local config = {
		"def",
		"g_data",
		"game",
		"res",
		"display",
		"device"
	}

	for i, v in ipairs(config) do
		local col = math.modf((i - 1) / 3)
		local line = (i - 1) % 3

		an.newLabel(v, 22, 0, {
			color = cc.c3b(255, 255, 0)
		}):anchor(0.5, 0.5):pos(60 + line * 170, self.content:geth() - 120 - col * 50):add2(self.content):addUnderline(cc.c3b(255, 255, 0)):enableClick(function()
			input:setString(v)
		end, {
			ani = true,
			size = cc.size(120, 40)
		})
	end

	an.newBtn(res.gettex2("pic/scale/scale2.png"), function()
		self:createContentForLuaQueryVarDetail(input:getText())
	end, {
		pressBig = true,
		scale9 = cc.size(80, 40),
		label = {
			"确定",
			18,
			1,
			{
				color = cc.c3b(255, 255, 0)
			}
		}
	}):add2(self.content):pos(self.content:getw() - 150, 30)
	an.newBtn(res.gettex2("pic/scale/scale2.png"), function()
		self:createContentForLua()
	end, {
		pressBig = true,
		scale9 = cc.size(80, 40),
		label = {
			"返回",
			18,
			1,
			{
				color = cc.c3b(255, 255, 0)
			}
		}
	}):add2(self.content):pos(self.content:getw() - 50, 30)
end

function debugNode:createContentForLuaQueryVarDetail(varText, parent)
	self:createContentBase()

	parent = parent or {}
	parent[#parent + 1] = varText

	local function goback()
		if #parent == 1 then
			self:createContentForLuaQueryVar()
		else
			parent[#parent] = nil

			local lastVar = parent[#parent]

			parent[#parent] = nil

			self:createContentForLuaQueryVarDetail(lastVar, clone(parent))
		end
	end

	local fullVarText = ""

	for i, v in ipairs(parent) do
		if i == 1 then
			fullVarText = v
		elseif type(v) == "string" then
			fullVarText = fullVarText .. "[\"" .. v .. "\"]"
		elseif type(v) == "number" then
			fullVarText = fullVarText .. "[" .. v .. "]"
		else
			fullVarText = fullVarText .. ":get(\"" .. v[1] .. "\")"
		end
	end

	local str = "local var = " .. fullVarText .. " return var"
	local f = loadstring(str)

	if not f then
		self:createContentForTips("查询失败. [" .. str .. "]", goback)

		return
	end

	print(fullVarText)

	local var = f()

	if type(var) ~= "table" then
		self:createContentForTips("变量[" .. varText .. "]并不是table类型", goback)

		return
	end

	local scroll = an.newScroll(6, 6, self.content:getw() - 16, self.content:geth() - 12, {
		labelM = {
			22,
			0
		}
	}):anchor(0, 0):addTo(self.content)

	scroll.labelM:nextLine():addLabel("变量名: " .. fullVarText, cc.c3b(255, 0, 255)):nextLine()

	local showVar = var
	local keys = table.keys(showVar)

	table.sort(keys, function(a, b)
		return tostring(a) < tostring(b)
	end)

	for i, k in pairs(keys) do
		local v = showVar[k]

		if type(v) == "table" then
			scroll.labelM:nextLine():addLabel(type(v) .. "  ", display.COLOR_GREEN):addLabel(k .. "  ", cc.c3b(0, 255, 255)):addLabel("查看详情[" .. table.nums(v) .. "]", cc.c3b(255, 255, 0), nil, nil, {
				ani = true,
				callback = function()
					self:createContentForLuaQueryVarDetail(k, clone(parent))
				end
			})
		elseif type(v) == "number" or type(v) == "string" then
			scroll.labelM:nextLine():addLabel(type(v) .. "  ", display.COLOR_GREEN):addLabel(k .. "  ", cc.c3b(0, 255, 255)):addLabel(v)
		end
	end

	an.newBtn(res.gettex2("pic/scale/scale2.png"), goback, {
		pressBig = true,
		scale9 = cc.size(80, 40),
		label = {
			"返回",
			18,
			1,
			{
				color = cc.c3b(255, 255, 0)
			}
		}
	}):add2(self.content):pos(self.content:getw() - 50, 30)
end

function debugNode:createContentForLuaQueryConst()
	self:createContentBase()

	local scroll = an.newScroll(6, 6, self.content:getw() - 16, self.content:geth() - 12, {
		labelM = {
			18,
			0
		}
	}):anchor(0, 0):addTo(self.content)
	local config = {
		{
			"原始版本",
			MIR2_VERSION_BASE
		},
		{
			"现在版本",
			MIR2_VERSION
		},
		{
			"登录服务器ip",
			def.ip
		},
		{
			"登录服务器端口",
			def.port
		},
		{
			"区服id",
			def.areaID
		},
		{
			"更新服务器ip",
			import("...upt.def", current).httpRoot
		},
		{
			"httpRoot",
			def.httpRoot
		},
		{
			"chatHttpRoot",
			def.chatHttpRoot
		},
		{
			"屏幕宽高",
			display.width .. " * " .. display.height
		},
		{
			"版本类型",
			def.gameVersionType
		},
		{
			"客户端版本号",
			def.MIR_VERSION_NUMBER
		}
	}

	for i, v in ipairs(config) do
		scroll.labelM:nextLine():addLabel(v[1] .. ": ", display.COLOR_GREEN):addLabel(v[2])
	end

	an.newBtn(res.gettex2("pic/scale/scale2.png"), function()
		self:createContentForLua()
	end, {
		pressBig = true,
		scale9 = cc.size(80, 40),
		label = {
			"返回",
			18,
			1,
			{
				color = cc.c3b(255, 255, 0)
			}
		}
	}):add2(self.content):pos(self.content:getw() - 50, 30)
end

function debugNode:createContentForAdaptSpeed()
	self:createContentBase()

	if not m2debug.roleSpeed then
		m2debug.roleSpeed = def.role.speed
	else
		def.role.speed = m2debug.roleSpeed
	end

	config = {
		{
			"一般动作",
			"normal"
		},
		{
			"加速",
			"fast"
		},
		{
			"冲撞失败",
			"rushKung"
		},
		{
			"野蛮冲撞",
			"rush"
		},
		{
			"基础释法间隔",
			"spell"
		},
		{
			"基础攻击间隔",
			"attack"
		}
	}

	local preInput
	local adapt2input = {}
	local h = 0

	for k, v in pairs(config) do
		h = h + 45

		local input

		local function stopCb()
			print(v[1], num)

			if not tolua.isnull(input) then
				local num = tonumber(input:getString())

				def.role.speed[v[2]] = num

				cache.saveDebug("roleSpeed", def.role.speed)
				print(v[1], num)
			end
		end

		input = an.newInput(200, self.content:geth() - h, 170, 32, 15, {
			label = {
				"" .. def.role.speed[v[2]],
				22,
				1
			},
			bg = {
				h = 40,
				tex = res.gettex2("pic/scale/scale2.png"),
				offset = {
					-10,
					0
				}
			},
			start_call = function()
				if preInput and preInput ~= input then
					preInput:stopInput()
				end

				preInput = input
			end,
			stop_call = stopCb
		}):anchor(0, 0.5):add2(self.content)

		function input.onCleanup()
			stopCb()
			input:stopInput()
		end

		local lb = an.newLabel(v[1] .. ":", 22, 0, {
			color = cc.c3b(255, 255, 255)
		}):pos(10, self.content:geth() - h - 10):add2(self.content)
	end
end

function debugNode:createContentForTest()
	local function setAssetServerUrl(url)
		local uptScene = require("upt.scene")

		SKIP_UPT = false
		s = uptScene.new(function()
			s:setTitle("请重启游戏")
		end)

		s:rmdir(device.writablePath .. "cache/")
		s:rmdir(s.storagePath .. "res/")
		s:rmdir(s.storagePath .. "rs/")
		s:rmdir(s.storagePath .. "upt/")
		os.remove(s.storagePath .. "project.manifest")
		os.remove(s.storagePath .. "version.manifest")
		display.replaceScene(s)
		s:saveRemoteAddress(url)
	end

	local function connectServer(ip, port)
		local loginTcp = g_data.login:getLoginTCP()

		loginTcp:clearRemoteHosts()
		loginTcp:clearAllSunscribeScriptOnProtocol()
		loginTcp:clearAllSubscribeOnState()

		if loginTcp:isConnected() then
			print("m2debug:connectServer -- loginTcp:disconnect(true)")
			loginTcp:disconnect(true)
		end

		loginTcp:addRemoteHost(def.ip, def.port)
		print("m2debug:connectServer -- loginTcp:connect start")
		loginTcp:connect()
	end

	local function disconnectServer()
		local loginTcp = g_data.login:getLoginTCP()

		loginTcp:clearRemoteHosts()
		loginTcp:clearAllSunscribeScriptOnProtocol()
		loginTcp:clearAllSubscribeOnState()

		if loginTcp:isConnected() then
			print("m2debug:connectServer -- loginTcp:disconnect(false)")
			loginTcp:disconnect(false)
		end
	end

	local config = {
		{
			"执行hotfix",
			function()
				if hotfix_app_phone_call_crash then
					hotfix_app_phone_call_crash()
				else
					print("hotfix_app_phone_call_crash is nil")
				end
			end
		},
		{
			"模拟来电",
			function(edit)
				local str = "{\n \"state\":1,\n \"number\": 18262284791\n}"

				if _G.app_phone_call then
					app_phone_call(str)
				else
					print("app_phone_call is nil")
				end
			end
		},
		{
			"模拟接听电话",
			function(edit)
				local str = "{\n \"state\":2,\n \"number\": 18262284791\n}"

				if _G.app_phone_call then
					app_phone_call(str)
				else
					print("app_phone_call is nil")
				end
			end
		},
		{
			"模拟呼出电话",
			function(edit)
				local str = "{\n \"state\":3,\n \"number\": 18262284791\n}"

				if _G.app_phone_call then
					app_phone_call(str)
				else
					print("app_phone_call is nil")
				end
			end
		},
		{
			"模拟挂断电话",
			function(edit)
				local str = "{\n \"state\":0,\n \"number\": 18262284791\n}"

				if _G.app_phone_call then
					app_phone_call(str)
				else
					print("app_phone_call is nil")
				end
			end
		},
		{
			"重启lua时不重启虚拟机",
			function()
				g_data.testOldRestart = not g_data.testOldRestart

				tip("重启lua时将" .. (g_data.testOldRestart and "----不----重启虚拟机" or "重启虚拟机"))
			end
		},
		{
			"重启lua",
			function()
				reStart()
			end
		},
		{
			"是否越狱或Root",
			function()
				if device.isJailBrokenOrRoot() then
					tip("(越狱/Root)设备")
				else
					tip("非(越狱/Root)设备")
				end
			end
		},
		{
			"微端下载",
			function()
				if main_scene and main_scene.ui then
					main_scene.ui:togglePanel("miniResDownload")
				end
			end
		},
		{
			"截图",
			function()
				if main_scene and main_scene.ui then
					self:hide()
					main_scene.ui:togglePanel("screenshot")
				end
			end
		},
		{
			"打开游戏内论坛",
			function()
				MirSDKAgent:openGameCenter()
			end
		},
		{
			"图库资源测试",
			function()
				if MirMiniResDownMgr and MirMiniResDownMgr:getInstance().testAtlasRes then
					print("=======MirMiniResDownMgr:getInstance():testAtlasRes=========")
					print(MirMiniResDownMgr:getInstance():testAtlasRes())
					print("============================================================")
				end
			end
		},
		{
			"开关下载游戏文件校验",
			function()
				if MirLaunch then
					MirLaunch.checkGameAfterInstall = not MirLaunch.checkGameAfterInstall
				end
			end
		},
		{
			"开关下载游戏文件Md5校验",
			function()
				if MirLaunch then
					MirLaunch.checkMd5 = not MirLaunch.checkMd5
				end
			end
		}
	}

	self:createContentForSetting(config)
end

function debugNode:createContentForProfile()
	local config = {}

	if PlatformUtils:getInstance().luaProfileStart then
		config = {
			{
				"开关lua函数调用频率统计",
				function()
					g_data.luaSampling = not g_data.luaSampling

					tip("lua函数调用频率统计已" .. (g_data.luaSampling and "开启，采样完毕后需再次点击此开关关闭统计生成采样文件" or "关闭，正在生成采样文件，请勿关闭游戏"))

					if g_data.luaSampling then
						PlatformUtils:getInstance():luaProfileStart(0)
					else
						PlatformUtils:getInstance():luaProfileStop()
					end
				end
			},
			{
				"开关lua函数内存申请统计",
				function()
					g_data.luaAllocProfile = not g_data.luaAllocProfile

					tip("lua函数内存申请统计已" .. (g_data.luaAllocProfile and "开启，采样完毕后需再次点击此开关关闭统计生成采样文件" or "关闭，正在生成采样文件，请勿关闭游戏"))

					if g_data.luaAllocProfile then
						PlatformUtils:getInstance():luaProfileStart(1)
					else
						PlatformUtils:getInstance():luaProfileStop()
					end
				end
			},
			{
				"开关lua函数CPU时间占用统计",
				function()
					g_data.luaAllocProfile = not g_data.luaAllocProfile

					tip("lua函数CPU时间占用统计已" .. (g_data.luaAllocProfile and "开启，采样完毕后需再次点击此开关关闭统计生成采样文件" or "关闭，正在生成采样文件，请勿关闭游戏"))

					if g_data.luaAllocProfile then
						PlatformUtils:getInstance():luaProfileStart(2)
					else
						PlatformUtils:getInstance():luaProfileStop()
					end
				end
			},
			{
				"调用luaGC",
				function()
					collectgarbage("collect")
				end
			},
			{
				"调用tolua.fullgc",
				function()
					tolua.fullgc()
				end
			}
		}
	end

	self:createContentForSetting(config)
end

function debugNode:createContentForPlayerLog()
	local config = {
		{
			"开关玩家本人走跑砍间隔日志",
			function()
				g_data.openMoveLog = not g_data.openMoveLog

				tip("开关玩家本人走跑间隔日志：" .. (g_data.openMoveLog and "开启" or "关闭"))
			end
		},
		{
			"开关他人走跑砍协议日志",
			function()
				g_data.openOtherMoveLog = not g_data.openOtherMoveLog

				tip("开关他人走跑砍协议日志：" .. (g_data.openOtherMoveLog and "开启" or "关闭"))
			end
		},
		{
			"开关玩家全部动作日志",
			function()
				g_data.playerActLog = not g_data.playerActLog

				tip("开关玩家全部动作日志：" .. (g_data.playerActLog and "开启" or "关闭"))
			end
		},
		{
			"开关玩家攻击动作日志",
			function()
				g_data.playerActLog = not g_data.playerActLog

				tip("开关玩家攻击动作日志：" .. (g_data.playerActLog and "开启" or "关闭"))
			end
		},
		{
			"开关技能日志",
			function()
				g_data.openSkillLog = not g_data.openSkillLog

				tip("开关技能日志" .. (g_data.openSkillLog and "开启" or "关闭"))
			end
		},
		{
			"调整动作速度",
			function()
				self:createContentForAdaptSpeed()
			end
		},
		{
			"加速他人动作",
			function()
				g_data.speedUpOther = not g_data.speedUpOther

				tip("加速他人动作：" .. (g_data.speedUpOther and "开启" or "关闭"))
			end
		},
		{
			"开关动作实时播放",
			function()
				g_data.openRealTimeAction = not g_data.openRealTimeAction

				tip("动作播放修正已" .. (g_data.openRealTimeAction and "开启" or "关闭"))

				if g_data.openRealTimeAction then
					gLastPostTime = nil
				end
			end
		}
	}

	self:createContentForSetting(config)
end

function debugNode:createContentForException()
	local config = {
		{
			"触发内存警告",
			function()
				app:memoryWarning()
			end
		},
		{
			"测试lua error",
			function()
				local testLuaError

				testLuaError:func()
			end
		},
		{
			"测试崩溃",
			function()
				ycFunction:testCrash()
			end
		},
		{
			"操作被释放的cocos对象",
			function()
				local node = display.newNode()

				scheduler.performWithDelayGlobal(function()
					node:pos(display.cx, display.cy)
				end, 0)
				node:pos(display.cx, display.cy)
			end
		},
		{
			"测试创建角色超时",
			function()
				g_data.roleCreateTest = not g_data.roleCreateTest

				tip("创建角色超时开关已" .. (g_data.roleCreateTest and "开启，创建角色时将超时无法创建成功" or "关闭，可正常创建角色"))
			end
		},
		{
			"打印红点树",
			function()
				if g_data.badgeSystem and g_data.badgeSystem._deepTree then
					dump(g_data.badgeSystem._deepTree, "BadgeTree:")
				end
			end
		}
	}

	self:createContentForSetting(config)
end

function debugNode:createContentForSetting(cfg)
	self:createContentBase()

	local config = cfg or {
		{
			"隐藏工具图标",
			function()
				if m2debug.node then
					m2debug.node:removeSelf()

					m2debug.node = nil
				end

				m2debug.hideNode = true
			end
		},
		{
			"调试信息开关",
			function()
				self:createContentForSettingShows()
			end
		},
		{
			"辅助模拟测试",
			function()
				self:createContentForTest()
			end
		},
		{
			"玩家动作测试",
			function()
				self:createContentForPlayerLog()
			end
		},
		{
			"客户端性能测试",
			function()
				self:createContentForProfile()
			end
		},
		{
			"客户端异常测试",
			function()
				self:createContentForException()
			end
		},
		{
			"开关省电模式",
			function()
				if main_scene and g_data.setting and g_data.setting.base then
					g_data.setting.base.operateCheck = not g_data.setting.base.operateCheck

					if not g_data.setting.base.operateCheck then
						main_scene:setNormalFPS()
					else
						main_scene.touchStamp = socket.gettime()
					end

					tip("省电模式已" .. (g_data.setting.base.operateCheck and "开启，一段时间内无操作帧率将自动降低" or "关闭"))
				else
					tip("省电模式需进入主场景后可用")
				end
			end
		},
		{
			"GM面板",
			function()
				if main_scene and main_scene.ui then
					main_scene.ui:togglePanel("quickTest")
				else
					tip("GM面板需进入主场景后可用")
				end
			end
		},
		{
			"控制面板",
			function()
				if main_scene and main_scene.ui then
					main_scene.ui:togglePanel("coreManager")
				end
			end
		}
	}
	local pos = self.content:geth() - 40
	local left = 20
	local maxWidth = 0

	for i, v in ipairs(config) do
		local edit
		local lb = an.newLabel(v[1], 22, 0, {
			color = cc.c3b(255, 255, 0)
		}):pos(left, pos):add2(self.content):addUnderline(cc.c3b(255, 255, 0)):enableClick(function()
			if not v or not v[2] then
				return
			end

			v[2](edit)
		end, {
			ani = true
		})

		if v[3] then
			-- block empty
		end

		maxWidth = math.max(lb:getw(), maxWidth)
		pos = pos - 45

		if pos < 0 then
			left = left + maxWidth + 20
			maxWidth = 0
			pos = self.content:geth() - 40
		end
	end

	an.newBtn(res.gettex2("pic/scale/scale2.png"), function()
		self:createContent()
	end, {
		pressBig = true,
		scale9 = cc.size(80, 40),
		label = {
			"返回",
			18,
			1,
			{
				color = cc.c3b(255, 255, 0)
			}
		}
	}):add2(self.content):pos(self.content:getw() - 50, 30)
end

function debugNode:createContentForSettingShows()
	self:createContentBase()
	self.content:setNodeEventEnabled(true)

	function self.content.onCleanup()
		cache.saveDebug("shows", m2debug.showEnables)
	end

	local cnt = 0

	local function add(key, func)
		local col = cnt % 3
		local line = math.modf(cnt / 3)
		local pos = cc.p(20 + col * 160, self.content:geth() - 40 - line * 60)
		local toggle = an.newToggle(res.gettex2("pic/common/toggle10.png"), res.gettex2("pic/common/toggle11.png"), function(b)
			m2debug.showEnables[key] = b

			shows[key](b)
		end, {
			easy = true,
			default = m2debug.showEnables[key],
			label = {
				key,
				20,
				1,
				{
					color = self:getColor(key)
				}
			}
		}):anchor(0, 0.5):pos(pos.x, pos.y):add2(self.content)

		cnt = cnt + 1
	end

	for k, v in pairs(shows) do
		add(k)
	end

	an.newBtn(res.gettex2("pic/scale/scale2.png"), function()
		self:createContent()
	end, {
		pressBig = true,
		scale9 = cc.size(80, 40),
		label = {
			"返回",
			18,
			1,
			{
				color = cc.c3b(255, 255, 0)
			}
		}
	}):add2(self.content):pos(self.content:getw() - 50, 30)
end

function debugNode:createContentForSettingServer()
	self:createContentBase()
	an.newLabel("服务器IP: ", 22, 1, {
		color = cc.c3b(0, 255, 0)
	}):anchor(0, 0.5):pos(20, self.content:geth() - 50):add2(self.content)

	local input, areaInput

	input = an.newInput(130, self.content:geth() - 52, 170, 32, 15, {
		label = {
			"",
			22,
			1
		},
		bg = {
			h = 40,
			tex = res.gettex2("pic/scale/scale2.png"),
			offset = {
				-10,
				0
			}
		},
		start_call = function()
			areaInput:stopInput()
		end
	}):anchor(0, 0.5):add2(self.content)

	an.newLabel("区服ID: ", 22, 1, {
		color = cc.c3b(0, 255, 0)
	}):anchor(0, 0.5):pos(300, self.content:geth() - 50):add2(self.content)

	areaInput = an.newInput(390, self.content:geth() - 52, 90, 32, 6, {
		label = {
			"",
			22,
			1
		},
		bg = {
			h = 40,
			tex = res.gettex2("pic/scale/scale2.png"),
			offset = {
				-10,
				0
			}
		},
		start_call = function()
			input:stopInput()
		end
	}):anchor(0, 0.5):add2(self.content)

	an.newBtn(res.gettex2("pic/scale/scale2.png"), function()
		local function tip(text)
			self:createContentForTips(text, function()
				self:createContentForSetting()
			end)
		end

		input:stopInput()
		areaInput:stopInput()

		local ip = input:getText()
		local nums = string.split(ip, ".")

		if #nums ~= 4 then
			return tip("不是有效的ip地址")
		end

		for i, v in ipairs(nums) do
			local num = tonumber(v)

			if not num or num > 255 or num < 0 then
				return tip("不是有效的ip地址")
			end
		end

		local areaid = areaInput:getText()

		if areaid == "" then
			return tip("不是有效的区服id")
		end

		local function checkHistory(key, value)
			if not m2debug.setting[key] then
				m2debug.setting[key] = {}
			end

			local has

			for i, v in ipairs(m2debug.setting[key]) do
				if v == value then
					table.remove(m2debug.setting[key], i)

					break
				end
			end

			table.insert(m2debug.setting[key], 1, value)
		end

		checkHistory("ip_history", ip .. "-" .. areaid)
		cache.saveDebug("setting", m2debug.setting)
		MirLaunch:restartLaunch()
	end, {
		pressBig = true,
		scale9 = cc.size(80, 40),
		label = {
			"确定",
			18,
			1,
			{
				color = cc.c3b(255, 255, 0)
			}
		}
	}):add2(self.content):pos(self.content:getw() - 150, 30)
	an.newBtn(res.gettex2("pic/scale/scale2.png"), function()
		self:createContentForSetting()
	end, {
		pressBig = true,
		scale9 = cc.size(80, 40),
		label = {
			"返回",
			18,
			1,
			{
				color = cc.c3b(255, 255, 0)
			}
		}
	}):add2(self.content):pos(self.content:getw() - 50, 30)
end

function debugNode:createContentForTips(text, func)
	self:createContentBase()
	an.newLabel(text, 22, 1, {
		color = cc.c3b(0, 255, 0)
	}):anchor(0.5, 0.5):pos(self.content:centerPos()):add2(self.content)
	an.newBtn(res.gettex2("pic/scale/scale2.png"), func or function()
		self:createContent()
	end, {
		pressBig = true,
		scale9 = cc.size(80, 40),
		label = {
			"返回",
			18,
			1,
			{
				color = cc.c3b(255, 255, 0)
			}
		}
	}):add2(self.content):pos(self.content:getw() - 50, 30)
end

function debugNode:createContentForGMCmd()
	self:createContentBase()

	local scroll = self:createCmdList("common")

	an.newLabel("命令类别", 18, 1, {
		color = display.COLOR_RED
	}):addTo(scroll):pos(10, scroll.h):anchor(0, 1)

	local h = scroll.h - scroll.space
	local cnt = 1

	an.newBtn(res.gettex2("pic/scale/scale2.png"), function()
		self:createContent()
	end, {
		pressBig = true,
		scale9 = cc.size(80, 40),
		label = {
			"返回",
			18,
			1,
			{
				color = cc.c3b(255, 255, 0)
			}
		}
	}):add2(self.content):pos(self.content:getw() - 50, 30)
end

function debugNode:createCmdList(key)
	self:createContentBase()

	local scroll = an.newScroll(6, 6, self.content:getw() - 16, self.content:geth() - 12, {
		labelM = {
			18,
			0
		}
	}):anchor(0, 0):addTo(self.content)

	scroll.h = scroll:geth() - 5
	scroll.space = 30

	return scroll
end

function debugNode:createCmd(data, func)
	self:createContentBase()
	self.content:setNodeEventEnabled(true)

	function self.content.onCleanup()
		m2debug.catchNode = nil
	end

	local scroll = an.newScroll(6, 6, self.content:getw() - 16, self.content:geth() - 12, {
		labelM = {
			18,
			0
		}
	}):anchor(0, 0):addTo(self.content)

	dump(data)

	local w, h, sw, sh = 10, scroll:geth() - 10, 150, 40

	an.newLabelM(self.content:getw() - 20, 20, 1):addTo(scroll):pos(w, h):anchor(0, 1):nextLine():addLabel("命令描述: " .. data[2])

	h = h - sh

	local edits = {}
	local needCatch = false
	local mapEdit

	if data[4] ~= "" then
		local t = loadstring("return " .. data[4])

		for i, v in ipairs(t()) do
			local opt, edit

			opt = an.newLabel(v, 20, 1):addTo(scroll):pos(10, h):anchor(0, 1)
			edit = an.newInput(0, 0, 120, 35, 255, {
				donotClip = true,
				bg = {
					h = 35,
					tex = res.gettex2("pic/scale/edit.png"),
					offset = {
						-10,
						0
					}
				}
			}):addTo(scroll):pos(30 + opt:getw(), h):anchor(0, 1)
			edits[#edits + 1] = edit

			if string.find(v, "角色名") or string.find(v, "怪物名") then
				needCatch = true

				an.newBtn(res.gettex2("pic/scale/scale2.png"), function()
					print("catch name ", m2debug.catchName)
					edit:setText(m2debug.catchName and m2debug.catchName or "")
				end, {
					pressBig = true,
					scale9 = cc.size(80, 40),
					label = {
						"获取名字",
						18,
						1,
						{
							color = cc.c3b(255, 255, 0)
						}
					}
				}):add2(scroll):pos(edit:getPositionX() + edit:getw() + 30, h):anchor(0, 1)
			end

			if string.find(v, "地图ID") then
				mapEdit = edit
			end

			h = h - sh
		end
	end

	local options = {}
	local selected

	if data[5] ~= "" then
		local t = loadstring("return " .. data[5])

		for i, v in ipairs(t()) do
			local opt

			opt = an.newBtn(res.gettex2("pic/common/toggle10.png"), function(btn)
				for _, tog in ipairs(options) do
					if tog == btn then
						tog:select()

						selected = v
					else
						tog:unselect()
					end
				end
			end, {
				manual = true,
				label = {
					v,
					20,
					1,
					{
						color = def.colors.btn20,
						sc = def.colors.btn20s
					}
				},
				labelOffset = {
					x = 50,
					y = 0
				},
				select = {
					res.gettex2("pic/common/toggle11.png")
				}
			}):addTo(scroll):anchor(0, 1)
			options[#options + 1] = opt

			opt:pos((#options + 2) % 3 * sw + w, h)

			if i == 1 then
				opt:select()

				selected = v
			end
		end
	end

	if mapEdit then
		local mapCfg = {
			["0"] = "比奇省",
			sldg = "边界城",
			["2"] = "毒蛇山谷",
			["3"] = "盟重省",
			["11"] = "白日门",
			["6"] = "魔龙城",
			["5"] = "苍月岛",
			["1"] = "沃玛森林",
			["4"] = "封魔谷"
		}
		local cnt = 1

		for i, v in pairs(mapCfg) do
			an.newBtn(res.gettex2("pic/scale/scale2.png"), function()
				mapEdit:setText(i)
			end, {
				pressBig = true,
				scale9 = cc.size(80, 40),
				label = {
					v,
					20,
					1,
					{
						color = cc.c3b(255, 255, 0)
					}
				}
			}):add2(scroll):pos((cnt - (cnt > 5 and 6 or 1)) * 90, h):anchor(0, 1)

			cnt = cnt + 1
			h = cnt == 6 and h - sh or h
		end
	end

	if needCatch then
		self.catchNode = an.newToggle(res.gettex2("pic/common/toggle10.png"), res.gettex2("pic/common/toggle11.png"), function(b)
			m2debug.catch = b
		end, {
			easy = true,
			default = m2debug.catch,
			label = {
				"允许获取",
				20,
				1
			}
		}):addTo(scroll):pos(10, 24):anchor(0, 0.5)
	end

	an.newBtn(res.gettex2("pic/scale/scale2.png"), function()
		local str = "@" .. data[3]

		for i, v in ipairs(edits) do
			if v:getText() ~= "" then
				str = str .. " " .. v:getText()
			end
		end

		if selected then
			str = str .. " " .. selected
		end

		local function encodeMsg(str)
			local ret = {}

			if str then
				str = utf8strs(str)

				for i, v in ipairs(str) do
					if string.len(v) >= 4 then
						local t = crypto.encodeBase64(v)

						t = string.sub(t, 1, string.len(t) - 1)
						t = string.gsub(t, "/", "!")
						ret[i] = "{#ej" .. t .. "}"
					else
						ret[i] = v
					end
				end
			end

			return table.concat(ret)
		end

		common.sendGMCmd(encodeMsg(str))
	end, {
		pressBig = true,
		scale9 = cc.size(80, 40),
		label = {
			"确定",
			18,
			1,
			{
				color = cc.c3b(255, 255, 0)
			}
		}
	}):add2(scroll):pos(scroll:getw() - 150, 24)
	an.newBtn(res.gettex2("pic/scale/scale2.png"), func, {
		pressBig = true,
		scale9 = cc.size(80, 40),
		label = {
			"返回",
			18,
			1,
			{
				color = cc.c3b(255, 255, 0)
			}
		}
	}):add2(scroll):pos(scroll:getw() - 40, 24)
end

function debugNode:getColor(tag)
	if tag == "error" or tag == "assert" then
		return display.COLOR_RED
	end

	return display.COLOR_GREEN
end

function debugNode:addLog(tag, str)
	if not self.content or self.content.type ~= "main" then
		return
	end

	local scroll = self.content.scroll
	local x, y = scroll:getScrollOffset()
	local isInEnd = y + scroll:geth() + scroll.labelM.wordSize.height > scroll:getScrollSize().height

	scroll.labelM:nextLine():addLabel("[ " .. tag .. " ] ", self:getColor(tag)):addLabel(str)

	if isInEnd then
		scroll:setScrollOffset(0, scroll:getScrollSize().height - scroll:geth())
	else
		self:showNewMark()
	end

	return true
end

function debugNode:showNewMark()
	if not self.content.newMark then
		self.content.newMark = res.get2("pic/common/msgNew.png"):add2(self.content, 1):run(cc.RepeatForever:create(transition.sequence({
			cc.ScaleTo:create(0.5, 0.7),
			cc.ScaleTo:create(0.5, 1)
		}))):enableClick(function()
			self.content.newMark:hide()
			self.content.scroll:setScrollOffset(0, self.content.scroll:getScrollSize().height - self.content.scroll:geth())
		end)
	end

	self.content.newMark:show():pos(self.content:getw() - 20, 24)
end

function debugNode:hideNewMark()
	if self.content.newMark then
		self.content.newMark:hide()
	end
end

function m2debug.listenOnTcp(port)
	local console = cc.Director:getInstance():getConsole()

	console:stop()
	scheduler.performWithDelayGlobal(function()
		console:listenOnTCP(port)
		console:addCommand({
			help = "execute lua script",
			name = "l"
		}, function(fd, args)
			if type(args) == "string" then
				scheduler.performWithDelayGlobal(function()
					local func, err = loadstring(args)

					if err then
						print(err)
					else
						func()
					end
				end, 0)
			end
		end)
		console:addCommand({
			help = "use mir2 say",
			name = "say"
		}, function(fd, args)
			scheduler.performWithDelayGlobal(function()
				local args = ycFunction:a2u(args, string.len(args))

				args = string.trim(args)

				print(args)
				common.sendGMCmd(args)
			end, 0)
		end)
		console:addCommand({
			help = "fuck everything",
			name = "fuck"
		}, function(fd, str)
			scheduler.performWithDelayGlobal(function()
				local data = json.decode(str)
				local cmdName = ""
				local taskid = data.taskid
				local params = {}

				for k, v in pairs(data) do
					if k == "name" then
						cmdName = v
					else
						params[k] = v
					end
				end

				local retTable = common.executeFuckCmd(cmdName, params)

				if retTable and taskid then
					local ret = {
						taskid = taskid,
						ret = retTable
					}

					console:send(fd, "fuck " .. json.encode(ret))
				end
			end, 0)
		end)
	end, 0)
end

M2DEBUG_INIT = true

return m2debug
