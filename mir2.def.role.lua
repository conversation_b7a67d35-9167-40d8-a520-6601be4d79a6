-- chunkname: @mir2\\def\\role.lua

local role = {}

table.merge(role, import(".state"))

role.humFrame = 600
role.size = {
	w = 50,
	h = 85
}
role.speed = {
	fast = 0.1,      -- 原值0.2，改小=更快移动
	attack = 0.3,    -- 原值0.9，改小=攻击更快
	spell = 0.4,     -- 原值0.8，改小=施法更快
	rush = 0.15,     -- 原值0.3，改小=冲刺更快
	rushKung = 0.15, -- 原值0.3，改小=功夫冲刺更快
	normal = 0.2     -- 原值0.6，改小=普通移动更快
}
role.speedOtherPlayer = {
	fast = 0.1,      -- 原值0.18，其他玩家快速移动
	attack = 0.3,    -- 原值0.84，其他玩家攻击速度
	spell = 0.4,     -- 原值0.74，其他玩家施法速度
	rush = 0.15,     -- 原值0.28，其他玩家冲刺速度
	rushKung = 0.15, -- 原值0.28，其他玩家功夫冲刺
	normal = 0.2     -- 原值0.57，其他玩家普通移动
}
role.state = {}
role.dir = {
	leftUp = 7,
	rightUp = 1,
	left = 6,
	up = 0,
	leftBottom = 5,
	rightBottom = 3,
	bottom = 4,
	right = 2,
	_0 = {
		0,
		-1
	},
	_1 = {
		1,
		-1
	},
	_2 = {
		1,
		0
	},
	_3 = {
		1,
		1
	},
	_4 = {
		0,
		1
	},
	_5 = {
		-1,
		1
	},
	_6 = {
		-1,
		0
	},
	_7 = {
		-1,
		-1
	}
}
role.namecolorcnt = 9
role.namecolors = {
	249,
	216,
	250,
	252,
	253,
	255,
	152,
	149,
	70
}

function role.getConfig(key, changeSkinState)
	local configpath = "config/"

	if g_data.login:isChangeSkinCheckServer() and changeSkinState then
		configpath = g_data.login:getChkConfigPath()

		local file = io.readfile(cc.FileUtils:getInstance():fullPathForFilename(configpath .. key .. ".json"))

		if not file then
			return parseJson(configpath .. key .. ".json") or {}
		end

		local config = json.decode(file)

		return config
	end

	return parseJson(configpath .. key .. ".json") or {}
end

function role.init()
	role.monster = {}
	role.npc = {}
	role.frame = {}
	role.heroDress = {}
	role.heroHorse = {}
	role.heroWeapon = {}
	role.heroHp = {}
	role.heroWeaponEffect = {}
	role.stands = {}
	role.hair_pos = {}
	role.monEffect = {}
	role.godAspects = {}
	role.heroWing = {}

	for _, info in ipairs(role.getConfig("dress", true)) do
		role.heroDress[info.uid] = info
	end

	for _, info in ipairs(role.getConfig("horse")) do
		role.heroHorse[info.uid] = info
	end

	for _, info in ipairs(role.getConfig("weapon")) do
		role.heroWeapon[info.Id] = info
	end

	for _, info in ipairs(role.getConfig("weapon_ef")) do
		role.heroWeaponEffect[info.Id] = info
	end

	for _, info in ipairs(role.getConfig("hp")) do
		role.heroHp[info.level] = info
	end

	for _, info in ipairs(role.getConfig("monster", true)) do
		role.monster[info.id] = info
	end

	for _, info in ipairs(role.getConfig("npc")) do
		role.npc[info.id] = info
	end

	for _, info in ipairs(role.getConfig("frame", true)) do
		if not role.frame[info.id] then
			role.frame[info.id] = {}
		end

		role.frame[info.id][info.state] = info
	end

	for _, info in ipairs(role.getConfig("hair_pos")) do
		role.hair_pos[info.index] = {
			info.x,
			info.y
		}
	end

	for _, info in ipairs(role.getConfig("monEffect")) do
		role.monEffect[info.monsterId] = info
	end

	for _, info in ipairs(role.getConfig("godAspects")) do
		role.godAspects[info.id] = info
	end

	for _, info in ipairs(role.getConfig("wing", false)) do
		role.heroWing[info.uid] = info
	end
end

function role.hp2level(hp, job)
	local zstr, dstr, fstr
	local str = ""

	for lv, info in pairs(role.heroHp) do
		if info.z_hp == tonumber(hp) then
			zstr = "Z" .. lv

			if job and job == 0 then
				return zstr
			end
		end

		if info.d_hp == tonumber(hp) then
			dstr = "D" .. lv

			if job and job == 2 then
				return dstr
			end
		end

		if info.f_hp == tonumber(hp) then
			fstr = "M" .. lv

			if job and job == 1 then
				return fstr
			end
		end
	end

	if zstr then
		str = str .. zstr
	end

	if dstr then
		if zstr then
			str = str .. "/"
		end

		str = str .. dstr
	end

	if fstr then
		if dstr or zstr then
			str = str .. "/"
		end

		str = str .. fstr
	end

	if str == "" then
		str = "?"
	end

	return str
end

function role.getHeroDress(dressid)
	return role.heroDress[dressid] or {}
end

function role.getHeroWeapon(weapon)
	return role.heroWeapon[weapon] or {}
end

function role.getHeroWeaponEffect(weapon)
	return role.heroWeaponEffect[weapon] or {}
end

function role.getHeroHorse(horseid)
	return role.heroHorse[horseid]
end

function role.getMonEffect(monsterId)
	return role.monEffect[monsterId] or nil
end

function role.getGodAspects(id)
	return role.godAspects[id] or nil
end

function role.getHeroWing(id)
	return role.heroWing[id] or {}
end

function role.hair(feature)
	if feature.sex == 0 then
		local hairs = {
			{
				"hair",
				0
			},
			{
				"hair",
				4
			},
			{
				"hair2",
				6
			},
			{
				"hair2",
				7
			},
			{
				"hair5",
				14
			},
			{
				"hair5",
				22
			},
			{
				"hair5",
				24
			},
			{
				"hair5",
				16
			},
			{
				"hair5",
				26
			},
			{
				"hair5",
				18
			},
			{
				"hair5",
				12
			},
			{
				"hair5",
				10
			},
			{
				"hair5",
				20
			}
		}
		local ret = hairs[feature.hair + 1] or {
			"hair",
			0
		}

		return unpack(ret)
	else
		local hairs = {
			{
				"hair",
				3
			},
			{
				"hair",
				5
			},
			{
				"hair2",
				6
			},
			{
				"hair2",
				7
			},
			{
				"hair5",
				15
			},
			{
				"hair5",
				23
			},
			{
				"hair5",
				25
			},
			{
				"hair5",
				17
			},
			{
				"hair5",
				27
			},
			{
				"hair5",
				19
			},
			{
				"hair5",
				13
			},
			{
				"hair5",
				11
			},
			{
				"hair5",
				21
			}
		}
		local ret = hairs[feature.hair + 1] or {
			"hair",
			0
		}

		return unpack(ret)
	end
end

function role.getMoveDir(destx, desty, x, y)
	local offX = x - destx
	local offY = y - desty
	local angle = math.atan(offY / offX)

	if angle <= math.pi / 8 and angle > -math.pi / 8 then
		if offX > 0 then
			return role.dir.right
		else
			return role.dir.left
		end
	elseif angle < math.pi * 3 / 8 and angle > math.pi / 8 then
		if offX > 0 then
			return role.dir.rightBottom
		else
			return role.dir.leftUp
		end
	elseif offX == 0 or angle >= math.pi * 3 / 8 or angle < -math.pi * 3 / 8 then
		if offY > 0 then
			return role.dir.bottom
		elseif offY == 0 then
			return
		else
			return role.dir.up
		end
	elseif angle <= -math.pi / 8 and angle > -math.pi * 3 / 8 then
		if offY < 0 then
			return role.dir.rightUp
		else
			return role.dir.leftBottom
		end
	end

	return role.dir.bottom
end

function role.getAttackDir(destx, desty, x, y)
	local disx, disy = math.abs(x - destx), math.abs(y - desty)
	local dir

	if disx <= 1 then
		dir = y < desty and role.dir.up or role.dir.bottom
	elseif x < destx then
		if disy <= 1 then
			dir = role.dir.left
		elseif y < desty then
			dir = role.dir.leftUp
		else
			dir = role.dir.leftBottom
		end
	elseif destx < x then
		if disy <= 1 then
			dir = role.dir.right
		elseif y < desty then
			dir = role.dir.rightUp
		else
			dir = role.dir.rightBottom
		end
	end

	return dir
end

function role.getMonster(monsterId)
	if not role.monster[monsterId] then
		p2("error", "monster:" .. monsterId .. " is nil")

		return {}
	end

	return role.monster[monsterId]
end

function role.getOffset(monsterId)
	if not role.monster[monsterId] then
		p2("error", "monster:" .. monsterId .. " is nil")

		return 0
	end

	if not role.monster[monsterId].imgIdx then
		p2("error", "monster:" .. monsterId .. " img offset is nil")

		return 0
	end

	return role.monster[monsterId].imgIdx
end

function role.getMonImg(monsterId)
	if not role.monster[monsterId] then
		p2("error", "monster:" .. monsterId .. " is nil")

		return "?"
	end

	if not role.monster[monsterId].img then
		p2("error", "monster:" .. monsterId .. " img is nil")

		return "?"
	end

	return role.monster[monsterId].img
end

function role.getNpc(npdId)
	if not role.npc[npdId] then
		p2("error", "npc:" .. npdId .. " is nil")

		return {}
	end

	return role.npc[npdId]
end

function role.getNpcOffset(npdId)
	if not role.npc[npdId] then
		p2("error", "npc:" .. npdId .. " is nil")

		return 0
	end

	if not role.npc[npdId].imgIdx then
		p2("error", "npc:" .. npdId .. " img offset is nil")

		return 0
	end

	return role.npc[npdId].imgIdx
end

function role.getRoleId(race, appr)
	return race * 1000 + appr
end

function role.getFrame(roleId)
	if not role.frame[roleId] then
		p2("error", "role:" .. roleId .. " frame config is nil")

		return {}
	end

	return role.frame[roleId]
end

function role.getDressFrame(roleId)
	if not role.frame[roleId] then
		p2("error", "role:" .. roleId .. " frame config is nil")

		return {}
	end

	local dressFrame = {}

	for state, info in pairs(role.frame[roleId]) do
		if info.dress then
			dressFrame[state] = info.dress
		end
	end

	return dressFrame
end

function role.changeStandFrame(roleId, i_type, i_state)
	local standInfo = role.stands[roleId] or {}

	role.stands[roleId] = standInfo

	if i_type == "dress" then
		local info = role.frame[roleId][i_state]

		if info and info.dress then
			if not standInfo.dress then
				standInfo.dress = role.frame[roleId].stand.dress
			end

			role.frame[roleId].stand.dress = info.dress
		end
	elseif i_type == "hair" then
		local info = role.frame[roleId][i_state]

		if info and info.hair then
			if not standInfo.hair then
				standInfo.hair = role.frame[roleId].stand.hair
			end

			role.frame[roleId].stand.hair = info.hair
		end
	end
end

function role.resetRoleStandFrame(roleId)
	local standInfo = role.stands[roleId]

	if standInfo then
		if standInfo.dress then
			role.frame[roleId].stand.dress = standInfo.dress
			standInfo.dress = nil
		end

		if standInfo.hair then
			role.frame[roleId].stand.hair = standInfo.hair
			standInfo.hair = nil
		end

		if not standInfo.dress and not standInfo.hair then
			role.stands[roleId] = nil
		end
	end
end

function role.resetRoleFrame(roleId)
	for _, info in ipairs(role.getConfig("frame")) do
		if info.id == roleId then
			role.frame[info.id][info.state] = info
		end
	end
end

function role.getHairFrame(roleId)
	if not role.frame[roleId] then
		p2("error", "role:" .. roleId .. " frame config is nil")

		return nil
	end

	local hairFrame

	for state, info in pairs(role.frame[roleId]) do
		if info.hair then
			hairFrame = hairFrame or {}
			hairFrame[state] = info.hair
		end
	end

	return hairFrame
end

function role.getRoleHairPos(hair, params)
	params = params or {}

	local x, y = params.x, params.y
	local pos = role.hair_pos[hair]

	if not pos then
		return params.x or 139, params.y or 240
	end

	return unpack(pos)
end

return role
