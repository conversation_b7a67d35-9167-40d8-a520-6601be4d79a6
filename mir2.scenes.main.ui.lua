-- chunkname: @mir2\\scenes\\main\\ui.lua

local current = ...

common = import(".common.common")

local settingLogic = import(".common.settingLogic")
local panelDelegate = import(".panel._delegate")
local itemUse = import(".common.itemUse")

panelHelper = import(".common.panelHelper")
attributeHelper = import(".common.attributeHelper")

local magic = import(".common.magic")

item = import(".common.item")
JsonParse = require("cjson")

local quickJson = require("json")
local mainui = class("mainui", function()
	return display.newNode()
end)
local ui_btnEx

table.merge(mainui, {
	z = {
		focus = 1,
		centerTopTip = 4,
		leftTopTip = 10,
		waiting = 10,
		chatChannel = 9,
		textInfo = 2,
		voiceTip = 5,
		replaceAsk = 7,
		diyBtn = 6,
		detail = 8
	},
	delayLabel_list = {},
	tTime = {},
	timeLimitWidgets = {
		weChatRedPacket = {
			enable = false
		},
		weChatGift = {
			enable = false
		},
		focusVIPWechat = {
			enable = false
		},
		cruiseMerchant = {
			enable = false
		},
		btnWoC = {
			enable = false
		},
		btnGodMonsterItem = {
			enable = false
		},
		btnSeaWarArm = {
			enable = false
		}
	},
	globleTimeLabel = {
		state = 0
	},
	newActivityRedLists = {},
	bossHPBar = {}
})

function mainui:bindMsg()
	self.relationBindId1 = MirTcpClient:getInstance():subscribeOnProtocol(SM_ClientFriendRelationList, g_data.relation.onSM_ClientFriendRelationList)
	self.relationBindId2 = MirTcpClient:getInstance():subscribeOnProtocol(SM_ClientFriendRelation, g_data.relation.onSM_ClientFriendRelation)
	self.relationBindId3 = MirTcpClient:getInstance():subscribeOnProtocol(SM_ClientAttentionRelationList, g_data.relation.onSM_ClientAttentionRelationList)
	self.relationBindId4 = MirTcpClient:getInstance():subscribeOnProtocol(SM_ClientAttentionRelation, g_data.relation.onSM_ClientAttentionRelation)
	self.relationBindId5 = MirTcpClient:getInstance():subscribeOnProtocol(SM_ClientNormBlackListRelationList, g_data.relation.onSM_ClientNormBlackListRelationList)
	self.relationBindId6 = MirTcpClient:getInstance():subscribeOnProtocol(SM_ClientNormBlackListRelation, g_data.relation.onSM_ClientNormBlackListRelation)
	self.relationBindId7 = MirTcpClient:getInstance():subscribeOnProtocol(SM_RelationMemberOnline, g_data.relation.onSM_RelationMemberOnline)
	self.relationBindId8 = MirTcpClient:getInstance():subscribeOnProtocol(SM_RelationMemberOffline, g_data.relation.onSM_RelationMemberOffline)

	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_GlobalSet, self, self.onSM_GlobalSet)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_Ability, self, self.onSM_Ability)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_DrumLevel, self, self.onSM_DrumLevel)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_MilitaryRankLv, self, self.onSM_MilitaryRankLv)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_AddDelRelation, self, self.onSM_AddDelRelation)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_NotifyGroupMessage, self, self.onSM_NotifyGroupMessage)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_UpdateRelationColor, self, self.onSM_UpdateRelationColor)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_SAY, self, self.onSM_SAY)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_QueryFocusItem, self, self.onSM_QueryFocusItem)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_SYSMESSAGE, self, self.onSM_SYSMESSAGE)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_CreateGroup, self, self.onSM_CreateGroup)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_AddGroupMember, self, self.onSM_AddGroupMember)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_DelGroupMember, self, self.onSM_DelGroupMember)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_JoinGroup, self, self.onSM_JoinGroup)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_ClientGroupMemInfoList, self, self.onSM_ClientGroupMemInfoList)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_GroupMode, self, self.onSM_GroupMode)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_ClientNearbyGroupInfoList, self, self.onSM_ClientNearbyGroupInfoList)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_GroupCancel, self, self.onSM_GroupCancel)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_GroupMemsPositionList, self, self.onSM_GroupMemsPositionList)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_QUERY_MAIL_LIST, self, self.onSM_QUERY_MAIL_LIST)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_QUERY_MAIL_INFO, self, self.onSM_QueryMailInfo)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_DISPOSE_MAIL, self, self.onSM_DisposeMail)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_HAVE_NEW_MAI, self, self.onSM_HAVE_NEW_MAI)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_HAVE_NEW_GIFT, self, self.onSM_HAVE_NEW_GIFT)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_GET_ALL_MAILITEMS, self, self.onSM_GET_ALL_MAILITEM)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_NOREAD_MAIL_COUNT, self, self.onSM_NOREAD_MAIL_COUNT)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_CanGet_CashGift_COUNT, self, self.onSM_CanGet_CashGift_COUNT)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_SendUseItems, self, self.onSM_SendUseItems)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_MYMAGICLIST, self, self.onSM_MYMAGICLIST)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_MERCHANT_SAY, self, self.onSM_MERCHANT_SAY)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_MERCHANT_DLG_CLOSE, self, self.onSM_MERCHANT_DLG_CLOSE)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_TAKEON_OK, self, self.onSM_TAKEON_OK)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_TAKEON_FAIL, self, self.onSM_TAKEON_FAIL)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_TAKEOFF_OK, self, self.onSM_TAKEOFF_OK)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_TakeOnHorseEquip, self, self.onSM_TakeOnHorseEquip)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_TakeOffHorseEquip, self, self.onSM_TakeOffHorseEquip)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_HorseEquipInfo, self, self.onSM_HorseEquipInfo)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_TAKEOFF_FAIL, self, self.onSM_TAKEOFF_FAIL)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_ADDITEM, self, self.onSM_ADDITEM)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_DelItem, self, self.onSM_DelItem)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_DELITEMS, self, self.onSM_DELITEMS)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_UPDATEITEM, self, self.onSM_UPDATEITEM)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_BAGITEMS, self, self.onSM_BAGITEMS)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_DROPITEM_FAIL, self, self.onSM_DROPITEM_FAIL)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_UP_WEAPON, self, self.onSM_UP_WEAPON)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_UPWEAPON_UI, self, self.onSM_UPWEAPON_UI)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_UPDATE_CLOTHES, self, self.onSM_UPDATE_CLOTHES)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_ENHANCE_NECKLACE, self, self.onSM_ENHANCE_NECKLACE)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_FLY_SHOE, self, self.onSM_FLY_SHOE)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_RANKING_LIST, self, self.onSM_RANKING_LIST)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_LEVELUP, self, self.onSM_LEVELUP)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_Vality, self, self.onSM_Vality)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_VExpToBeConverted, self, self.onSM_VExpToBeConverted)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_WinExp, self, self.onSM_WINEXP)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_TaskAll, self, self.onSM_TaskAll)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_TaskDetail, self, self.onSM_TaskDetail)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_TaskDelete, self, self.onSM_TaskDelete)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_TaskDialog, self, self.onSM_TaskDialog)
	g_data.eventDispatcher:addListener("NOW_DEATH", self, self.handleDeath)
	g_data.eventDispatcher:addListener("NOW_RELIVE", self, self.handleRelive)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_CHGNAMECOLOR, self, self.onSM_CHGNAMECOLOR)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_NORMAL_CRYSTAL_USE, self, self.onSM_NORMAL_CRYSTAL_USE)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_YB_CRYSTAL_USE, self, self.onSM_YB_CRYSTAL_USE)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_ITEM_PILEUP_RESULT, self, self.onSM_ITEM_PILEUP_RESULT)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_ITEMDURACHG, self, self.onSM_ITEMDURACHG)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_QueryEnergyCrystalInfo, self, self.onSM_QueryEnergyCrystalInfo)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_QueryGetEnergyCrystal, self, self.onSM_QueryGetEnergyCrystal)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_COMPOSE_DRESS, self, self.onSM_COMPOSE_DRESS)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_USER_INFO, self, self.onSM_USER_INFO)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_EAT, self, self.onSM_EAT)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_SENDUSERREPAIR, self, self.onSM_SENDUSERREPAIR)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_Act_Detail, self, self.onSM_Act_Detail)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_BossBookSendAll, self, self.onSM_BossBookSendAll)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_BossBookFly, self, self.onSM_BossBookFly)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_MONEY_INFO, self, self.onSM_MONEY_INFO)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_VITALITYITEM, self, self.onSM_VITALITYITEM)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_AddMAGIC, self, self.onSM_AddMAGIC)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_SAVEITEMLIST, self, self.onSM_SAVEITEMLIST)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_STORAGE_SPACE_CHANGED, self, self.onSM_STORAGE_SPACE_CHANGED)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_STORAGE_ADDITEM, self, self.onSM_STORAGE_ADDITEM)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_AddItemMCStorage, self, self.onSM_STORAGE_ADDITEM)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_ChangeStoreItem, self, self.onSM_ChangeStoreItem)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_ChangeItemMCStorage, self, self.onSM_ChangeStoreItem)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_SHOP_QUERY, self, self.onSM_SHOP_QUERY)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_BUY_SHOPITEM, self, self.onSM_BUY_SHOPITEM)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_ShopDiscInfo, self, self.onSM_ShopDiscInfo)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_YB_EXCHANGE_LF, self, self.onSM_YB_EXCHANGE_LF)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_LF_EXCHANGE_JQ, self, self.onSM_LF_EXCHANGE_JQ)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_AUTHENTICATECREDIT, self, self.onSM_AUTHENTICATECREDIT)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_MAGIC_LVEXP, self, self.onSM_MAGIC_LVEXP)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_QUERY_MAP_NPC, self, self.onSM_QUERY_MAP_NPC)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_OpenDialogs, self, self.onSM_OpenDialogs)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_ATTACKMODE, self, self.onSM_ATTACKMODE)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_NPC_Action, self, self.onSM_NPC_Action)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_NpcGoodList, self, self.onSM_NpcGoodList)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_NpcDetailGoodList, self, self.onSM_NpcDetailGoodList)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_NpcBuyItem, self, self.onSM_NpcBuyItem)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_NpcSell, self, self.onSM_NpcSell)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_NpcSellPrice, self, self.onSM_NpcSellPrice)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_NpcSellItem, self, self.onSM_NpcSellItem)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_TransferFee, self, self.onSM_TransferFee)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_F2F_DEAL, self, self.onSM_F2F_DEAL)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_F2F_DEAL_CONTENT, self, self.onSM_F2F_DEAL_CONTENT)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_INPUT_DIALOG, self, self.onSM_INPUT_DIALOG)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_SPELLRECVED, self, self.onSM_SPELLRECVED)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_UserRegDate, self, self.onSM_UserRegDate)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_CHG_USERNAME, self, self.onSM_CHG_USERNAME)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_PlayerGildInfo, self, self.onSM_PlayerGildInfo)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_GetServerAndNetDelayTick, self, self.onSM_GetServerAndNetDelayTick)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_KillMan_Cnt, self, self.onSM_KillMan_Cnt)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_TradeBankSoldOut, self, self.onSM_TradeBankSoldOut)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_TradeBankFetchTip, self, self.onSM_TradeBankFetchTip)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_Request_Marry, self, self.onSM_Request_Marry)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_BLOCKCHAIN_SHOP_QUERY, self, self.onSM_BLOCKCHAIN_SHOP_QUERY)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_BLOCKCHAIN_BUY_SHOPITEM, self, self.onSM_BLOCKCHAIN_BUY_SHOPITEM)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_BlockChainNewOrder, self, self.onSM_BlockChainNewOrder)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_BlockChainChannelLogin, self, self.onSM_BlockChainChannelLogin)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_BlockChaninMoney, self, self.onSM_BlockChaninMoney)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_IDSLevelChange, self, self.onSM_IDSLevelChange)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_DIAMONDLIST, self, self.onSM_DIAMONDLIST)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_DiamondSpot, self, self.onSM_DiamondSpot)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_Act_Tip, self, self.onSM_Act_Tip)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_QueryDirectBuyGiftBag, self, self.onSM_QueryDirectBuyGiftBag)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_QuerySelfDBGBTimes, self, self.onSM_QuerySelfDBGBTimes)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_LOL_RANK, self, self.onSM_LOL_RANK)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_LOL_BUFF, self, self.onSM_LOL_BUFF)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_SeverLevel, self, self.onSM_SeverLevel)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_NumericDataChg, self, self.onSM_NumericDataChg)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_QueryServStepAndDays, self, self.onSM_QueryServStepAndDays)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_QueryWingInfo, self, self.onSM_QueryWingInfo)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_QueryMFBKInfo, self, self.onSM_QueryMFBKInfo)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_RideState, self, self.onSM_RideState)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_SendIdentifyCode, self, self.onSM_SendIdentifyCode)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_SendResIdentifyCode, self, self.onSM_SendResIdentifyCode)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_QueryPetInfo, self, self.onSM_QueryPetInfo)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_AddPet, self, self.onSM_AddPet)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_DelPet, self, self.onSM_DelPet)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_DelPets, self, self.onSM_DelPets)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_ShowVIPIcon, self, self.onSM_ShowVIPIcon)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_QueryHorseInfo, self, self.onSM_QueryHorseInfo)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_AddHorse, self, self.onSM_AddHorse)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_ServerTime, self, self.onSM_ServerTime)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_HorseInvalid, self, self.onSM_HorseInvalid)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_InitOkGameStart, self, self.onSM_InitOkGameStart)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_AddPetSkin, self, self.onSM_AddPetSkin)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_PetSkinInvalid, self, self.onSM_PetSkinInvalid)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_QueryGodWeaponList, self, self.onSM_QueryGodWeaponList)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_MonCrystalLv, self, self.onSM_MonCrystalLv)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_QuaryMonSoulInfo, self, self.onSM_QuaryMonSoulInfo)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_QueryGodRingList, self, self.onSM_QueryGodRingList)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_ClientQueryFEInfo, self, self.onSM_ClientQueryFEInfo)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_ArenaReqList, self, self.onSM_ArenaReqList)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_ArenaReqUserinfo, self, self.onSM_ArenaReqUserinfo)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_EquipSuiteActList, self, self.onSM_EquipSuiteActList)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_QueryHongFuBagInfo, self, self.onSM_QueryHongFuBagInfo)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_QueryWingEquipInfo, self, self.onSM_QueryWingEquipInfo)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_Group5v5PkInfo, self, self.onSM_Group5v5PkInfo)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_QueryEquipBarInfo, self, self.onSM_QueryEquipBarInfo)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_QueryRankEquipInfo, self, self.onSM_QueryRankEquipInfo)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_PsychicPowerInfo, self, self.onSM_PsychicPowerInfo)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_ClientQueryMIInfo, self, self.onSM_ClientQueryMIInfo)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_QueryEmoticonInfo, self, self.onSM_QueryEmoticonInfo)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_ChangeSexSucess, self, self.onSM_ChangeSexSucess)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_ClientGetUserConfig, self, self.onSM_ClientGetUserConfig)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_HellPKGetDetail, self, self.onSM_HellPKGetDetail)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_NewHellPK_RankInfo, self, self.onSM_NewHellPK_RankInfo)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_QueryCardInfo, self, self.onSM_QueryCardInfo)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_GetCardReward, self, self.onSM_GetCardReward)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_QueryYZQKInfo, self, self.onSM_QueryYZQKInfo)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_PLevelNpcSendIcon, self, self.onSM_PLevelNpcSendIcon)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_PLevelNpcSendAll, self, self.onSM_PLevelNpcSendAll)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_YbBoxSendAll, self, self.onSM_YbBoxSendAll)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_QueryGodShieldInfo, self, self.onSM_QueryGodShieldInfo)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_QueryReportReason, self, self.onSM_QueryReportReason)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_BoReviveRingOk, self, self.onSM_BoReviveRingOk)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_QueryGodBodyInfo, self, self.onSM_QueryGodBodyInfo)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_ReviveProtect, self, self.onSM_ReviveProtect)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_QueryCliTLCardInfo, self, self.onSM_QueryCliTLCardInfo)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_AskTLCardCfg, self, self.onSM_AskTLCardCfg)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_PSoulAbilSendAll, self, self.onSM_PSoulAbilSendAll)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_PSoulAbilQueryOther, self, self.onSM_PSoulAbilQueryOther)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_OpenWindow, self, self.onSM_OpenWindow)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_PKFDTError, self, self.onSM_PKFDTError)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_SendFlag, self, self.onSM_SendFlag)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_PAmuletRedPoint, self, self.onSM_PAmuletRedPoint)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_QueryFightSpiritInfo, self, self.onSM_QueryFightSpiritInfo)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_GildBattleScore, self, self.onSM_GildBattleScore)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_ServerActOpenInfo, self, self.onSM_ServerActOpenInfo)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_TowelLv, self, self.onSM_TowelLv)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_PrestNum, self, self.onSM_PrestNum)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_QueryGodRingEchoList, self, self.onSM_QueryGodRingEchoList)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_TNBAbilSendAll, self, self.onSM_TNBAbilSendAll)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_TNBAbilQueryOther, self, self.onSM_TNBAbilQueryOther)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_AchievementInfo, self, self.onSM_AchievementInfo)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_SingleAchievementInfo, self, self.onSM_SingleAchievementInfo)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_AchievementPoint, self, self.onSM_AchievementPoint)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_AchievementAwardInfo, self, self.onSM_AchievementAwardInfo)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_ShowRedPoint, self, self.onSM_ShowRedPoint)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_DisablePage, self, self.onSM_DisablePage)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_GodMonsterItem, self, self.onSM_GodMonsterItem)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_FirstRecharge, self, self.onSM_FirstRecharge)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_QueryHMPCharmInfo, self, self.onSM_QueryHMPCharmInfo)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_ExtLEVELUP, self, self.onSM_ExtLEVELUP)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_WinExtExp, self, self.onSM_WinExtExp)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_QueryBattleTeamEndInfo, self, self.onSM_QueryBattleTeamEndInfo)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_PlayerBTBattleInfo, self, self.onSM_PlayerBTBattleInfo)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_MCVipTime, self, self.onSM_MCVipTime)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_QueryFateInfo, self, self.onSM_QueryFateInfo)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_QueryOtherFateInfo, self, self.onSM_QueryOtherFateInfo)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_New_Act_Request_Res, self, self.onSM_New_Act_Request_Res)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_Senddmlist, self, self.onSM_Senddmlist)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_SendDMbroad, self, self.onSM_SendDMbroad)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_QueryLoginActInfo, self, self.onSM_QueryLoginActInfo)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_RedRainACtion, self, self.onSM_RedRainACtion)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_GetRedRain, self, self.onSM_GetRedRain)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_RedPStatus, self, self.onSM_RedPStatus)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_SendBoxMonster, self, self.onSM_SendBoxMonster)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_GetMoneyIcon, self, self.onSM_GetMoneyIcon)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_WMSecNotice, self, self.onSM_WMSecNotice)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_QueryAllPalInfo, self, self.onSM_QueryAllPalInfo)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_PalFetterQuery, self, self.onSM_PalFetterQuery)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_QueryPalAbility, self, self.onSM_QueryPalAbility)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_QueryPalEquips, self, self.onSM_QueryPalEquips)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_TakeOnPalEquip, self, self.onSM_TakeOnPalEquip)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_TakeOffPalEquip, self, self.onSM_TakeOffPalEquip)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_QueryPalQA, self, self.onSM_QueryPalQA)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_CliQueryPalDisPatch, self, self.onCliQueryPalDisPatch)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_CliPalDisPatch, self, self.onSM_CliPalDisPatch)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_CliGetPalDisPatch, self, self.onSM_CliGetPalDisPatch)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_RingLeechEffect, self, self.onSM_RingLeechEffect)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_GetMoneyQueryMain, self, self.onSM_GetMoneyQueryMain)
	g_data.eventDispatcher:addListener("M_BAGITEM_CHG", self, self.onM_BAGITEM_CHG)
	g_data.eventDispatcher:addListener("ALLTIME_COMFIRM", self, self.onALLTIME_COMFIRM)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_SendDiamTaskTag, self, self.onSM_SendDiamTaskTag)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_QueryLimitGroupBuy, self, self.onSM_QueryLimitGroupBuy)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_JoinGroupBuy, self, self.onSM_JoinGroupBuy)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_QueryCreateDay, self, self.onSM_QueryCreateDay)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_QueryAttSkyInfo, self, self.onSM_QueryAttSkyInfo)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_UseAttSky, self, self.onSM_UseAttSky)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_QueryGodItemInfo, self, self.onSM_QueryGodItemInfo)
	g_data.eventDispatcher:addListener("M_CHANGE_SERVER_TYPE", self, self.handleCrossServer)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_GildMonsterCmdRes, self, self.onSM_GildMonsterCmdRes)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_ChallengeResult, self, self.onSM_ChallengeResult)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_QueryHonourTitle, self, self.onSM_QueryHonourTitle)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_QueryAspectsInfo, self, self.onSM_QueryAspectsInfo)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_Horse_SkillUpLevel, self, self.onSM_Horse_SkillUpLevel)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_HorseBaseSkillLV, self, self.onSM_HorseBaseSkillLV)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_NormalStrKv, self, self.onSM_NormalStrKv)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_NormalStrKvList, self, self.onSM_NormalStrKvList)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_TianLeiInfo, self, self.onSM_TianLeiInfo)
	g_data.eventDispatcher:addListener("SERVER_KV_CHANGED", self, self.handleServerKVChanged)
	g_data.eventDispatcher:addListener("SERVER_KVLIST_CHANGED", self, self.handleServerKVListChanged)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_ChangeUnrealFeture, self, self.onSM_ChangeUnrealFeture)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_SCSetSkill, self, self.onSM_SCSetSkill)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_SCQueryInfo, self, self.onSM_SCQueryInfo)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_sendUnrealBannerInfo, self, self.onSM_sendUnrealBannerInfo)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_AnimalHpChange, self, self.onSM_AnimalHpChange)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_SwitchWuXingType, self, self.onSM_SwitchWuXingType)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_InitStruct, self, self.onSM_InitStruct)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_LeagueInfo, self, self.onSM_LeagueInfo)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_LeagueList, self, self.onSM_LeagueList)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_LeagueMemberList, self, self.onSM_LeagueMemberList)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_CenSerUPRedPoint, self, self.onSM_CenSerUPRedPoint)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_DiamUseAllDiamBag, self, self.onSM_DiamUseAllDiamBag)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_NewHellPK_BoxSite, self, self.onSM_NewHellPK_BoxSite)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_DelayActionOP, self, self.onSM_DelayActionOP)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_Sel_Exchg_Item, self, self.onSM_Sel_Exchg_Item)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_WuXueList, self, self.onSM_WuXueList)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_TTWAR_Info, self, self.onSM_TTWAR_Info)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_GildManor_Res, self, self.onSM_GildManor_Res)
	MirTcpClient:getInstance():subscribeMemberOnProtocol(SM_PalGrow_OP, self, self.onSM_PalGrow_OP)
	g_data.eventDispatcher:addListener("TEAM_MEM_CHANGE", self, self.onTeamMemChange)

	if DEBUG > 0 and device.platform == "windows" and IS_PC_SIMUALTOR then
		g_data.eventDispatcher:addListener("CC_KEYPAD_EVENT", self, self.onCC_KEYPAD_EVENT)
	end
end

function mainui:onALLTIME_COMFIRM(result)
	local rsb = DefaultClientMessage(CM_QueryAchievements)

	MirTcpClient:getInstance():postRsb(rsb)
end

function mainui:requestInit()
	return
end

function mainui:requestData()
	local function requestRelationData(relationType)
		local rsb = DefaultClientMessage(CM_QueryRelation)

		rsb.FRelationMark = relationType

		MirTcpClient:getInstance():postRsb(rsb)
	end
end

function mainui:requestInitData()
	self:requestData()

	local rsb = DefaultClientMessage(CM_QueryServStepAndDays)
	local rsb = DefaultClientMessage(CM_ServerTime)

	MirTcpClient:getInstance():postRsb(rsb)

	local rsb = DefaultClientMessage(CM_MilitaryRankLv)

	MirTcpClient:getInstance():postRsb(rsb)

	local rsb = DefaultClientMessage(CM_QueryGodWeaponList)

	MirTcpClient:getInstance():postRsb(rsb)

	local rsb = DefaultClientMessage(CM_QueryGodRingList)

	MirTcpClient:getInstance():postRsb(rsb)

	local rsb = DefaultClientMessage(CM_ClientQueryFEInfo)

	rsb.FFEType = 1

	MirTcpClient:getInstance():postRsb(rsb)

	local rsb = DefaultClientMessage(CM_QueryRankEquipInfo)

	MirTcpClient:getInstance():postRsb(rsb)

	local rsb = DefaultClientMessage(CM_QueryEmoticonInfo)

	MirTcpClient:getInstance():postRsb(rsb)

	local rsb = DefaultClientMessage(CM_QueryGodShieldInfo)

	MirTcpClient:getInstance():postRsb(rsb)

	local rsb = DefaultClientMessage(CM_QueryGodBodyInfo)

	MirTcpClient:getInstance():postRsb(rsb)

	local rsb = DefaultClientMessage(CM_AskTLCardCfg)

	MirTcpClient:getInstance():postRsb(rsb)

	local rsb = DefaultClientMessage(CM_QueryCliTLCardInfo)

	MirTcpClient:getInstance():postRsb(rsb)

	local rsb = DefaultClientMessage(CM_PSoulAbilSendAll)

	MirTcpClient:getInstance():postRsb(rsb)

	local rsb = DefaultClientMessage(CM_QueryFightSpiritInfo)

	MirTcpClient:getInstance():postRsb(rsb)

	local rsb = DefaultClientMessage(CM_QueryGodRingEchoList)

	MirTcpClient:getInstance():postRsb(rsb)

	local rsb = DefaultClientMessage(CM_QueryHMPCharmInfo)

	MirTcpClient:getInstance():postRsb(rsb)

	local rsb = DefaultClientMessage(CM_MCVipTime)

	MirTcpClient:getInstance():postRsb(rsb)

	local rsb = DefaultClientMessage(CM_QueryFateInfo)

	MirTcpClient:getInstance():postRsb(rsb)

	local rsb = DefaultClientMessage(CM_GetSBXSReward)

	MirTcpClient:getInstance():postRsb(rsb)

	local rsb = DefaultClientMessage(CM_QueryLoginActInfo)

	MirTcpClient:getInstance():postRsb(rsb)

	local rsb = DefaultClientMessage(CM_QueryButtleTime)

	MirTcpClient:getInstance():postRsb(rsb)

	local rsb = DefaultClientMessage(CM_QueryAllPalInfo)

	MirTcpClient:getInstance():postRsb(rsb)

	local rsb = DefaultClientMessage(CM_CliQueryPalDisPatch)

	MirTcpClient:getInstance():postRsb(rsb)

	local rsb = DefaultClientMessage(CM_PalGrow_OP)

	rsb.FOpid = 1
	rsb.Fparam = 0
	rsb.FParamStr = 0

	MirTcpClient:getInstance():postRsb(rsb)

	local rsb = DefaultClientMessage(CM_New_Act_Request)

	rsb.FActType = ActivityType.ACT_RINGPROMISE
	rsb.FActId = ActivityType.ACT_RINGPROMISE_ACTID
	rsb.FButtonId = 0

	MirTcpClient:getInstance():postRsb(rsb)

	local rsb = DefaultClientMessage(CM_New_Act_Request)

	rsb.FActType = ActivityType.ACT_WEEKPRIVILEGE
	rsb.FActId = ActivityType.ACT_WEEKPRIVILEGE_ACTID
	rsb.FButtonId = 0

	MirTcpClient:getInstance():postRsb(rsb)

	local rsb = DefaultClientMessage(CM_QueryHonourTitle)

	MirTcpClient:getInstance():postRsb(rsb)

	local rsb = DefaultClientMessage(CM_QueryAspectsInfo)

	MirTcpClient:getInstance():postRsb(rsb)

	local rsb = DefaultClientMessage(CM_QueryTianLeiInfo)

	MirTcpClient:getInstance():postRsb(rsb)

	for k, v in pairs(SPSkill) do
		local rsb = DefaultClientMessage(CM_SPSkillCD)

		rsb.FID = v.id

		MirTcpClient:getInstance():postRsb(rsb)
	end

	self:requestPenDant()
	self:checkServerActOpenInfo()

	local rsb = DefaultClientMessage(CM_TTWAR_Op)

	rsb.Fopid = 0
	rsb.FactId = 0

	MirTcpClient:getInstance():postRsb(rsb)
end

function mainui:ctor()
	if device.platform == "android" or device.platform == "ios" or IS_PC_SIMUALTOR or IS_PLAYER_DEBUG then
		local infoStr = ""

		local function addInfo(key, value)
			infoStr = infoStr .. "|" .. key .. "=" .. value
		end

		self.channelID = platformSdk:sdkChannelId()

		if tonumber(self.channelID) == nil then
			self.channelID = ""
		end

		if self.channelID then
			print("ctor获取的channelID:" .. self.channelID)
		end

		addInfo("platform", device.platform == "android" and 1 or 2)

		if self.channelID then
			addInfo("channel_id", self.channelID)
		end

		addInfo("device_id", MirDevices:getInstance():getDeviceId())
		addInfo("game_version", MIR2_VERSION_BASE or "0")
		addInfo("resource_version", MIR2_VERSION or "0")

		local platformRsb = DefaultClientMessage(CM_PlatformInfo)

		platformRsb.FInfo = infoStr

		p2("net", infoStr)
		MirTcpClient:getInstance():postRsb(platformRsb)
	end

	if game.deviceFix then
		display.newScale9Sprite(res.getframe2("pic/common/fix_border.png"), 0, 0, cc.size(game.deviceFix, display.height)):add2(self):anchor(1, 1):pos(display.width, display.height)
		display.newScale9Sprite(res.getframe2("pic/common/fix_border.png"), 0, 0, cc.size(game.deviceFix, display.height)):add2(self):scaleX(-1):anchor(1, 1):pos(0, display.height)
	end

	self.timeLimitWidgets = {
		weChatRedPacket = {
			enable = false
		},
		weChatGift = {
			enable = false
		},
		focusVIPWechat = {
			enable = false
		},
		cruiseMerchant = {
			enable = false
		},
		btnWoC = {
			enable = false
		},
		btnGodMonsterItem = {
			enable = false
		},
		btnBattleContest = {
			enable = false
		},
		btnSeaWarArm = {
			enable = false
		}
	}
	self.globleTimeLabel = {
		state = 0
	}
	self.maskWidgets = {}
	self.panels = {}
	self.customs = {}
	self.tTime = {}
	self.delayLabel_list = {}
	self.requestFromDrumUpgrade = false
	self.leftTopTip = import(".common.leftTopTip", current).new():add2(self, self.z.leftTopTip)
	self.centerTopTip = import(".common.centerTopTip", current).new():add2(self, self.z.centerTopTip)

	self:loadConsole()

	self.crossChallenge = import(".console.crossChallenge", current).new():add2(self, self.z.focus)
	self.newCrossChallenge = import(".console.newCrossChallenge", current).new():add2(self, self.z.focus)
	self.battleScore = import(".console.battleScore", current).new():add2(self, self.z.focus)
	self.worldBossChallenge = import(".console.worldBossChallenge", current).new():add2(self, self.z.focus)
	self.notice = import(".common.notice", current).new():add2(self, self.z.focus)
	self.waiting = import(".common.waiting", current)
	self.loading = import(".common.loading", current).new():add2(self, self.z.focus)
	self.countDown = import(".console.countDown", current).new():add2(self, self.z.focus)
	self.buffManager = import(".common.buffManager", current).new():add2(self, self.z.focus)
	self.fairyLandSBKView = import(".console.fairyLandSBKView", current).new():add2(self, self.z.focus)

	self:bindMsg()
	self:requestInit()

	for i = 1, 3 do
		self.delayLabel_list[i] = an.newLabel("", 14, 1, {
			color = display.COLOR_GREEN,
			align = cc.TEXT_ALIGNMENT_RIGHT
		}):anchor(1, 0):pos(display.width - 5, display.height - 48 - (i - 1) * 20):add2(self, 100)
	end

	if g_data.setting.base.DelayShow and self.GlobleController == nil then
		self:delayLabelShow()
	else
		self:delayLabelHide()
	end

	self.msgs = newList()
	g_data.client.serverState = 0
	g_data.client.openDay = 0

	g_data.solider:updateSoliderList()

	self.canJuBao = true
	self.BagCards = {}

	g_data.firstOpen:set("firstWelcome", false)

	self.attsp = nil
	self.moveSpeed = nil
	self.newHorseSpeed = nil
end

function mainui:onEnter()
	local lastclickTime = cache.getLastclickTime(common.getPlayerName())
	local curTime = g_data.serverTime:getTime() or socket.gettime()

	if not lastclickTime then
		self.handler = scheduler.scheduleGlobal(function()
			local rsb = DefaultClientMessage(CM_New_Act_Request)

			rsb.FActType = 1
			rsb.FActId = 1000022
			rsb.FButtonId = 0

			MirTcpClient:getInstance():postRsb(rsb)
			scheduler.unscheduleGlobal(self.handler)

			self.handler = nil

			local rsb2 = DefaultClientMessage(CM_SCQueryInfo)

			MirTcpClient:getInstance():postRsb(rsb2)
		end, 0.2, 0, 0.2)
	else
		local temp_1 = os.date("%y-%m-%d", lastclickTime)
		local tempList_1 = string.split(temp_1, "-")
		local temp_2 = os.date("%y-%m-%d", curTime)
		local tempList_2 = string.split(temp_2, "-")

		if #tempList_1 > 0 and #tempList_2 > 0 and tonumber(tempList_1[3]) ~= tonumber(tempList_2[3]) then
			self.handler = scheduler.scheduleGlobal(function()
				local rsb = DefaultClientMessage(CM_New_Act_Request)

				rsb.FActType = 1
				rsb.FActId = 1000022
				rsb.FButtonId = 0

				MirTcpClient:getInstance():postRsb(rsb)
				scheduler.unscheduleGlobal(self.handler)

				self.handler = nil

				local rsb2 = DefaultClientMessage(CM_SCQueryInfo)

				MirTcpClient:getInstance():postRsb(rsb2)
			end, 0.2, 0, 0.2)
		elseif #tempList_1 > 0 and #tempList_2 > 0 and tonumber(tempList_1[3]) == tonumber(tempList_2[3]) then
			self.handler = scheduler.scheduleGlobal(function()
				scheduler.unscheduleGlobal(self.handler)

				self.handler = nil

				local rsb2 = DefaultClientMessage(CM_SCQueryInfo)

				MirTcpClient:getInstance():postRsb(rsb2)
			end, 0.2, 0, 0.2)
		end
	end
end

function mainui:initBtnAutoRatConfig2()
	self.btnAutoRat2 = {
		data = {
			key = "btnAutoRat2",
			x = display.cx - 187,
			y = display.cy - 125
		},
		config = {
			btntype = "normal",
			key = "btnAutoRat2",
			class = "btnMove",
			name = "自动战斗",
			banRemove = true,
			desc = "跨服竞技自动战斗"
		}
	}

	self.console:addWidget(self.btnAutoRat2.data, true, self.btnAutoRat2.config)
end

function mainui:refreshRedPoint()
	local playerId = tonumber(common.getPlayerId())

	if not playerId or playerId == 0 then
		return
	end

	self:checkFirstSolider()
	self:checkFirstGodRing()
	self:checkGodRingTJ()
	self:checkFirstHorseSoul()
	self:checkFirstWingEquip()
	self:checkFirstCardSys()
	self:checkFirstGodDun()
	self:checkFirstGodBody()
	self:checkFirstCastingSoul()
	self:checkFirstGoldEquip()
	self:checkFirstGodCustome()
	self:checkFirstAmulet()
	self:checkFirstFootPoint()
	self:checkFirstMoh()
	self:checkPrivateBoss()
	self:checkFirstSoliderSoul()
	self:checkGodRingJH()
	self:checkFirstMumerology()
	self:checkFirstPal()
	self:checkFirstMagicWeapon()
	self:checkFirstGrowthRoad()
	self:checkFirstGodItem()
	self:checkFirstMingWen()
	self:checkFirstStarChart()
	self:checkFirstMeridian()
	self:checkFirstBirthSign()
	self:checkFirstPenDant()
	self:checkFirstTemple()
	self:checkFirstBagua()
	self:checkFirstWuxue()
end

function mainui:checkServerActOpenInfo()
	local rsb = DefaultClientMessage(CM_ServerActOpenInfo)

	MirTcpClient:getInstance():postRsb(rsb)

	local rsb = DefaultClientMessage(CM_MCVipTime)

	MirTcpClient:getInstance():postRsb(rsb)

	local rsb = DefaultClientMessage(CM_QueryCreateDay)

	MirTcpClient:getInstance():postRsb(rsb)
	g_data.wuxue:checkWuxuePanelIcon()
end

local serverActOpenData = {
	FQKSLStr = {
		index = "FQKSLStr",
		name = "乾坤神炉",
		funcId = 1015,
		fun = function(data, config)
			local open = tonumber(data[1]) or 0

			main_scene.ui.godStoveOpen = open
		end
	},
	FQDZPStr = {
		index = "FQDZPStr",
		name = "庆典转盘",
		funcId = 1016,
		fun = function(data, config)
			local open = tonumber(data[1]) or 0
			local reddot = tonumber(data[2]) or 0

			if reddot and reddot == 1 then
				g_data.badgeSystem:set(badge.B_INTERFACE_WOC, true)
			else
				g_data.badgeSystem:set(badge.B_INTERFACE_WOC, false)
			end

			main_scene.ui.wheelOfCebOpen = open
		end
	},
	FLevelGiftStr = {
		index = "FLevelGiftStr",
		name = "新手豪礼",
		funcId = 1017,
		fun = function(data, _)
			local open = tonumber(data[1]) or 0

			main_scene.ui.newassGiftOpen = open
		end
	},
	FAchievementStr = {
		index = "FAchievementStr",
		name = "成就",
		funcId = 1019,
		fun = function(data, config)
			local open = tonumber(data[1]) or 0

			main_scene.ui.achieveOpen = open
		end
	},
	godmonster = {
		index = "godmonster",
		name = "轰天雷",
		funcId = 1020,
		fun = function(data, _)
			local open = tonumber(data[1]) or 0

			main_scene.ui.godMonsterItemOpen = open == 1
		end
	},
	SkyGift = {
		index = "SkyGift",
		name = "天赐宝箱",
		funcId = 1021,
		fun = function(data, _)
			local open = tonumber(data[1]) or 0

			main_scene.ui.skyGiftOpen = open == 1
		end
	},
	battleContest = {
		index = "battleContest",
		name = "战队赛",
		funcId = 1022,
		fun = function(data, _)
			local open = tonumber(data[1]) or 0

			main_scene.ui.battleContestOpen = open == 1
		end
	},
	FShopDiscStr = {
		index = "FShopDiscStr",
		name = "限时折扣",
		funcId = 1024,
		fun = function(data, _)
			local open = tonumber(data[1]) or 0

			main_scene.ui.limitedShopOpen = open == 1
		end
	},
	newActivity = {
		index = "newActivity",
		name = "周年庆典",
		funcId = 1025,
		fun = function(data, config)
			local open = tonumber(data[1]) or 0

			main_scene.ui.newActivityOpen = open == 1
		end
	},
	dm = {
		index = "dm",
		name = "弹幕",
		funcId = 1027,
		fun = function(data, config)
			local open = tonumber(data[1]) or 0

			main_scene.ui.dmOpen = open == 1
		end
	},
	activityBrave = {
		index = "activityBrave",
		name = "勇士集结",
		funcId = 1029,
		fun = function(data, config)
			local open = tonumber(data[1]) or 0

			main_scene.ui.activityBrave = open == 1
		end
	},
	earnOnline = {
		index = "earnOnline",
		name = "网赚",
		funcId = 1028,
		fun = function(data, config)
			return
		end
	},
	helpsign = {
		index = "helpsign",
		name = "成长助力",
		funcId = 1031,
		fun = function(data, config)
			local open = tonumber(data[1]) or 0

			main_scene.ui.growthBoostOpen = open == 1 and common.middleDays(g_data.player.regtime, g_data.serverTime:getTime()) <= 31 and common.middleDays(os.time({
				month = 8,
				year = 2020,
				day = 6
			}), g_data.serverTime:getTime()) >= g_data.client.openDay
		end
	},
	groupbuy = {
		index = "groupbuy",
		name = "团购",
		funcId = 1032,
		fun = function(data, config)
			if data[1] and not main_scene.ui.__opengroupbuy then
				main_scene.ui:hidePanel("groupBuying")
				main_scene.ui:showPanel("groupBuying")

				main_scene.ui.__opengroupbuy = true
			end
		end
	},
	MonthVip = {
		index = "MonthVip",
		name = "至尊特权",
		funcId = 1037,
		fun = function(data, config)
			local open = tonumber(data[1]) or 0

			main_scene.ui.highestPrivilegeOpen = open == 1

			main_scene.ui:checkHighestPrivilege()
		end
	},
	weekPrivilege = {
		index = "weekPrivilege",
		name = "周卡特权",
		funcId = 1044,
		fun = function(data, config)
			main_scene.ui:checkHighestPrivilege()
		end
	},
	SeaWar = {
		index = "SeaWar",
		name = "海战",
		funcId = 1047,
		fun = function(data, config)
			local open = tonumber(data[1]) or 0

			main_scene.ui.__seaWarOpen = open == 1
		end
	},
	monthActivity = {
		index = "monthActivity",
		name = "月度狂欢周",
		funcId = 1058,
		fun = function(data, config)
			local open = tonumber(data[1]) or 0

			main_scene.ui.monthActivityOpen = open == 1

			main_scene.ui:checkMonthActivityEveryDayRedPoint()
		end
	},
	newMagicBox = {
		index = "newMagicBox",
		name = "幻域魔匣",
		funcId = 1061,
		fun = function(data, config)
			local open = tonumber(data[1]) or 0

			main_scene.ui.newMagicBox = open == 1
		end
	}
}

function mainui:checkMonthActivityEveryDayRedPoint()
	if FuncOpen.isFuncOpen(gameFunc.FUNC1058) then
		local day = cache.getCVarN("monthActivity")

		day = day or 0

		local day1 = tonumber(os.date("%d", os.time()))

		if day and day ~= day1 then
			g_data.badgeSystem:set(badge.B_MONTH_ACTIVITY, true)
		end

		cache.setCVar("monthActivity", day1)
	end
end

function mainui:onSM_ServerActOpenInfo(result)
	if not result then
		return
	end

	local function findServerConfig(name)
		for i, v in ipairs(result.Ftags) do
			if v.Fstr == name then
				return v
			end
		end

		return nil
	end

	local record = {}

	for k, v in pairs(serverActOpenData) do
		local ignore = false
		local cfg = findServerConfig(k)

		if result[k] or cfg then
			local info = {
				0
			}

			if cfg and v.funcId ~= 1031 then
				info = {
					cfg.FStatus
				}
			elseif cfg and v.funcId == 1031 then
				info = {
					cfg.FStatus == 1 and common.middleDays(g_data.player.regtime, g_data.serverTime:getTime()) <= 31 and common.middleDays(os.time({
						month = 8,
						year = 2020,
						day = 6
					}), g_data.serverTime:getTime()) >= g_data.client.openDay and 1 or 0
				}
			elseif result[k] ~= "" then
				info = string.split(result[k], "|") or {}
			else
				ignore = true
			end

			if cfg and cfg.Fstr == "groupbuy" then
				info = {
					cfg.FStatus
				}
			end

			if cfg and cfg.Fstr == "MonthVip" then
				info = {
					cfg.FStatus
				}
			end

			if v.fun then
				v.fun(info, v)
			end

			record[#record + 1] = {
				name = v.name,
				funcId = v.funcId,
				info = info
			}
		elseif v.funcId == 1044 and g_data.login.zoneId == 1108 then
			-- block empty
		else
			if v.fun then
				v.fun(info or {}, v)
			end

			local open = FuncOpen.isFuncOpen(v.funcId) and 1 or 0
			local info = {
				open
			}

			record[#record + 1] = {
				name = v.name,
				funcId = v.funcId,
				info = info
			}
		end
	end

	main_scene.ui.console:checkOpen(record)

	if main_scene.ui.newassGiftOpen then
		for _, v in ipairs(record) do
			if v.funcId == 1017 then
				main_scene.ui:startNewassCountdown(v.info)
			end
		end
	end

	g_data.wuxue:checkWuxuePanelIcon()
end

function mainui:onSM_GlobalSet(result, protoId)
	local strTime = ycFunction:getTimeStrByMS(result.Fnowtime)

	print("服务器时间:" .. strTime)

	local tNowTime = string.split(strTime, " ")
	local tYearMonDay = string.split(tNowTime[2], "-")
	local tHourMinSec = string.split(tNowTime[3], ":")

	self.tTime[1] = string.sub(tYearMonDay[1], 2)
	self.tTime[2] = tYearMonDay[2]
	self.tTime[3] = tYearMonDay[3]
	self.tTime[4] = tHourMinSec[1]
	self.tTime[5] = tHourMinSec[2]
	self.tTime[6] = string.sub(tHourMinSec[3], 1, 2)

	local element = result.Fitemlist[1]
	local playerLevel = g_data.player.ability.FLevel

	if playerLevel <= 22 or element == nil then
		return
	end

	for i, v in pairs(result.Fitemlist) do
		if v.Fid == self.channelID then
			element = v

			break
		end
	end

	local keys = {
		"weChatRedPacket",
		"weChatGift"
	}
	local version = 1

	if element.Fid == 1 then
		-- block empty
	elseif element.Fid == 200 then
		version = 2
	end

	local function startWechat(dt)
		if self.timeLimitWidgets then
			local key = keys[version]

			self.timeLimitWidgets[key].enable = true

			if self.console then
				local data = self.timeLimitWidgets[key].data

				data = data or g_data.widgetDef.getData(key)

				self.console:addWidget(data)
			end
		end
	end

	local function closeWechat(dt)
		if self.timeLimitWidgets then
			local key = keys[version]

			self.timeLimitWidgets[key].enable = false

			if self.console then
				self.console:removeWidget(key)
			end
		end
	end

	if element and tonumber(result.Fnowtime) < tonumber(element.Fbegintime) then
		scheduler.performWithDelayGlobal(startWechat, result.Fitemlist[1].Fbegintime - result.Fnowtime)
		scheduler.performWithDelayGlobal(closeWechat, result.Fitemlist[1].Fendtime - result.Fnowtime)
	elseif element and tonumber(element.Fbegintime) <= tonumber(result.Fnowtime) and tonumber(result.Fnowtime) < tonumber(element.Fendtime) then
		startWechat()
		scheduler.performWithDelayGlobal(closeWechat, result.Fitemlist[1].Fendtime - result.Fnowtime)
	elseif element and tonumber(element.Fendtime) < tonumber(result.Fnowtime) then
		closeWechat()
	end
end

function mainui:onSM_TradeBankSoldOut(result)
	if g_data.player:getIsCrossServer() then
		main_scene.ui:tip("该功能不能使用")

		return
	end

	self.notice:showCustomNotic("pic/console/notice/trade.png", function()
		main_scene.ui:togglePanel("tradeshop", {
			default = 4,
			type = result.FBankType
		})
	end)
end

function mainui:onSM_QueryServStepAndDays(result)
	if g_data.login.serverLevel ~= result.FServerStep or g_data.client.openDay ~= result.FOpenDays then
		self:checkServerActOpenInfo()
		self:handleDayOrServerLVChange(g_data.client.openDay ~= result.FOpenDays, g_data.login.serverLevel ~= result.FServerStep)
	end

	g_data.login.serverLevel = result.FServerStep
	g_data.client.serverState = result.FServerStep
	g_data.client.openDay = result.FOpenDays

	self:refreshRedPoint()

	if g_data.client.openDay <= 7 then
		print("开服七天内打开庆典转盘")
		self:openWoC()
	end
end

function mainui:onSM_QueryWingInfo(result)
	g_data.player:setWingInfo(result)
end

function mainui:onSM_ClientQueryFEInfo(result)
	if result.FFEListType == 1 then
		g_data.player:setFashionInfo(result)
	end
end

function mainui:onSM_AddHorse(result)
	g_data.horse:add(result.FHorseInfo)

	local horse = g_data.horse:getHorseById(result.FHorseInfo.FID)

	if horse then
		local tipStr = "恭喜你获得了" .. horse.name .. "坐骑"

		if horse.istime then
			tipStr = tipStr .. "，限时" .. math.ceil(horse.time / 24) .. "天"
		end

		main_scene.ui:tip(tipStr, 6)
		common.addMsg(tipStr, 255, 252, true)
	end
end

function mainui:onSM_ServerTime(result)
	if not result then
		return
	end

	local date = os.date("*t", result.FServerTime)

	g_data.serverTime:setTime(result.FServerTime)
	g_data.eventDispatcher:dispatch("ALLTIME_COMFIRM", result)
end

function mainui:onSM_HorseInvalid(result)
	if not result then
		return
	end

	g_data.horse:del(result.FHorseID)

	local horse = g_data.horse:getHorseById(result.FHorseID)

	if horse then
		common.addMsg("您的限时坐骑：" .. horse.name .. "，已过期", 255, 252, true)
	end
end

function mainui:onSM_InitOkGameStart(result)
	if not result then
		return
	end

	self:requestInitData()
end

function mainui:onSM_AddPetSkin(result)
	g_data.pet:addSkin(result.FPetSkinInfo)

	local skin = def.pet:getBaseSkinCfgByID(result.FPetSkinInfo.FID)
	local tipStr = "恭喜你获得皮肤“" .. skin.ClientName .. "”"

	if skin.BoLimitTime ~= 0 then
		tipStr = tipStr .. " 限时" .. math.ceil(skin.Time / 24) .. "天"
	end

	main_scene.ui:tip(tipStr, 6)
	common.addMsg(tipStr, 255, 252, true)
end

function mainui:onSM_PetSkinInvalid(result)
	if not result then
		return
	end

	g_data.pet:delSkin(result.FPetSkinID)

	local skin = def.pet:getBaseSkinCfgByID(result.FPetSkinID)

	if skin then
		common.addMsg("您的皮肤：" .. skin.ClientName .. "，已过期", 255, 252, true)
	end
end

function mainui:onSM_QueryGodWeaponList(result)
	if not result then
		return
	end

	if tonumber(result.FUserId) == 0 then
		g_data.solider:setInfo(result.FGodWeaponList)
		g_data.eventDispatcher:dispatch("M_SOLIDE_DATA_CHG")
	else
		g_data.eventDispatcher:dispatch("M_QueryGodWeaponList", result)
	end
end

function mainui:onSM_QueryGodShieldInfo(result)
	if not result then
		return
	end

	g_data.goddun:setInfo(result.FInfoList)
	g_data.eventDispatcher:dispatch("M_GODDUN_DATA_CHG")
end

function mainui:onSM_QueryHorseInfo(result)
	if not result then
		return
	end

	g_data.horse:setInfo(result)
	g_data.horse:isHave(result)
end

function mainui:onSM_QueryPetInfo(result)
	g_data.pet:setInfo(result)
end

function mainui:onSM_AddPet(result)
	g_data.pet:add(result.FPetInfo)
end

function mainui:onSM_DelPet(result)
	g_data.pet:del(result.FPetIdent)
end

function mainui:onSM_DelPets(result)
	for i, v in ipairs(result.FPetIdentArr) do
		g_data.pet:del(v)
	end
end

function mainui:onSM_RideState(result)
	g_data.player:setRideState(result.FRideState)
end

function mainui:onSM_TradeBankFetchTip(result)
	if not result or result.FBankType ~= 0 and result.FBankType ~= 1 then
		return
	end

	g_data.badgeSystem:set(result.FBankType == 0 and badge.B_BOTTOM_TRADEYB or badge.B_BOTTOM_TRADEJB, result.FBoTip)
end

function mainui:delayLabelResult(result)
	if not result then
		return
	end

	local curTicket = math.ceil(socket.gettime() * 1000)
	local Currentping = curTicket - tonumber(result.Tick)

	self.delayTimes = math.ceil(Currentping / 2)
end

function mainui:delayLabelSet()
	if main_scene.ui then
		local rsb = DefaultClientMessage(CM_GetServerAndNetDelayTick)

		rsb.Tick = math.ceil(socket.gettime() * 1000)

		MirTcpClient:getInstance():postRsb(rsb)

		if self.delayTimes then
			local ping = self.delayTimes

			for i = #self.delayLabel_list, 2, -1 do
				self.delayLabel_list[i]:setString(self.delayLabel_list[i - 1]:getString())
				self.delayLabel_list[i]:setColor(self.delayLabel_list[i - 1]:getColor())
			end

			self.delayLabel_list[1]:setString("延时: " .. ping .. "毫秒")

			if ping <= 100 then
				self.delayLabel_list[1]:setColor(cc.c3b(51, 255, 0))
			elseif ping >= 100 and ping <= 200 then
				self.delayLabel_list[1]:setColor(cc.c3b(241, 237, 2))
			elseif ping >= 200 then
				self.delayLabel_list[1]:setColor(cc.c3b(243, 3, 2))
			end
		end
	else
		return
	end
end

function mainui:delayLabelShow()
	if main_scene then
		if self.GlobleControllerhide then
			scheduler.unscheduleGlobal(self.GlobleControllerhide)

			self.GlobleControllerhide = nil
		end

		self:delayLabelSet()

		self.GlobleController = scheduler.scheduleGlobal(function()
			if self.delayLabelSet ~= nil then
				self:delayLabelSet()
			end
		end, 2)
	end
end

function mainui:delayLabelHide()
	if self.GlobleController then
		scheduler.unscheduleGlobal(self.GlobleController)

		self.GlobleController = nil
		self.GlobleControllerhide = scheduler.scheduleGlobal(function()
			local index

			for i = #self.delayLabel_list, 1, -1 do
				local label = self.delayLabel_list[i]
				local text = label:getString()

				index = i

				if text ~= "" then
					label:setString("")

					return
				end
			end

			if index == 1 and self.GlobleControllerhide then
				scheduler.unscheduleGlobal(self.GlobleControllerhide)

				self.GlobleControllerhide = nil
			end
		end, 2)
	else
		return
	end
end

function mainui:onSM_SPELLRECVED(result, protoId)
	if result then
		g_data.client.skillBtns["skillMutex" .. tostring(result.FSkillId)] = false

		if g_data.openSkillLog then
			print("____________mainui:onSM_SPELLRECVED .." .. result.FSkillId)
		end
	end
end

function mainui:onSM_PlayerGildInfo(result, protoId)
	if result then
		g_data.player.guildInfo.guildName = result.FGildName
		g_data.player.guildInfo.title = result.FPosition
	end
end

function mainui:dispatchGuildEvent(eventType)
	local eventDispatcher = cc.Director:getInstance():getEventDispatcher()
	local event = cc.EventCustom:new(eventType)

	eventDispatcher:dispatchEvent(event)
end

function mainui:handleDeath(params)
	if g_data.map.state == 11 or g_data.map.state == 13 then
		return
	end

	self.centerTopTip:show("relive")
end

function mainui:handleRelive(params)
	self.centerTopTip:hide()
end

function mainui:onSM_MAGIC_LVEXP(result, protoId)
	if result then
		local v = g_data.player:setMagicExp(result)

		if v and self.panels.equip then
			self.panels.equip:updateMagic(v.FMagicId)
		end
	end
end

function mainui:onSM_AddMAGIC(result, protoId)
	if result then
		local data = g_data.player:addMagic(result)

		if data then
			main_scene.ui.console.skills:layout(data.FMagicId)
		end

		main_scene.ui.console.skills:upt()

		if self.panels.equip and self.panels.equip.page == "skill" then
			self.panels.equip:showContent("skill")
		end

		magic.checkSkillLv(70)
	end
end

function mainui:onSM_EAT(result, protoId)
	if result then
		local item

		if result.FStateTag == 0 then
			local makeIndex, data, isQuick, where = g_data.bag:useEnd("eat", false)

			item = data

			if makeIndex then
				local panel = self.panels

				if self.panels.bag then
					self.panels.bag:addItem(makeIndex)
				end
			end

			self:checkUsedItemforStopAutoRat(data)
		elseif result.FStateTag == 1 then
			local _, data, isQuick = g_data.bag:useEnd("eat", true)

			item = data

			self:checkUsedItemforStopAutoRat(data)
			print("mainui:onSM_EAT_ true")
		end

		if item then
			g_data.eventDispatcher:dispatch("ITEM_USE", item)

			g_data.itemUseMtx = false
		end
	end
end

function mainui:onSM_EATAll(result, _)
	if result then
		-- block empty
	end
end

function mainui:onSM_ITEMDURACHG(result, protoId)
	if result then
		if result.Flag == 0 then
			g_data.bag:duraChange(result.FItemIdent, result.FItemDura, result.FItemMaxDura)

			if self.panels.bag then
				self.panels.bag:duraChange(result.FItemIdent)
			end

			if self.panels.strengthen then
				self.panels.strengthen:duraChange(result.FItemIdent)
			end

			if self.panels.clothComposition then
				self.panels.clothComposition:duraChange(result.FItemIdent)
			end

			local _, item = g_data.bag:getItem(result.FItemIdent)

			if item and item:getVar("name") == "金刚石" and self.panels.smelting then
				self.panels.smelting:refreshStone()
			end

			if self.panels.f2fDeal then
				self.panels.f2fDeal:duraChange(result.FItemIdent)
			end

			if self.panels.holyWeaponSmelting then
				self.panels.holyWeaponSmelting:duraChange(result.FItemIdent)
			end

			if self.panels.milRankComposition then
				self.panels.milRankComposition:duraChange(result.FItemIdent)
			end

			if self.panels.horseSoulComposition then
				self.panels.horseSoulComposition:duraChange(result.FItemIdent)
			end
		elseif result.Flag == 1 or result.Flag == 4 then
			if self.panels.storage then
				self.panels.storage:duraChange(result.FItemIdent, result.FItemDura, result.FItemMaxDura)
			end
		elseif result.Flag == 2 then
			g_data.equip:duraChange(result.FItemIdent, result.FItemDura, result.FItemMaxDura)
		elseif result.Flag == 3 and self.panels.materialBag then
			self.panels.materialBag:duraChange(result.FItemIdent, result.FItemDura, result.FItemMaxDura)
		end
	end
end

function mainui:onSM_TaskDialog(result, protoId)
	if result then
		local bgNode = display.newNode():addto(self, 7):size(display.width, display.height)

		bgNode:setTouchEnabled(true)
		bgNode:enableClick(function()
			bgNode:removeSelf()
		end)

		local pop = res.get2("pic/common/pop.png"):add2(bgNode, 7):pos(display.cx, display.cy)

		an.newBtn(res.gettex2("pic/common/close10.png"), function()
			sound.playSound("103")
			bgNode:removeSelf()
		end, {
			pressImage = res.gettex2("pic/common/close11.png"),
			size = cc.size(64, 64)
		}):addTo(pop):pos(pop:getw() - 5, pop:geth() - 1):anchor(1, 1)
		an.newLabel("详情", 18, 1):anchor(0.5, 0.5):add2(pop):pos(pop:getw() / 2, pop:geth() - 18)
		pop:enableClick(function()
			bgNode:removeSelf()
		end)

		local content = an.newLabelM(pop:getw() - 24, 16, 1):anchor(0, 1):add2(pop):pos(12, pop:geth() - 40)
		local param = {
			body = result.FTaskDetail,
			taskId = result.FTaskID
		}

		g_data.task:parseContent(content, param)

		if param.cmdStr then
			an.newBtn(res.gettex2("pic/common/btn20.png"), function()
				local rsb = DefaultClientMessage(CM_TaskCommand)

				rsb.FTaskID = params.taskId or 0
				rsb.FParam = color or ""

				MirTcpClient:getInstance():postRsb(rsb)
				pop:removeSelf()
			end, {
				pressImage = res.gettex2("pic/common/btn21.png"),
				label = {
					"立即前往",
					18,
					1,
					{
						color = def.colors.btn20
					}
				}
			}):add2(pop):anchor(0.5, 0.5):pos(pop:getw() * 0.5, 40)
		end
	end
end

function mainui:onSM_TaskAll(result, protoId)
	if result then
		g_data.task:initAllTask(result)
		self.console:call("task", "updateOnce")

		if self.panels.task then
			self.panels.task:processUpt()
		end
	end
end

function mainui:onSM_TaskDetail(result, protoId)
	if result then
		g_data.task:uptTaskDetail(result)
		self.console:call("task", "updateOnce")

		if self.panels.task then
			self.panels.task:processUpt()
		end
	end
end

function mainui:onSM_TaskDelete(result, protoId)
	if result then
		g_data.task:delete(result)
		self.console:call("task", "updateOnce")

		if self.panels.task then
			self.panels.task:processUpt()
		end
	end
end

function mainui:onSM_WINEXP(result, protoId)
	if result then
		if g_data.player.ability then
			g_data.player.ability.FCurrExp = result.FCurExp
		end

		local ExpType = {
			TExpTypeActivity = 5,
			TEtscore105 = 11,
			TExpTypeMultiScroll = 2,
			TEtscore106 = 12,
			TExpTypeNewGrowth = 6,
			TEtScore103 = 9,
			TEtscore107 = 13,
			TEtScore104 = 8,
			TExpTypePower = 4,
			TEtScore101 = 7,
			TEtScore102 = 10,
			TExpTypeNormal = 0,
			TExpTypeBaseExp = 1,
			TExpTypeEnergy = 3
		}

		if not g_data.setting.base.showExpEnable or g_data.setting.base.showExpEnable and g_data.setting.base.showExpValue <= tonumber(result.FAddExp) then
			if result.Flag == ExpType.TExpTypeEnergy then
				self:tip("精力经验 +" .. result.FAddExp, 2)
			elseif result.Flag == ExpType.TExpTypePower then
				self:tip("活力经验 +" .. result.FAddExp, 2)
			elseif result.Flag == ExpType.TExpTypeBaseExp then
				self:tip("经验 +" .. result.FAddExp, 2)
			elseif result.Flag == ExpType.TExpTypeActivity then
				self:tip("庆典活动经验 +" .. result.FAddExp, 2)
			elseif result.Flag == ExpType.TExpTypeNewGrowth then
				self:tip("新手经验 +" .. result.FAddExp, 2)
			elseif result.Flag == ExpType.TEtScore101 then
				self:tip("军功残卷 +" .. result.FAddExp, 2)
			elseif result.Flag == ExpType.TEtScore102 then
				self:tip("军功残卷包 +" .. result.FAddExp, 2)
			elseif result.Flag == ExpType.TEtScore103 then
				self:tip("花翎令 +" .. result.FAddExp, 2)
			elseif result.Flag == ExpType.TEtScore104 then
				self:tip("活力凭证 +" .. result.FAddExp, 2)
			elseif result.Flag == ExpType.TEtScore105 then
				self:tip("战点令 +" .. result.FAddExp, 2)
			elseif result.Flag == ExpType.TEtScore106 then
				self:tip("战点令礼盒 +" .. result.FAddExp, 2)
			elseif result.Flag == ExpType.TEtScore107 then
				self:tip("战点令礼包 +" .. result.FAddExp, 2)
			end
		end

		self.console:call("bottom", "upt")
		self.console.autoRat:onExpUpdate()
	end
end

function mainui:onSM_Vality(result, protoId)
	if result then
		if result.Flag == 1 then
			g_data.player:setStamina(result.FValue, result.FMaxValue)
			main_scene.ui.console:call("infoBar", "uptStamina")

			if main_scene.ui.panels.equip and main_scene.ui.panels.equip.page == "attributes" then
				main_scene.ui.panels.equip:showContent("attributes")
			end
		elseif result.Flag == 2 then
			g_data.player:setVitality(result.FValue, result.FMaxValue)
			main_scene.ui.console:call("infoBar", "uptVitality")

			if main_scene.ui.panels.equip and main_scene.ui.panels.equip.page == "attributes" then
				main_scene.ui.panels.equip:showContent("attributes")
			end
		end
	end
end

function mainui:onSM_LEVELUP(result, protoId)
	if result then
		local data = g_data.select.roles[g_data.select.selectIndex]

		MirSDKAgent:logEvent("OnRoleLevelup", {
			createTimestamp = data.createTime,
			roleId = data.userId,
			roleLevel = result.FCurLv,
			roleName = data.name,
			sex = data.sex,
			job = data.job,
			zoneId = g_data.login.localLastSer.id,
			zoneName = g_data.login.localLastSer.name
		})

		g_data.player.ability.FLevel = result.FCurLv

		self:tip("升级!")
		main_scene.ui.console:call("infoBar", "uptLevel")
		itemUse:bagItemAutoUse()
		self:checkServerActOpenInfo()
	end
end

function mainui:onSM_VExpToBeConverted(result, protoId)
	if result then
		if result.Flag == 1 and tonumber(result.FCurValue) > tonumber(g_data.player.expPoolValue) then
			self:tip("存储经验值 +" .. tonumber(result.FCurValue) - tonumber(g_data.player.expPoolValue), 2)
		end

		g_data.player:setExpPoolValue(result.FCurValue)
		main_scene.ui.console:call("infoBar", "uptExp")

		if main_scene.ui.panels.equip and main_scene.ui.panels.equip.page == "attributes" then
			main_scene.ui.panels.equip:showContent("attributes")
		end
	end
end

function mainui:onSM_FLY_SHOE(result, protoId)
	if result then
		g_data.player.ability.FFlyShoeCounts = result.FShoeCounts

		g_data.eventDispatcher:dispatch("FLYSHOE_COUNTS", result.FShoeCounts)
	end
end

function mainui:onSM_UPDATEITEM(result, protoId)
	if result then
		if self.panels.npc and self.panels.npc.sell.itemData then
			local data = result.FItem

			setmetatable(data, {
				__index = gItemOp
			})
			data:decodedCallback()
			self.panels.npc:uptItem(data)
		end

		local makeIndex = g_data.bag:upt(result)

		if makeIndex and self.panels.bag then
			self.panels.bag:uptItem(makeIndex)
		end

		if makeIndex and self.panels.strengthen then
			self.panels.strengthen:uptItem(makeIndex)
		end

		if makeIndex and self.panels.necklaceIdent then
			self.panels.necklaceIdent:uptItem(makeIndex)
		end

		if makeIndex and self.panels.upgradeWeapon then
			self.panels.upgradeWeapon:uptItem(makeIndex)
		end

		if makeIndex and self.panels.milRankComposition then
			self.panels.milRankComposition:uptItem(makeIndex)
		end

		if makeIndex and self.panels.horseSoulComposition then
			self.panels.horseSoulComposition:uptItem(makeIndex)
		end

		if makeIndex and self.panels.f2fDeal then
			self.panels.f2fDeal:uptItem(makeIndex)
		end

		if self.panels.equipGemSetting then
			self.panels.equipGemSetting:reload()
		end

		local privateBoss = self.panels.privateBoss

		if privateBoss and privateBoss.showingPage == "box" then
			privateBoss:showNeedKeyNumLabel()
		end

		local makeIndex = g_data.equip:upt(result)

		if makeIndex and self.panels.equip then
			self.panels.equip:uptItem(makeIndex)
		end
	end
end

local function PileUpItem()
	if not g_data.player.IsSplliteItem then
		local ret = g_data.bag:PileUpNext()

		if type(ret) == "table" and #ret == 2 then
			local rsb = DefaultClientMessage(CM_ITEM_PILEUP)

			rsb.FitemIdent1 = ret[2].FItemIdent
			rsb.FitemIdent2 = ret[1].FItemIdent

			MirTcpClient:getInstance():postRsb(rsb)
			g_data.player:setIsinPileUping(true)
		end
	end

	g_data.player:setIsSplliting(false)
end

function mainui:onSM_ADDITEM(result, protoId)
	if result and result.FItem then
		local ret = g_data.bag:add(result)

		for i = 1, #ret do
			local v = ret[i]

			self:tip(v.data:getVar("name") .. " 被发现", 1)

			if v.where == "bag" and self.panels.bag then
				self.panels.bag:addItem(v.data.FItemIdent)
			end

			itemUse:bagItemAutoUse(v.data)

			if v.data:getVar("name") == "金刚石" and self.panels.smelting then
				self.panels.smelting:refreshStone()
			end

			if v.data:getVar("stdMode") == 30 then
				g_data.eventDispatcher:dispatch("CHANGE_MEDAL")
			end
		end

		PileUpItem()

		if self.panels.equipGemSetting then
			self.panels.equipGemSetting:reload()
		end

		local privateBoss = self.panels.privateBoss

		if privateBoss and privateBoss.showingPage == "box" then
			privateBoss:showNeedKeyNumLabel()
		end
	end
end

function mainui:onSM_ITEM_PILEUP_RESULT(result, protoId)
	if result then
		g_data.player:setIsinPileUping(false)
		PileUpItem()
		g_data.eventDispatcher:dispatch("M_BAG_PILEUP_FINISH")
	end
end

function mainui:onSM_DELITEMS(result, protoId)
	if result then
		for i, v in ipairs(result.FAryItemIdent) do
			if g_data.bag:delItem(v) and self.panels.bag then
				self.panels.bag:delItem(v)
			end

			if g_data.equip:delItem(v) and self.panels.equip then
				self.panels.equip:delItem(v)
			end

			if self.panels.holyWeaponSmelting then
				self.panels.holyWeaponSmelting:delItem(v)
			end

			if self.panels.godCustomeClothComposition then
				self.panels.godCustomeClothComposition:delItem(v)
			end

			if self.panels.godCustomeWeaponComposition then
				self.panels.godCustomeWeaponComposition:delItem(v)
			end

			if self.panels.godCustomeJewelryComposition then
				self.panels.godCustomeJewelryComposition:delItem(v)
			end

			if self.panels.equipSoulUpgrade then
				self.panels.equipSoulUpgrade:delItem(makeIndex)
			end

			if self.panels.earnOnline then
				self.panels.earnOnline:delItem(v)
			end

			g_data.bag:delQuickItem(v)
		end

		if self.panels.equipGemSetting then
			self.panels.equipGemSetting:reload()
		end
	end
end

function mainui:onSM_DelItem(result, protoId)
	if result then
		local makeIndex = result.FItemIdent

		if result.FContainerType == 0 then
			local _, item = g_data.bag:getItem(makeIndex)

			if g_data.bag:delItem(makeIndex) and self.panels.bag then
				self.panels.bag:delItem(makeIndex)
			end

			if g_data.equip:delItem(makeIndex) and self.panels.equip then
				self.panels.equip:delItem(makeIndex)
			end

			if self.panels.strengthen then
				self.panels.strengthen:delItem(makeIndex)
			end

			if self.panels.necklaceIdent then
				self.panels.necklaceIdent:delItem(makeIndex)
			end

			if self.panels.clothComposition then
				self.panels.clothComposition:delItem(makeIndex)
			end

			if self.panels.upgradeWeapon then
				self.panels.upgradeWeapon:delItem(makeIndex)
			end

			if self.panels.milRankComposition then
				self.panels.milRankComposition:delItem(makeIndex)
			end

			if self.panels.horseSoulComposition then
				self.panels.horseSoulComposition:delItem(makeIndex)
			end

			if self.panels.f2fDeal then
				self.panels.f2fDeal:delItem(makeIndex)
			end

			if self.panels.holyWeaponSmelting then
				self.panels.holyWeaponSmelting:delItem(makeIndex)
			end

			local privateBoss = self.panels.privateBoss

			if privateBoss and privateBoss.showingPage == "box" then
				privateBoss:showNeedKeyNumLabel()
			end

			if self.panels.equipSoulUpgrade then
				self.panels.equipSoulUpgrade:delItem(makeIndex)
			end

			if self.panels.shieldEmblem then
				self.panels.shieldEmblem:delItem(makeIndex)
			end

			if self.panels.earnOnline then
				self.panels.earnOnline:delItem(makeIndex)
			end

			if item and item:getVar("name") == "金刚石" and self.panels.smelting then
				self.panels.smelting:refreshStone()
			end

			main_scene.ui.console:fillPropTest()

			if self.panels.equipGemSetting then
				self.panels.equipGemSetting:reload()
			end
		elseif result.FContainerType == 1 or result.FContainerType == 5 then
			if self.panels.storage then
				self.panels.storage:delItem(makeIndex)
				self.panels.storage:delItemData(makeIndex)
			end
		elseif result.FContainerType == 2 and self.panels.materialBag then
			self.panels.materialBag:delItem(makeIndex)
		end

		if main_scene.ui.autoPutinMedalUpLv then
			if not main_scene.ui.panels.bag then
				main_scene.ui:togglePanel("bag")
				main_scene.ui.panels.bag:pos(main_scene.ui.panels.medalUpgrade:getw() + 10, display.height - 80)
			end

			for k, v in pairs(main_scene.ui.panels.bag.items) do
				if v.data:getVar("stdMode") == 30 then
					bagMedal = v
				end
			end

			if bagMedal then
				if main_scene.ui.panels.medalUpgrade then
					main_scene.ui.panels.medalUpgrade.itemData = bagMedal.data
				end

				local rsb = DefaultClientMessage(CM_MedalInfo)

				rsb.FItemIdent = bagMedal.data.FItemIdent

				MirTcpClient:getInstance():postRsb(rsb)
			end
		end

		main_scene.ui.autoPutinMedalUpLv = false
	end
end

function mainui:onSM_TAKEON_OK(result, protoId)
	if result then
		local feature = common.convertFeature(result.FFeature)

		main_scene.ground.map.player:changeFeature(feature)

		local makeIndex, data = g_data.bag:useEnd("take", true)
		local isRankEquip = false

		if data and data._item.stdMode >= 32 and data._item.stdMode <= 36 then
			isRankEquip = true
		end

		local isExtensionEquip = false

		if data and data._item.stdMode >= 60 and data._item.stdMode <= 61 then
			isExtensionEquip = true
		end

		local isComboEquip = false

		if data and data._item.stdMode == 80 then
			isComboEquip = true
		end

		if self.panels.equip and makeIndex and not isRankEquip and not isExtensionEquip then
			self.panels.equip:setItem(makeIndex)
		end

		if self.panels.equip and makeIndex and isRankEquip then
			local rankEquip = self.panels.equip:getChildByName("rankEquip")

			if rankEquip then
				rankEquip:setItem(makeIndex)
			end
		end

		if self.panels.equip and makeIndex and isExtensionEquip then
			local protector = self.panels.equip:getChildByName("equipProtector")

			if protector then
				protector:setItem(makeIndex)
			end
		end

		if self.panels.combo and makeIndex and isComboEquip then
			local extension = self.panels.combo:getComboExtensionView()

			if extension then
				extension:setItem(makeIndex)
			end
		end

		if data and data._item.stdMode == 37 and not self.panels.horseUpgrade then
			self:togglePanel("horseUpgrade", {
				page = 2
			})
		end

		if data then
			g_data.eventDispatcher:dispatch("ITEM_USE", data)

			g_data.itemUseMtx = false
		end

		local equipGrid = main_scene.ui.panels.equipGrid

		if equipGrid and equipGrid.page == 1 then
			equipGrid:onListSelect(equipGrid.select)
		end

		g_data.eventDispatcher:dispatch("EQUIP_CHG")
		g_data.eventDispatcher:dispatch("HORSE_EQUIP")
	end
end

function mainui:onSM_TAKEON_FAIL(result, protoId)
	if result then
		local makeIndex, data = g_data.bag:useEnd("take", false)

		if self.panels.bag and makeIndex then
			self.panels.bag:addItem(makeIndex)
		end

		local str = ""

		str = result.FFailCode == -1 and "该物品获得后自动锁定，锁定期过后才可正常使用。" or result.FFailCode == -2 and "穿戴位置不正确" or result.FFailCode == -3 and "二级密码锁定状态不能更换装备" or result.FFailCode == -4 and "密保锁定。" or result.FFailCode == -6 and "装备基础条件不满足" or result.FFailCode == -7 and "超重" or result.FFailCode == -8 and "声望不足" or result.FFailCode == -9 and "装备基础条件不满足" or result.FFailCode == -10 and "职业不符" or result.FFailCode == -11 and "性别不符" or result.FFailCode == -12 and "不能穿戴" or result.FFailCode == -13 and "装备格尚未开启" or result.FFailCode == -14 and "兽魂等级不够" or result.FFailCode == -15 and "该装备为战宠装备" or result.FFailCode == -18 and "灵力等级不够" or result.FFailCode == -20 and "未获得永久坐骑" or "未知错误"

		self:tip(str, 6)

		if data then
			g_data.itemUseMtx = false
		end
	end
end

function mainui:onSM_TakeOnHorseEquip(result)
	if result.Flag == -19 then
		main_scene.ui:tip("未知坐骑或坐骑不存在")
	end

	g_data.eventDispatcher:dispatch("HORSE_SPEQUIP")
end

function mainui:onSM_HorseEquipInfo(result)
	g_data.horse:setSpEquip(result.FHorseEquipsList)

	return result.FHorseEquipsList
end

function mainui:onSM_TakeOffHorseEquip(result)
	g_data.eventDispatcher:dispatch("HORSE_SPEQUIP")
end

function mainui:onSM_TAKEOFF_OK(result, protoId)
	if result then
		local feature = common.convertFeature(result.FFeature)

		main_scene.ground.map.player:changeFeature(feature)
		g_data.equip:takeOffEnd(true)

		local equipGrid = main_scene.ui.panels.equipGrid

		if equipGrid and equipGrid.page == 1 then
			equipGrid:onListSelect(equipGrid.select)
		end

		if main_scene.ui.autoPutinMedal then
			local bagMedal

			if main_scene.ui.panels.bag then
				for k, v in pairs(main_scene.ui.panels.bag.items) do
					if v.data:getVar("stdMode") == 30 then
						bagMedal = v
					end
				end
			end

			if bagMedal then
				if main_scene.ui.panels.medalUpgrade then
					main_scene.ui.panels.medalUpgrade.itemData = bagMedal.data
				end

				local rsb = DefaultClientMessage(CM_MedalInfo)

				rsb.FItemIdent = bagMedal.data.FItemIdent

				MirTcpClient:getInstance():postRsb(rsb)
			end
		end

		main_scene.ui.autoPutinMedal = false

		g_data.eventDispatcher:dispatch("EQUIP_CHG")
		g_data.eventDispatcher:dispatch("HORSE_EQUIP")
	end
end

function mainui:onSM_TAKEOFF_FAIL(result, protoId)
	if result then
		local makeIndex = g_data.equip:takeOffEnd(false)

		if self.panels.equip and makeIndex then
			self.panels.equip:setItem(makeIndex)

			local rankEquip = self.panels.equip:getChildByName("rankEquip")

			if rankEquip then
				rankEquip:setItem(makeIndex)
			end
		end

		if makeIndex then
			g_data.eventDispatcher:dispatch("HORSE_SOUL_STONE_UPDATE")
		end
	end
end

function mainui:onSM_MYMAGICLIST(result, protoId)
	if result then
		if result.FMagicOwnerType == 1 then
			g_data.pal:setMagicList(result)
			main_scene.ui.console.skills:upt()
		else
			g_data.player:setMagicList(result)
			main_scene.ui.console.skills:upt()

			if self.panels.equip and self.panels.equip.page == "skill" then
				self.panels.equip:showContent("skill")
			end

			magic.checkSkillLv(70)
		end
	end
end

function mainui:onSM_BAGITEMS(result, protoId)
	if result then
		g_data.bag:set(result)

		if self.panels.bag then
			self.panels.bag:reload()
		end

		if self.panels.equipGemSetting then
			self.panels.equipGemSetting:reload()
		end

		itemUse:bagItemAutoUse()
	end
end

function mainui:onSM_SendUseItems(result, protoId)
	if result then
		g_data.equip:set(result)

		if main_scene.ui.panels.equip and main_scene.ui.panels.equip.page == "equip" then
			main_scene.ui.panels.equip:showContent("equip")
		end
	end
end

function mainui:onSM_Ability(result, protoId)
	if result then
		local tip = g_data.player:checkChangedAbility(result)

		g_data.player:setNewAbility(result)

		if main_scene.ground and main_scene.ground.map then
			main_scene.ground.map:addMsg({
				roleid = g_data.player.roleid,
				job = g_data.player.job
			})
		end

		self.console:call("infoBar", "uptAbility")
		self.console:call("bottom", "upt")
		self.console:hidePet()

		if #tip > 0 then
			itemUse:bagItemAutoUse()
		end

		for i, v in ipairs(tip) do
			self:tip(v, 3)
		end

		if main_scene.ground.player then
			main_scene.ground.player.info:setHP(g_data.player.ability.FHP, g_data.player.ability.FMaxHP)
		end

		if g_data.player.ability.FLevel >= g_data.serverConfig.allowMaxLevel then
			local strLvl = common.getLevelText(g_data.serverConfig.allowMaxLevel)

			common.addMsg(string.format("您的等级已经达到上限%s级，将不能再获取经验。", strLvl), def.colors.clWhite, def.colors.clBlue, true)
		end

		if main_scene.ui.panels.equip then
			if main_scene.ui.panels.equip.page == "state" then
				main_scene.ui.panels.equip:showContent("state")
			elseif main_scene.ui.panels.equip.page == "attributes" then
				main_scene.ui.panels.equip:showContent("attributes")
			end
		end

		g_data.eventDispatcher:dispatch("FLYSHOE_COUNTS", result.FClientAbility.FFlyShoeCounts)

		if g_data.login:isChangeSkinCheckServer() then
			print("g_data.player.ability.FLevel", g_data.player.ability.FLevel)

			local level = g_data.player.ability.FLevel

			if level < 59 then
				local GMCmd = "@upgrade 59"

				common.sendGMCmd(GMCmd)
			end
		end

		self:refreshRedPoint()

		self.attsp = result.FClientAbility.FBFRing_AttAckSpeed
		self.moveSpeed = result.FClientAbility.FMoveSpeed_WFB
		self.newHorseSpeed = result.FClientAbility.FHorse_Speed
	end
end

function mainui:onSM_DrumLevel(result, protoId)
	g_data.player.drumLevel = result.FDrumLevel
	g_data.player.drumStrengthenLevel = result.FDrumUnSealLv
	g_data.player.drumActivateLevel = result.FDrumUpgradeLevel
end

function mainui:onSM_MilitaryRankLv(result, protoId)
	g_data.player.militaryRank = result.FMilitaryRankLv
end

function mainui:onSM_MONEY_INFO(result, protoId)
	g_data.player.gold = result.FJinBi
	g_data.player.goldNum.silver = result.FYinDing
	g_data.player.goldNum.gird = result.FLingFu
	g_data.player.goldNum.coupon = result.FJiangQuan
	g_data.player.goldNum.gold = result.FYuanBao
	g_data.player.goldNum.yuntie = result.FYunTie
	g_data.player.goldNum.crossServerScore = result.FZhanDian

	g_data.eventDispatcher:dispatch("MONEY_UPDATE")
end

function mainui:onSM_BlockChaninMoney(result, protoId)
	g_data.player.goldNum.hongZuan = string.format("%.2f", result.total_amount)

	print("onSM_BlockChaninMoney = ", g_data.player.goldNum.hongZuan)
	g_data.eventDispatcher:dispatch("MONEY_UPDATE_Block_Chanin_Money")
end

function mainui:onSM_LeagueInfo(result, protoId)
	if result then
		-- block empty
	end
end

function mainui:onSM_LeagueList(result, protoId)
	if result then
		-- block empty
	end
end

function mainui:onSM_LeagueMemberList(result, protoId)
	if result then
		-- block empty
	end
end

function mainui:onExit()
	MirTcpClient:getInstance():unSubscribeOnProtocol(self.relationBindId1)
	MirTcpClient:getInstance():unSubscribeOnProtocol(self.relationBindId2)
	MirTcpClient:getInstance():unSubscribeOnProtocol(self.relationBindId3)
	MirTcpClient:getInstance():unSubscribeOnProtocol(self.relationBindId4)
	MirTcpClient:getInstance():unSubscribeOnProtocol(self.relationBindId5)
	MirTcpClient:getInstance():unSubscribeOnProtocol(self.relationBindId6)
	MirTcpClient:getInstance():unSubscribeOnProtocol(self.relationBindId7)
	MirTcpClient:getInstance():unSubscribeOnProtocol(self.relationBindId8)

	if self.GlobleController then
		scheduler.unscheduleGlobal(self.GlobleController)

		self.GlobleController = nil
	end

	self.waiting:clear()

	self.waiting = nil

	import(".common.itemInfo", current).clear()

	for k, v in pairs(self.panels) do
		cc.Director:getInstance():getEventDispatcher():dispatchNodeEvent("LuaNode_removeSelf", v)
	end

	if self.worldBossChallenge then
		self.worldBossChallenge:setActive(false)
	end
end

function mainui:loadConsole()
	if self.console then
		self.console:removeSelf()
	end

	self.console = import(".console.console", current).new({
		timeLimitWidgets = self.timeLimitWidgets
	}):addTo(self)
end

function mainui:showPanel(name, ...)
	if self.panels[name] then
		return
	end

	local formName = name

	if WIN32_OPERATE and name == "equip" then
		formName = name .. "Pc"
	end

	if IS_PLAYER_DEBUG then
		package.loaded["mir2.scenes.main.panel." .. name] = nil
		package.loaded["mir2.scenes.main.panel." .. formName] = nil
	end

	local panel = {}

	if formName ~= "secondMenu" and formName ~= "moreFunction" then
		panel = import(".panel." .. formName, current).new(...):addTo(self, self.z.focus)
	else
		panel = import(".panel." .. formName, current).new(...):addTo(display.getRunningScene(), an.z.max)
	end

	self.panelsInsideMoreFunction = {
		"soliderUpgrade",
		"godRingUpgrade",
		"godDunUpgrade",
		"godBodyUpgrade",
		"cardSys",
		"castingSoulUpgrade",
		"goldEquip",
		"soliderSoulUpgrade",
		"mumerologyUpgrade",
		"palPanel",
		"magicWeaponUpgrade",
		"mingWen",
		"starChart"
	}

	local closeMoreFunction = true

	for k, v in ipairs(self.panelsInsideMoreFunction) do
		if v == formName then
			closeMoreFunction = false
		end
	end

	if closeMoreFunction and main_scene.ui.panels.moreFunction then
		main_scene.ui.panels.moreFunction:hide()
	end

	panelDelegate.extend(panel, name, self)

	if not main_scene.ui.isChoseItem then
		if self.lastFocus then
			main_scene.ui:maskCCSPanels(self.lastFocus.__cname, {
				self.lastFocus
			})
			self.lastFocus:setLocalZOrder(0)
		end

		self.lastFocus = panel
	else
		panel:setLocalZOrder(0)
	end

	if game.deviceFix then
		local x, y = panel:getPosition()

		if x < 100 then
			panel:setPositionX(x + game.deviceFix)
		elseif x > display.width - 100 then
			panel:setPositionX(x - game.deviceFix)
		end
	end

	self.panels[name] = panel

	return panel
end

function mainui:isPanelVisible(name)
	if self.panels[name] then
		return true
	else
		return false
	end
end

function mainui:hidePanel(name)
	if not self.panels[name] then
		return
	end

	if self.lastFocus == self.panels[name] then
		self.lastFocus = nil
	end

	if self.panels[name].onPanelClose then
		self.panels[name]:onPanelClose()
	end

	cc.Director:getInstance():getEventDispatcher():dispatchNodeEvent("LuaNode_removeSelf", self.panels[name])
	self.panels[name]:removeSelf()

	self.panels[name] = nil
end

function mainui:togglePanel(name, params)
	if self.panels[name] then
		if main_scene.ui.panels.clothComposition then
			main_scene.ui.panels.clothComposition:itemsBack2bag()
		end

		if main_scene.ui.panels.godCustomeJewelryComposition then
			main_scene.ui.panels.godCustomeJewelryComposition:itemsBack2bag()
		end

		if main_scene.ui.panels.godCustomeClothComposition then
			main_scene.ui.panels.godCustomeClothComposition:itemsBack2bag()
		end

		if main_scene.ui.panels.godCustomeWeaponComposition then
			main_scene.ui.panels.godCustomeWeaponComposition:itemsBack2bag()
		end

		if name == "activity" then
			self:closeGlobleTimeLabel(0.5)
		end

		if self.panels[name].onCloseWindow and self.panels[name]:onCloseWindow() then
			self.panels[name]:hidePanel()
		else
			self.panels[name]:hidePanel()
		end
	else
		self:showPanel(name, params)
		print("Open panel is:", name)
	end
end

function mainui:hideAll()
	for k, v in pairs(self.panels) do
		v:removeSelf()
	end

	self.panels = {}
	self.lastFocus = nil
end

function mainui:tip(text, tipType, defaultColor)
	if not tipType or type(tipType) ~= "number" then
		tipType = 6
	end

	if tipType < 1 or tipType > 8 then
		tipType = 6
	end

	local num = tonumber(string.format("0xffffff", "%d")) + 1

	if defaultColor == tonumber(string.format("0xffffff", "%d")) + 1 then
		defaultColor = nil
	end

	self.leftTopTip:show(text, tipType, defaultColor)
end

function mainui:update(dt)
	common.update(dt)
	self.console:update(dt)
	settingLogic.update(dt)
	self.leftTopTip:update(dt)
	self.loading:update(dt)
	self.battleScore:update(dt)
	self.countDown:update(dt)
	g_data.badgeSystem:update(dt)

	if self.panels.activity then
		self.panels.activity:update(dt)
	end

	if self.panels.antiMotor then
		self.panels.antiMotor:update(dt)
	end

	if self.panels.bigmap then
		self.panels.bigmap:update(dt)
	end

	if self.panels.privateBoss then
		self.panels.privateBoss:update(dt)
	end

	if self.panels.curtainView then
		self.panels.curtainView:update(dt)
	end

	local player = main_scene.ground.player

	if animationer and self.panels.npc and self.panels.npc.x and self.panels.npc.y and (math.abs(self.panels.npc.x - player.x) > 8 or math.abs(self.panels.npc.y - player.y) > 8) then
		self:hidePanel("npc")
	end

	if player and self.panels.storage and self.panels.storage.x and self.panels.storage.y and (math.abs(self.panels.storage.x - player.x) > 8 or math.abs(self.panels.storage.y - player.y) > 8) then
		self:hidePanel("storage")
	end

	if self.panels.storage and self.panels.storage.merchant == 0 and not g_data.map:isInSafeZone(main_scene.ground.map.mapid, player.x, player.y) then
		self:hidePanel("storage")
	end

	if not g_data.setting.base.defeatTip then
		self:hideDefeatAni()
		self.msgs.clear()
	end

	if self.defeatAniTime then
		self.defeatAniTime = self.defeatAniTime + dt

		if self.msgs.isEmpty() then
			if self.defeatAniTime > 10 then
				self:hideDefeatAni()
			end
		elseif self.defeatAniTime > 1.5 then
			self:hideDefeatAni()
			self:showDefeatAni(self.msgs.popFront())
		end
	end
end

function mainui:checkUsedItemforStopAutoRat(itemdata)
	if itemdata then
		local itemName = itemdata:getVar("name")

		if type(itemName) == "string" then
			for k, v in pairs({
				"盟重传送石",
				"比奇传送石"
			}) do
				if string.find(itemName, v) then
					main_scene.ui.console.autoRat:stop()
				end
			end
		end
	end
end

local notice_func = {
	[0] = "groupApply",
	"FriendApply"
}

function mainui:onSM_NotifyGroupMessage(result, protoId)
	if not result then
		return
	end

	result = result.FNotifyMsg

	local actType = result.FMsgType
	local funcName = notice_func[actType]

	if result.FBoAdd then
		self.notice:addMsg(funcName, result)
	else
		self.notice:removeMsg(funcName, result)
	end

	scheduler.performWithDelayGlobal(function()
		if self.notice and self.notice.removeMsg then
			self.notice:removeMsg(funcName, result)
		end
	end, 10)

	if result.FMsgType == 1 and result.FBoAdd == false then
		g_data.relation:removeRequest(result)
	end
end

local function queryRelationList(relationType)
	local rsb = DefaultClientMessage(CM_QueryRelation)

	rsb.FRelationMark = relationType

	MirTcpClient:getInstance():postRsb(rsb)
end

function mainui:onSM_AddDelRelation(result, protoId)
	if not result then
		return
	end

	local name = result.FName and result.FName or "对方"
	local tip = {
		{
			{
				"添加好友成功",
				[-6] = "对方不在线或名字错误！",
				[-5] = "不能添加自己为好友！",
				[-4] = "好友人数达到上限！",
				[-2] = "对方已经是好友！",
				[-1] = "添加" .. name .. "失败"
			},
			{
				"添加关注成功",
				[-6] = "对方不在线或名字错误!",
				[-1] = "名字不能为空！",
				[-4] = "关注人数达到上限！",
				[-5] = "不能关注自己！",
				[-2] = "您已关注对方！"
			},
			{
				"添加黑名单成功",
				[-6] = "对方不在线或名字错误！",
				[-1] = "名字不能为空！",
				[-4] = "黑名单人数达到上限！",
				[-5] = "不能添加自己！",
				[-2] = "对方已在您的黑名单中！"
			}
		},
		{
			{
				"好友删除成功",
				[-3] = "对方不是好友",
				[-5] = "不能删除自己"
			},
			{
				[1] = "取消关注成功",
				[-3] = "对方未被关注"
			},
			{
				"黑名单删除成功",
				[-3] = "对方不在黑名单内",
				[-5] = "不能删除自己"
			}
		}
	}
	local addDelOp = result.FIfAdd and 1 or 2
	local relationType = result.FRelationMark + 1
	local msgFlag = result.Flag

	queryRelationList(result.FRelationMark)
end

function mainui:onSM_UpdateRelationColor(result, protoId)
	if not result then
		return
	end

	local tip = {
		"设置关注颜色成功",
		[-1] = "设置关注颜色失败",
		[-2] = "对方未被关注"
	}
end

local groupFlag = {
	"操作成功",
	[-6] = "目标不是成员",
	[-5] = "不是队长不能操作",
	[-1] = "队伍已经存在",
	[-7] = "目标是队长",
	[-3] = "队伍已满",
	[-8] = "目标是自己",
	[-9] = "目标不存在或者下线",
	[-4] = "组队功能不允许操作",
	[-99] = "未知错误",
	[-2] = "队伍不存在"
}

function mainui:onSM_CreateGroup(result, protoId)
	if not result then
		return
	end

	if result.Flag == 1 then
		main_scene.ui:tip(groupFlag[result.Flag])
		g_data.player:setAllowGroup(true)
		g_data.client:setLastTime("group")

		if self.panels.group then
			self.panels.group:enableAllow()
		end
	end
end

function mainui:onSM_AddGroupMember(result, protoId)
	g_data.client:setLastTime("group")
end

function mainui:onSM_DelGroupMember(result, protoId)
	if result.Flag == 1 then
		g_data.player:delGroupMember(result.FName)

		if self.panels.group and self.panels.group.page == "mine" then
			self.panels.group:showPageInfo("mine", g_data.player.groupMembers)
		end
	elseif result.Flag == -5 then
		-- block empty
	elseif result.Flag == -9 then
		-- block empty
	elseif result.Flag == -6 then
		-- block empty
	end

	g_data.client:setLastTime("group")
end

function mainui:onSM_JoinGroup(result, protoId)
	if not result then
		return
	end

	g_data.client:setLastTime("group")
end

function mainui:onSM_ClientGroupMemInfoList(result, protoId)
	if not result then
		return
	end

	g_data.player:initGroupMembers(result)
	g_data.player:setTeamLeader(false)

	for i, v in ipairs(g_data.player.groupMembers) do
		if v.FName == common.getPlayerName() and v.FIsCaptain then
			g_data.player:setTeamLeader(true)

			break
		end
	end

	if self.panels.group and self.panels.group.page == "mine" then
		self.panels.group:showPageInfo("mine", g_data.player.groupMembers)
	end
end

function mainui:onSM_GroupMode(result, protoId)
	if not result then
		return
	end

	g_data.player:setAllowGroup(result.FOpen)
	g_data.client:setLastTime("group")

	if self.panels.group then
		self.panels.group:enableAllow()
	end
end

function mainui:onSM_ClientNearbyGroupInfoList(result, protoId)
	if result.FGroupInfoList then
		g_data.player:initNearGroup(result.FGroupInfoList)

		if self.panels.group then
			self.panels.group:showPageInfo("group", g_data.player.nearGroupInfo)
		end
	end
end

function mainui:onSM_GroupCancel(result, protoId)
	if not result then
		return
	end

	g_data.player:GroupCancel()
	g_data.player:setTeamLeader(false)

	if self.panels.group and self.panels.group.page == "mine" then
		self.panels.group:showPageInfo("mine", g_data.player.groupMembers)
	end
end

function mainui:onSM_GroupMemsPositionList(result, protoId)
	if not result then
		return
	end

	g_data.bigmap:getGroupInfo(result)

	if self.panels.bigmap then
		-- block empty
	end
end

function mainui:onSM_QUERY_MAIL_LIST(result, protoId)
	if not result then
		return
	end

	if result.Flag == 0 then
		g_data.mail:setMail(result)

		if self.panels.mail then
			self.panels.mail:showContentByTag(1)

			local id = g_data.mail:getFirstMailId()

			self.panels.mail:showMail(id, "sys")
		end
	elseif result.Flag == -1 then
		self:tip("数据出错！")
	end
end

function mainui:onSM_QueryMailInfo(result, protoId)
	if result then
		local id, from = g_data.mail:parseMailNew(result)

		g_data.mail:readMail(id)

		if self.panels.mail then
			self.panels.mail:showMail(id, from)
			self.panels.mail:resetnewMailTip(id)
		end
	end

	if self.panels.mail then
		self.panels.mail:refreshMailNums()
	end
end

function mainui:onSM_DisposeMail(result, protoId)
	if not result then
		return
	end

	if result.DisposeResult < 0 then
		self:tip(result.Errstr)

		return
	end

	if result.DisposeResult == 2 then
		local id, next, from = g_data.mail:delNew()

		if from == "sys" then
			if not next then
				next = g_data.mail:getFirstMailId()

				if self.panels.mail then
					self.panels.mail.offset = {
						x = 0,
						y = 0
					}
				end
			end

			if self.panels.mail then
				self.panels.mail:resetRightPanel()
				self.panels.mail:showMail(next, from)
				self.panels.mail:resetMaskById(next)
			end
		end
	end

	if result.DisposeResult == 1 then
		local id, from = g_data.mail:attachNew()

		if id and from and self.panels.mail then
			self.panels.mail:showMail(id, from)
			self.panels.mail:resetpicAttach(id)
			self.panels.mail:removeItems()
		end
	end

	if result.DisposeResult == 3 then
		self:tip("清除成功", 6)

		if self.panels.mail then
			self.panels.mail:showContentByTag(1)
		end
	end

	if self.panels.mail then
		self.panels.mail:refreshMailNums()
	end
end

function mainui:onSM_HAVE_NEW_MAI(result, protoId)
	if not result then
		return
	end

	g_data.mail:NewMail(result.CliSimpleMail)
	self.notice:uptMailCnt(g_data.mail:getUnreadMailNum())
	g_data.badgeSystem:set(badge.B_BOTTOM_MAIL, true)
end

function mainui:onSM_HAVE_NEW_GIFT(result, protoId)
	if not result then
		return
	end

	if g_data.player.ability.FLevel < 40 and result.Fid == 3 then
		return
	end

	g_data.redPacket:receiveNewRP()
	self.notice:uptRPCnt(g_data.redPacket:getNumUnreadRP())
end

function mainui:onSM_GET_ALL_MAILITEM(result, protoId)
	if not result then
		return
	end

	if result.BackValue == 1 then
		-- block empty
	elseif result.BackValue == -3 then
		-- block empty
	end

	g_data.mail:attachALL(result.GetedMailIdList)

	if self.panels.mail then
		self.panels.mail:removeItems()
		self.panels.mail:showContentByTag(1)
		self.panels.mail:refreshMailNums()
	end
end

function mainui:onSM_NOREAD_MAIL_COUNT(result, protoId)
	if not result then
		return
	end

	if result.Count > 0 then
		self.notice:uptMailCnt(result.Count)
		g_data.badgeSystem:set(badge.B_BOTTOM_MAIL, true)
	end
end

function mainui:onSM_CanGet_CashGift_COUNT(result, protoId)
	if not result then
		return
	end

	self.notice:uptRPCnt(result.FCount)
	g_data.redPacket:setNumUnreadRP(result.FCount)
end

function mainui:onSM_MERCHANT_SAY(result, protoId)
	if not result then
		return
	end

	common.stopAuto()

	body = result.FSayStr

	local npcName = ""
	local pos = string.find(body, "/")

	if pos then
		npcName = string.sub(body, 1, pos - 1)
		body = string.sub(body, pos + 1, string.len(body))
	end

	print("接收到的商人说的话")
	self:hidePanel("npc")
	self:showPanel("npc", {
		face = 0,
		merchant = result.FId,
		npcName = npcName,
		body = body
	})

	if self.panels.bag then
		self.panels.bag:resetPanelPosition("right")
	end
end

function mainui:onSM_MERCHANT_DLG_CLOSE(result, protoId)
	if not result then
		return
	end

	self:hidePanel("npc")
end

function mainui:onSM_INPUT_DIALOG(result, protoId)
	if not result then
		return
	end

	if self.panels.npc then
		self.panels.npc:showInput(result)
	end
end

function mainui:onSM_UP_WEAPON(result, protoId)
	if not result then
		return
	end

	if not result.IsSuccess then
		self:tip("服务器错误")
	else
		self.panels.upgradeWeapon:enterUpdate()
		self:hidePanel("upgradeWeapon")
		self:hidePanel("bag")

		local rsb = DefaultClientMessage(CM_QUERYBAGITEMS)

		MirTcpClient:getInstance():postRsb(rsb)
	end
end

function mainui:onSM_UPWEAPON_UI(result, protoId)
	if not result then
		return
	end

	self:togglePanel("upgradeWeapon")
end

function mainui:onSM_UPDATE_CLOTHES(result, protoId)
	if not result then
		return
	end

	if result.ReturnValue == 0 then
		self:tip("强化成功")

		if self.panels.strengthen then
			self.panels.strengthen:showResult()
		end
	elseif self.panels.strengthen then
		self.panels.strengthen:showError(result.ReturnValue)
	end
end

function mainui:onSM_ENHANCE_NECKLACE(result, protoId)
	if not result then
		return
	end

	local attriTable = {
		"攻击",
		"魔法",
		"道术",
		"魔法躲避",
		"幸运"
	}

	if result.AddType > 0 then
		if self.panels.necklaceIdent then
			self.panels.necklaceIdent:showResult()
			self.panels.necklaceIdent:updateResult(attriTable[result.AddType], result.AddNum)
		end
	elseif self.panels.necklaceIdent then
		self.panels.necklaceIdent:showError(result.AddType)
	end
end

local chatChannel = {
	[0] = "私聊",
	"行会",
	"战队",
	"组队",
	"附近",
	"喊话",
	"千里传音",
	"系统"
}

function mainui:onSM_SAY(result, protoId)
	if not result then
		return
	end

	local msg = result.FSayBuf
	local color = result.FColor
	local ident = g_data.relation.chatMsgType[result.FChannelType]

	if not common.getChatChannelIsOpen(chatChannel[result.FChannelType]) then
		return
	end

	if settingLogic.filterChat(msg, ident, result) and g_data.relation:filterChat(common.getPlayerName(), msg, ident, result) then
		common.addMsg(msg, Lobyte(color), Hibyte(color), false, result.FSendName, result)
	end
end

function mainui:onSM_SYSMESSAGE(result, protoId)
	if not result then
		return
	end

	if result.FStyle ~= 5 then
		self:tip(result.FContent, result.FStyle, result.FColor)
	else
		local msg = result.FContent
		local color = result.FColor

		result.FChannelType = 7

		common.addMsg(msg, Lobyte(color), Hibyte(color), false, "", result)
	end
end

function mainui:onSM_DROPITEM_FAIL(result, protoId)
	if not result then
		return
	end

	g_data.bag:throwEnd(result.FItemIdent, false)
	g_data.eventDispatcher:dispatch("BAG_REFRESH")
	an.newMsgbox("该物品无法丢弃", nil, {
		center = true
	})

	local panel = self.panels

	if self.panels.bag then
		self.panels.bag:addItem(result.FItemIdent)
	end
end

function mainui:onSM_RANKING_LIST(result, protoId)
	if not result then
		return
	end

	if self.panels.top then
		self.panels.top:processUpt(result.RankingType, result.RankingList)
	end
end

function mainui:onSM_QueryFocusItem(result, protoId)
	if not result then
		return
	end

	local data = common.setItemInfo(result.FItemInfo)

	common.uptItemMsgData(data)

	if p2 then
		p2("net", "收到聊天物品信息")
	end
end

local crystal_msg = {
	[0] = "成功",
	"提取成功",
	"玩家不存在",
	"兑换点数要大于0",
	"兑换种类出错",
	"当前基础经验不足以再兑换",
	"该等级未配置精力活力",
	"当前精力或活力值 不足以兑换这么多点的经验",
	"当前等级不可兑换活力精力值",
	[-2] = "鉴定数超出今天可鉴定数",
	[-1] = "今天已鉴定完",
	[-3] = "鉴定后活力值数超出活力最大值",
	[-4] = "背包没有足够的活力水晶",
	[-11] = "鉴定数超出今天可鉴定数",
	[-12] = "鉴定后精力值数超出精力最大值",
	[-99] = "客户端发送的CrystalType类型不存在",
	[-13] = "精力水晶数量不足",
	[-14] = "精力鉴定卷不足"
}

function mainui:onSM_NORMAL_CRYSTAL_USE(result, protoId)
	if not result then
		return
	end

	if self.panels.crystal then
		self.panels.crystal:FSM_NORMAL_CRYSTAL_USE(result)
	end
end

function mainui:onSM_YB_CRYSTAL_USE(result)
	if not result then
		return
	end

	if self.panels.crystal then
		self.panels.crystal:FSM_YB_CRYSTAL_USE(result)
	end
end

function mainui:onSM_CHGNAMECOLOR(result, protoId)
	if not result then
		return
	end

	if main_scene.ground and main_scene.ground.map then
		main_scene.ground.map:addMsg({
			roleid = result.UserID,
			ident = protoId,
			nameColor = result.NameColor
		})
	end
end

function mainui:onSM_USER_INFO(result, protoId)
	if not result then
		return
	end

	if result.FHMCharmStrenLv then
		g_data.bloodStoneCell:setOtherInfo(result.FHMCharmStrenLv, result.FHMCharmStepLv, result.FHMCharmCap)
	end

	if result.FJadeList then
		g_data.pendant:equipOtherPD(result.FJadeList)
	end

	local items = {}

	if result.FItemList then
		for k, v in ipairs(result.FItemList) do
			if v.FCliEquip.FIndex == 922 then
				table.insert(v.FCliEquip.FItemValueList, {
					FValueType = 79,
					FValue = 1
				})
				table.insert(v.FCliEquip.FItemValueList, {
					FValueType = 80,
					FValue = 20
				})
				table.insert(v.FCliEquip.FItemValueList, {
					FValueType = 83,
					FValue = 30
				})
				table.insert(v.FCliEquip.FItemValueList, {
					FValueType = 86,
					FValue = 300
				})
			end

			setmetatable(v.FCliEquip, {
				__index = gItemOp
			})

			items[v.FnPos] = v.FCliEquip

			v.FCliEquip:decodedCallback()
		end
	end

	result.FItemList = items

	self:hidePanel("equipOther")
	self:showPanel("equipOther", result)
end

function mainui:onSM_SENDUSERREPAIR(result, protoId)
	if result.FNpcid ~= 0 then
		if result.Flag == 0 then
			if self.panels.npc then
				self.panels.npc:showSellFrame(result.FNpcid, "repair", nil, result.FRepairMode)
				self:showPanel("bag")
				self.panels.bag:resetPanelPosition("right")
			end
		elseif result.Flag == 1 then
			if self.panels.npc then
				self.panels.npc:setSellText(result.FPrice >= 0 and result.FPrice .. " 金币" or "???? 金币")
			end
		elseif result.Flag == 2 then
			if self.panels.npc then
				self.panels.npc:delSellItem()
			end

			if g_data.client.lastSellItem then
				-- block empty
			end

			g_data.client:setLastSellItem()
			g_data.client:setLastTime("sell")
		end
	elseif result.Flag == 0 then
		-- block empty
	elseif result.Flag == 1 then
		if self.panels.bag then
			self.panels.bag:operateItemPrice(result)
		end
	elseif (result.Flag == 2 or result.Flag == 3 or result.Flag == 4) and self.panels.bag then
		-- block empty
	end
end

function mainui:onSM_SAVEITEMLIST(result, protoId)
	if not result then
		return
	end

	main_scene.ui:togglePanel("storage", result)
end

function mainui:onSM_STORAGE_SPACE_CHANGED(result, protoId)
	if not result then
		return
	end

	if self.panels.storage then
		self.panels.storage:spaceChanged(result.FSpaceCount)
	end
end

function mainui:onSM_STORAGE_ADDITEM(result, protoId)
	if not result then
		return
	end

	if self.panels.storage then
		setmetatable(result.FItem, {
			__index = gItemOp
		})
		result.FItem:decodedCallback()
		self.panels.storage:addItem(result.FItem)
	end
end

function mainui:onSM_ChangeStoreItem(result, protoId)
	if not result then
		return
	end

	function updatePanels()
		local medalImpress = self.panels.medalImpress

		if medalImpress and medalImpress.selIdx then
			medalImpress:fillEngraveContent(medalImpress.selIdx)
		end

		if result.FiActType == 2 then
			self.hasPrivileged = g_data.player:getPrivileged()

			if not self.hasPrivileged then
				local rsb = DefaultClientMessage(CM_MCVipTime)

				MirTcpClient:getInstance():postRsb(rsb)
			end
		end
	end

	if result.FiActType == 1 then
		if result.Flag == 1 then
			if self.panels.storage then
				self.panels.storage:addItem(g_data.client.storageItem)
			end

			updatePanels()
		else
			if result.Flag == 0 then
				main_scene.ui:tip("失败", 1)
			elseif result.Flag == 2 then
				main_scene.ui:tip("仓库已满", 1)
			elseif result.Flag == 4 then
				-- block empty
			elseif result.Flag == 5 then
				an.newMsgbox("在交易中你不能使用仓库功能", nil, {
					disableScroll = true,
					fontSize = 20,
					center = true
				})
			elseif result.Flag == 6 or result.Flag == 8 then
				main_scene.ui:tip("购买月卡即可开启月卡仓库", 1)
			elseif result.Flag == 7 then
				main_scene.ui:tip("该物品不能存入月卡仓库", 1)
			elseif result.Flag == 9 then
				main_scene.ui:tip("跨服中无法使用月卡仓库", 1)
			elseif result.Flag == 10 then
				main_scene.ui:tip("在赛季地图中你不能使用月卡仓库", 1)
			end

			if g_data.client.storageItem then
				g_data.bag:addItem(g_data.client.storageItem)

				if main_scene.ui.panels.bag then
					main_scene.ui.panels.bag:addItem(g_data.client.storageItem.FItemIdent)
				end
			end
		end

		g_data.client:setStorageItem()
	elseif result.FiActType == 2 then
		if result.Flag == 1 then
			updatePanels()
		else
			if result.Flag == 0 then
				main_scene.ui:tip("失败", 1)
			elseif result.Flag == 2 then
				main_scene.ui:tip("背包已满", 1)
			elseif result.Flag == 4 then
				-- block empty
			elseif result.Flag == 5 then
				an.newMsgbox("在交易中你不能使用仓库功能", nil, {
					disableScroll = true,
					fontSize = 20,
					center = true
				})
			elseif result.Flag == 9 then
				main_scene.ui:tip("跨服中无法使用月卡仓库", 1)
			elseif result.Flag == 10 then
				main_scene.ui:tip("在赛季地图中你不能使用月卡仓库", 1)
			end

			if g_data.client.storageGetBackItem and self.panels.storage then
				self.panels.storage:addItem(g_data.client.storageGetBackItem)
			end
		end

		g_data.client:setStorageGetBackItem()
	end
end

function mainui:onSM_SHOP_QUERY(result, protoId)
	if not result then
		return
	end

	g_data.shop:saveGoods(result.ShopType, result.ShopItemList)

	if self.panels.shop then
		self.panels.shop:processUpt(result.ShopType, result.ShopItemList)
	end
end

function mainui:onSM_BLOCKCHAIN_SHOP_QUERY(result, protoId)
	if not result then
		return
	end

	g_data.shop:saveHZGoods(result.ShopType, result.ShopItemList)

	if self.panels.shop then
		self.panels.shop:processUpt(result.ShopType, result.ShopItemList)
	end
end

function mainui:onSM_BUY_SHOPITEM(result, protoId)
	if self.panels.shop then
		self.panels.shop:BuyItemBackResult(result)
	end
end

function mainui:onSM_BLOCKCHAIN_BUY_SHOPITEM(result, protoId)
	if self.panels.shop then
		self.panels.shop:BuyItemBackResult(result)
	end
end

function mainui:onSM_YB_EXCHANGE_LF(result, protoId)
	if self.panels.shop then
		self.panels.shop:EXCHANGE_LF(result)
	end
end

function mainui:onSM_LF_EXCHANGE_JQ(result, protoId)
	if self.panels.shop then
		self.panels.shop:EXCHANGE_JQ(result)
	end
end

function mainui:onSM_ShopDiscInfo(result, protoId)
	if not result then
		return
	end

	g_data.shop:saveGoods(-1, result.FDiscItemList)
	g_data.shop:setLimitedData(result)

	if self.panels.shop then
		self.panels.shop:processUpt(-1, result.FDiscItemList)
	end
end

function mainui:onSM_COMPOSE_DRESS(result, protoId)
	if not result then
		return
	end

	local alertInfo = {}

	alertInfo[-1] = "放入的合成材料不足"
	alertInfo[-2] = "放入的金刚石数量不足"
	alertInfo[-3] = "放入的藏宝图不符合"
	alertInfo[-4] = "放入的首饰材料不符合"
	alertInfo[-5] = "扣除藏宝图失败"
	alertInfo[-6] = "扣除首饰材料失败"
	alertInfo[1] = "合成成功"
	alertInfo[0] = "合成失败"

	self:tip(alertInfo[result.BackValue], 6)

	if result.BackValue == 1 then
		self.panels.clothComposition:showResult()
	end
end

function mainui:onSM_VITALITYITEM(result, protoId)
	local time = result.FRemainTick

	g_data.player:setVitaliyitemValue(time)
	main_scene.ui.console:call("infoBar", "uptBlood")
end

function mainui:onSM_AUTHENTICATECREDIT(result, protoId)
	g_data.player:setAuthen(result.FAuthenticate)
	g_data.player:setCreditScore(result.FCreditPoint)
	main_scene.ui.console:call("infoBar", "uptCredit")
	main_scene.ui.console:call("infoBar", "uptCreditAuthen")
end

local function parseMsgBox(str)
	local text = {}

	local function parseColor(val)
		val = tostring(val)

		if string.len(val) ~= 6 then
			return
		end

		local r, g, b

		val = string.lower(val)

		local val16 = {
			e = 14,
			a = 10,
			c = 12,
			d = 13,
			f = 15,
			b = 11
		}

		r = (val16[string.sub(val, 1, 1)] or tonumber(string.sub(val, 1, 1))) * 16 + (val16[string.sub(val, 2, 2)] or tonumber(string.sub(val, 2, 2)))
		g = (val16[string.sub(val, 3, 3)] or tonumber(string.sub(val, 3, 3))) * 16 + (val16[string.sub(val, 4, 4)] or tonumber(string.sub(val, 4, 4)))
		b = (val16[string.sub(val, 5, 5)] or tonumber(string.sub(val, 5, 5))) * 16 + (val16[string.sub(val, 6, 6)] or tonumber(string.sub(val, 6, 6)))

		return cc.c3b(r, g, b)
	end

	local contentT = {}

	lines = string.split(str, "$$")

	local count = 1

	for k, v in pairs(lines) do
		v = v .. "\n"

		local section = string.split(v, "|")

		for key, value in pairs(section) do
			local ret = string.split(value, "#")

			if ret[1] and ret[2] then
				ret[1] = parseColor(ret[1])
			else
				ret[1] = cc.c3b(255, 255, 255)
				ret[2] = value
			end

			local tmp = ret[1]

			ret[1] = ret[2]
			ret[2] = tmp
			contentT[count] = ret
			count = count + 1
		end
	end

	return contentT
end

function mainui:onSM_OpenDialogs(result, protoId)
	local text = parseMsgBox(result.FMsg)
	local fontSize, disableScroll, center = 20, true, true

	if result and result.FTitle and result.FTitle == "性别转换卡" then
		fontSize, disableScroll, center = 18, false, false
	end

	an.newMsgbox(text, function(isOk)
		local rsb = DefaultClientMessage(CM_DialogsAction)

		rsb.FSenderTick = result.FSenderTick

		if isOk == 1 then
			if g_data.client:checkLastTime("OpenDialogs", 0.8) then
				g_data.client:setLastTime("OpenDialogs", true)

				rsb.FAction = result.FBtn_Ok

				MirTcpClient:getInstance():postRsb(rsb)
			end
		elseif g_data.client:checkLastTime("OpenDialogs", 0.8) then
			g_data.client:setLastTime("OpenDialogs", true)

			rsb.FAction = result.FBtn_Cancel

			MirTcpClient:getInstance():postRsb(rsb)
		end
	end, {
		noclose = true,
		hasCancel = true,
		fontSize = fontSize,
		disableScroll = disableScroll,
		center = center
	})
end

function mainui:onSM_QUERY_MAP_NPC(result, protoId)
	g_data.bigmap:addNpcs(result.FNpcList)

	if self.panels.bigmap then
		self.panels.bigmap:uptNpcCell()
	end
end

function mainui:onSM_ATTACKMODE(result, protoId)
	local modes = {
		"[全体攻击模式]",
		"[和平攻击模式]",
		"[编组攻击模式]",
		"[行会攻击模式]",
		"[敌对攻击模式]",
		"[战队攻击模式]"
	}
	local mode = modes[result.AttackMode + 1] or "[未知攻击模式]"

	g_data.player:setAttackMode(mode)
	self.console:call("btnMode", "upt")

	if mode == "[行会攻击模式]" and main_scene.ground:isNewWorldMap() then
		self:tip("[战团攻击模式]", 4, 65280)
	else
		self:tip(mode, 4, 65280)
	end
end

function mainui:onSM_NPC_Action(result, protoId)
	if main_scene.ui.panels.smelting then
		main_scene.ui.panels.smelting:onSM_NPC_Action(result, protoId)
	end
end

function mainui:onSM_NpcGoodList(result, protoId)
	if self.panels.npc then
		self.panels.npc:showList(result.FNpcId, #result.FGoodList, result.FGoodList, nil, "goods")
		self:showPanel("bag")
		self.panels.bag:resetPanelPosition("right")
	end
end

function mainui:onSM_NpcDetailGoodList(result, protoId)
	if self.panels.npc then
		self.panels.npc:showList(result.FNpcId, result.FCount, result.FGoodList, nil, "goods_detail", result.FMenuIdx)
		self:showPanel("bag")
		self.panels.bag:resetPanelPosition("right")
	end
end

function mainui:onSM_NpcBuyItem(result, protoId)
	if result.FErrorCode == 0 then
		g_data.client:setLastTime("buy")
		common.goldChanged(result.FCurGold)

		if self.panels.npc then
			self.panels.npc:removeItem(result.FItemID)
		end
	else
		g_data.client:setLastTime("buy")

		if result.FErrorCode == 1 then
			an.newMsgbox("此物品被卖出.", nil, {
				center = true
			})
		elseif result.FErrorCode == 2 then
			an.newMsgbox("您无法携带更多物品了.", nil, {
				center = true
			})
		elseif result.FErrorCode == 3 then
			an.newMsgbox("您没有足够的钱来购买此物品.", nil, {
				center = true
			})
		else
			an.newMsgbox("未知错误: " .. result.FErrorCode, nil, {
				center = true
			})
		end
	end
end

function mainui:onSM_NpcSell(result, protoId)
	if self.panels.npc then
		self.panels.npc:showSellFrame(result.FNpcId, "sell")
		self:showPanel("bag")
		self.panels.bag:resetPanelPosition("right")
	end
end

function mainui:onSM_NpcSellPrice(result, protoId)
	if self.panels.npc then
		self.panels.npc:setSellText(result.FPrice >= 0 and result.FPrice .. " 金币" or "???? 金币")
	end
end

function mainui:onSM_NpcSellItem(result, protoId)
	if result.FBoSuccess then
		if self.panels.npc then
			self.panels.npc:delSellItem()
		end

		if g_data.client.lastSellItem then
			if self.panels.bag then
				self.panels.bag:delItem(g_data.client.lastSellItem.FItemIdent)
			end

			g_data.bag:delItem(g_data.client.lastSellItem.FItemIdent)
		end

		g_data.client:setLastTime("sell")
		g_data.client:setLastSellItem()
	else
		if self.panels.npc then
			self.panels.npc:delSellItem()
		end

		g_data.client:setLastTime("sell")
		g_data.client:setLastSellItem()
		an.newMsgbox("您不能出售该物品，可能是以下原因：\n1.    绑定物品和高级物品无法出售\n2.    请前往对应商店出售物品\n3.    可携带金币超出上限(未验证角色可携带200万金币，已验证角色可携带5亿金币)")
	end
end

function mainui:onSM_Act_Detail(result, protoId)
	print(json.encode(result))

	if not result then
		return
	end

	if g_data.player:getIsCrossServer() then
		main_scene.ui.waiting:close("ACTIVITY_TAB1")
		main_scene.ui.waiting:close("ACTIVITY_TAB2")
		main_scene.ui.waiting:close("ACTIVITY_CONTENT")
		main_scene.ui.waiting:close("ACTIVITY_BUYGIFT")
		main_scene.ui.waiting:close("ACTIVITY_NGO")
		main_scene.ui:tip("该功能不能使用")

		return
	end

	result.FContent = json.decode(result.FContent) or {}

	if result.FStrParam == "tab1" then
		main_scene.ui.waiting:close("ACTIVITY_TAB1")

		if not self.panels.activity then
			self:togglePanel("activity")
		elseif self.openActFromPicIdent then
			self:togglePanel("activity")
			self:togglePanel("activity")
		end

		if self.panels.activity then
			self.panels.activity:createTab(result, main_scene.ui.openActFromPicIdent)
		end
	elseif result.FStrParam == "tab2" then
		print("============" .. result.FActId)
		main_scene.ui.waiting:close("ACTIVITY_TAB2")

		if self.panels.activity and result.FActId == 11 then
			self.panels.activity:load(result.FActId, result)

			if g_data.diffPanels.showRMBGiftPageFromNPC == true then
				self.panels.activity:setActItemBtnSelectedByActID(1134)

				local rsb = DefaultClientMessage(CM_QueryDirectBuyGiftBag)

				MirTcpClient:getInstance():postRsb(rsb)
			elseif g_data.diffPanels.showMerchantPageFromNPC == true then
				g_data.diffPanels.showMerchantPageFromNPC = false

				self.panels.activity:setActItemBtnSelectedByActID(1176)

				local rsb = DefaultClientMessage(CM_ServerTime)

				MirTcpClient:getInstance():postRsb(rsb)

				local rsb = DefaultClientMessage(CM_QueryMysteryShopInfo)

				MirTcpClient:getInstance():postRsb(rsb)
			elseif g_data.diffPanels.showOneWorld then
				g_data.diffPanels.showOneWorld = false

				self.panels.activity:setActItemBtnSelectedByActID(1178)

				local rsb = DefaultClientMessage(CM_QueryYZQKInfo)

				MirTcpClient:getInstance():postRsb(rsb)
			elseif g_data.diffPanels.showDragonBoxFromNPC == true then
				self.panels.activity:setActItemBtnSelectedByActID(1177)

				local rsb = DefaultClientMessage(CM_ChestHaveItem)

				MirTcpClient:getInstance():postRsb(rsb)
			elseif g_data.diffPanels.showYBBOX == true then
				g_data.diffPanels.showYBBOX = false

				self.panels.activity:setActItemBtnSelectedByActID(1179)

				local rsb = DefaultClientMessage(CM_YbBoxReqAll)

				MirTcpClient:getInstance():postRsb(rsb)
			elseif g_data.diffPanels.showDaily == true then
				g_data.diffPanels.showDaily = false

				self:changeActivityTab("日常活动", 2, 12)
			elseif g_data.diffPanels.showLimit == true then
				g_data.diffPanels.showLimit = false

				self:changeActivityTab("限时活动", 3, 13)
			end
		end

		if self.panels.activity and (result.FActId == 12 or result.FActId == 13 or result.FActId == 15) then
			self.panels.activity:showConfigAct(result.FActId, result)
		end

		if self.panels.activity and result.FActId == 1132 then
			self.panels.activity:showGrowthRoad(result)
		end
	elseif result.FStrParam == "content" then
		main_scene.ui.waiting:close("ACTIVITY_CONTENT")

		if self.panels.activity then
			self.panels.activity:showActivityContent(result.FActId, result)
		end

		g_data.eventDispatcher:dispatch("ACTIVITY_CONTENT", result)
	elseif result.FStrParam == "buygift" then
		main_scene.ui.waiting:close("ACTIVITY_BUYGIFT")

		if result.FParamA == 1 then
			local rsb = DefaultClientMessage(CM_Act_ButtonClick)

			rsb.FActId = 1103
			rsb.FButtonId = 0
			rsb.FParamA = 0
			rsb.FParamB = 0
			rsb.FStrParam = "content"

			MirTcpClient:getInstance():postRsb(rsb)
			main_scene.ui.waiting:show(10, "ACTIVITY_CONTENT")
		end
	elseif result.FStrParam == "scoreShop" then
		main_scene.ui.waiting:close("ACTIVITY_BUYGIFT")

		if result.FParamA == 1 then
			local rsb = DefaultClientMessage(CM_Act_ButtonClick)

			rsb.FActId = 1184
			rsb.FButtonId = 0
			rsb.FParamA = 0
			rsb.FParamB = 0
			rsb.FStrParam = "content"

			MirTcpClient:getInstance():postRsb(rsb)
			main_scene.ui.waiting:show(10, "ACTIVITY_CONTENT")
		end
	elseif result.FStrParam == "growthBoost" then
		main_scene.ui.waiting:close("ACTIVITY_GROWTH")

		if self.panels.growthBoost then
			self.panels.growthBoost:onSM_Act_Detail(result.FActId, result)
		end
	elseif result.FStrParam == "friendBox" then
		if self.panels.friendBox then
			self.panels.friendBox:onSM_Act_Detail(result.FActId, result)
		end
	elseif result.FStrParam == "newStarWeapon" then
		if self.panels.activity then
			self.panels.activity:closeAll()
		end
	else
		main_scene.ui.waiting:close("ACTIVITY_NGO")

		if self.panels.activity then
			self.panels.activity:closeAll(result.FActId, result)
		end
	end
end

function mainui:onSM_BossBookSendAll(result, protoId)
	if not result then
		return
	end

	main_scene.ui.waiting:close("ACTIVITY_TAB2")

	if self.panels.picIdentify then
		self.panels.picIdentify:loadBossWiki(result)
	end
end

function mainui:onSM_BossBookFly(result, protoId)
	main_scene.ui.waiting:close("ACTIVITY_NGO")

	if not result then
		return
	end

	local s = {
		"",
		"您的金币不足",
		"您的飞鞋数量不足",
		"您已到达",
		"您的等级未能达到传送要求",
		"BOSS传送所需开服天数还没到",
		"您已死亡",
		"服务器阶段未达到"
	}

	if result.Fretcode >= 1 and result.Fretcode <= #s then
		self:tip(s[result.Fretcode] or "")
	end
end

function mainui:onSM_TransferFee(result, protoId)
	if not result or not g_data.player then
		return
	end

	g_data.player.transferFee = result
end

function mainui:onSM_F2F_DEAL(result, protoId)
	if not result then
		return
	end

	if result.dealStatus == 0 then
		self:tip("交易开始")

		if main_scene.ui:isPanelVisible("tradeshop") then
			main_scene.ui:tip("不能打开背包")

			return
		end

		self:togglePanel("f2fDeal", result)
	elseif result.dealStatus == 1 then
		main_scene.ui.waiting:close("f2fDeal")

		g_data.bag.f2fDeal = {}

		local rsb = DefaultClientMessage(CM_QUERYBAGITEMS)

		MirTcpClient:getInstance():postRsb(rsb)
		self:hidePanel("f2fDeal")
	elseif result.dealStatus == 2 then
		if self.panels.f2fDeal then
			self.panels.f2fDeal:setLockStatus()
		end
	elseif result.dealStatus == 3 then
		if self.panels.f2fDeal then
			self.panels.f2fDeal.otherHasClickedDeal = true
		end
	elseif result.dealStatus == 4 then
		main_scene.ui.waiting:show(10, "f2fDeal", 1)
	elseif result.dealStatus == 5 then
		an.newMsgbox("你必须和对方面对面且双方必须都处于非交易状态下才能进行交易！", nil, {
			center = true
		})
	elseif result.dealStatus == 6 then
		self:tip("服务器错误")
	elseif result.dealStatus == 7 then
		main_scene.ui.waiting:close("f2fDeal")
		self:hidePanel("f2fDeal")
	elseif result.dealStatus == 8 and result.Fusernameid then
		local pList = string.split(result.Fusernameid, "#")
		local playerData = {}

		for i, v in ipairs(pList) do
			playerData[i] = {
				name = string.split(v, "/")[1],
				id = tonumber(string.split(v, "/")[2])
			}
		end

		self:showDealListView(playerData)
	end
end

function mainui:showDealListView(playerList)
	if not playerList or playerList and #playerList <= 0 then
		return
	end

	local function sendDealId(Fname, Fid)
		if not main_scene.ground:isNewWorldMap() and g_data.player:getIsCrossServer() then
			main_scene.ui:tip("该功能不能使用")

			return
		end

		if g_data.client:checkLastTime("dealwithplayer", 3) then
			g_data.client:setLastTime("dealwithplayer", true)

			local rsb = DefaultClientMessage(CM_F2F_DEAL)

			rsb.dealStatus = 4
			rsb.Fname = Fname
			rsb.Fid = Fid

			MirTcpClient:getInstance():postRsb(rsb)
		end
	end

	local mask = display.newNode()

	mask:size(display.width, display.height):add2(display.getRunningScene(), an.z.msgbox)
	mask:setTouchEnabled(true)
	mask:setTouchSwallowEnabled(true)
	mask:addNodeEventListener(cc.NODE_TOUCH_EVENT, function()
		mask:removeSelf()
	end)

	local bg = display.newScale9Sprite(res.getframe2("pic/common/pop.png"), 0, 0, cc.size(220, 280)):anchor(0.5, 0.5):center():add2(mask)

	bg:setTouchEnabled(true)
	bg:setTouchSwallowEnabled(true)
	bg:addNodeEventListener(cc.NODE_TOUCH_EVENT, function()
		return
	end)
	an.newBtn(res.gettex2("pic/common/close10.png"), function()
		sound.playSound("103")
		mask:removeSelf()
	end, {
		pressImage = res.gettex2("pic/common/close11.png")
	}):anchor(1, 1):pos(bg:getw() - 5, bg:geth() - 4):add2(bg)
	an.newLabel("选择交易对象", 18, 1, {
		color = def.colors.Cd2b19c
	}):anchor(0.5, 0.5):add2(bg):pos(110, 262)

	local blackBg = display.newScale9Sprite(res.getframe2("pic/common/itembg2.png"), 0, 0, cc.size(bg:getw() - 22, bg:geth() - 55)):anchor(0.5, 0.5):pos(bg:getw() / 2, bg:geth() / 2 - 16):add2(bg)

	blackBg:setOpacity(0)

	local scroll = an.newScroll(15, 15, blackBg:getw() - 10, blackBg:geth() - 8):add2(bg)

	scroll:setScrollOffset(0, 0)

	for i = 1, 10 do
		local player = playerList[i]

		if player then
			local btn = an.newBtn(res.gettex2("pic/common/btn20.png"), function()
				sound.playSound("103")
				mask:removeSelf()
				sendDealId(player.name, player.id)
			end, {
				support = "scroll",
				label = {
					player.name,
					20,
					0,
					{
						color = def.colors.Cf0c896
					}
				},
				pressImage = res.gettex2("pic/common/btn21.png")
			}):add2(scroll):anchor(0.5, 1):pos(scroll:getw() / 2, scroll:geth() - 5 - (i - 1) * 55)

			if btn.bg then
				btn.bg:scaleX(2)
				btn.bg:scaleY(1.2)
			end
		end
	end
end

function mainui:onSM_F2F_DEAL_CONTENT(result, protoId)
	if not result then
		return
	end

	if self.panels.f2fDeal then
		self.panels.f2fDeal:updateOtherItemsandMoney(result)
	end
end

function mainui:onSM_UserRegDate(result, protoId)
	if not result then
		return
	end

	g_data.player.regtime = result.FRegDate
end

function mainui:onSM_QueryEnergyCrystalInfo(result)
	if not result then
		return
	end

	if self.panels.crystal then
		self.panels.crystal:GSon_getNum(result)
	end
end

function mainui:onSM_QueryGetEnergyCrystal(result)
	if not result then
		return
	end

	if self.panels.crystal then
		self.panels.crystal:GSM_getNum(result)
	end
end

function mainui:onSM_CHG_USERNAME(result, protoId)
	if not result then
		return
	end

	if result.FBoSuccess then
		main_scene:bigExit()
	else
		self.panels.bag:useItemByName("易名符")
	end
end

function mainui:onSM_GetServerAndNetDelayTick(result)
	if not result then
		return
	end

	self:delayLabelResult(result)
end

function mainui:onSM_KillMan_Cnt(result)
	if not result then
		return
	end

	if g_data.setting.base.defeatTip then
		local killCnt = result.KillCnt

		if killCnt > 5 then
			killCnt = 5
		end

		self.msgs.pushBack(killCnt)

		if not self.defeatAniTime then
			self:showDefeatAni(self.msgs.popFront())
		end
	end
end

function mainui:showDefeatAni(num)
	if self.defeatAniTime then
		self:hideDefeatAni()
	end

	local deviceFix = 0

	if game.deviceFix then
		deviceFix = game.deviceFix
	end

	local aniPos_x, aniPos_y = display.width - 220 - deviceFix, display.height - 120

	self.defeatAni = display.newNode():pos(aniPos_x, aniPos_y):add2(self, 1002)

	ccs.ArmatureDataManager:getInstance():addArmatureFileInfo("animation/zhan/zhan.csb")

	local zhanAni = ccs.Armature:create("zhan")

	zhanAni:addTo(self.defeatAni, 1002)
	zhanAni:getAnimation():play("zhan")

	local seq = cc.Sequence:create(cc.DelayTime:create(0.4264705882352941), cc.CallFunc:create(function()
		local __ = res.get2("pic/panels/crystal/zhan/bg.png"):anchor(0.5, 0.5):pos(30, 0):addto(self.defeatAni, 1)
		local __ = res.get2("pic/panels/crystal/zhan/" .. num .. ".png"):anchor(0.5, 0.5):pos(50, 0):addto(self.defeatAni, 1)
	end))

	self.defeatAni:runAction(seq)

	self.defeatAniTime = 0
end

function mainui:hideDefeatAni()
	if self.defeatAni then
		self.defeatAniTime = nil

		self.defeatAni:setVisible(false)
		self.defeatAni:removeSelf()

		self.defeatAni = nil
	end
end

function mainui:onSM_Request_Marry(result)
	if not result then
		return
	end

	local rsb = DefaultClientMessage(CM_Dispose_Marry_Request)
	local requestName = result.RequesterName

	an.newMsgbox(requestName .. "向你求婚，请选择接受或拒绝", function(idx)
		if idx == 1 then
			rsb.BoAgree = true
		else
			rsb.BoAgree = false
		end

		MirTcpClient:getInstance():postRsb(rsb)
	end, {
		noclose = true,
		center = true,
		hasCancel = true,
		btnTexts = {
			"接受",
			"拒绝"
		}
	})
end

function mainui:onSM_BlockChainNewOrder(result, protoId)
	print("onSM_BlockChainNewOrder")
	print("FTranID = ", result.FTranID)

	if VERSION_BLOCK_CHAIN == false or VERSION_BLOCK_CHAIN == nil then
		return
	end

	if result.FTranID then
		print("FTranID = ", result.FTranID)
		GRedCoinSdk:getInstance():startPay(result.FTranID, result.FTranID, 11, "复古传奇", result.FTranID, result.FTranID, result.FTranID, g_data.select:getCurUserId(), g_data.select:getCurLevel(), function(info)
			print("shop onSM_NewOrder GRedCoinSdk:getInstance():startPay success:" .. (info or ""))
		end, function(error)
			if error == 2 or error == 4 then
				local box = an.newMsgbox("登录失败，是否重新登陆", function(isOk)
					if isOk == 1 then
						local rsb = DefaultClientMessage(CM_BlockChainChannelLogin)

						MirTcpClient:getInstance():postRsb(rsb)
					end
				end, {
					disableScroll = true,
					fontSize = 20,
					title = "提示",
					center = true,
					hasCancel = true
				})
			end

			if error == -10331003 then
				local rsb = DefaultClientMessage(CM_BlockChainChannelLogin)

				MirTcpClient:getInstance():postRsb(rsb)
			end

			print("shop onSM_NewOrder GRedCoinSdk:getInstance():startPay failed:" .. (error or ""))
		end)
	end
end

function mainui:onSM_BlockChainChannelLogin(result, protoId)
	print("onSM_BlockChainChannelLogin")
	print("result.FTicket", result.FTicket)
	print("VERSION_BLOCK_CHAIN = ", VERSION_BLOCK_CHAIN)

	if VERSION_BLOCK_CHAIN == false or VERSION_BLOCK_CHAIN == nil then
		return
	end

	if result.FTicket then
		GRedCoinSdk:getInstance():setServerId(tostring(g_data.login.zoneId))
		GRedCoinSdk:getInstance():setAccessToken(result.FTicket)
		print("FTicket = ", result.FTicket)

		if VERSION_BLOCK_CHAIN then
			local function login()
				GRedCoinSdk:getInstance():login(function(userInfo)
					print("GRedCoinSdk:getInstance():login success")
				end, function(error)
					print("GRedCoinSdk:getInstance():login failed")

					if device.platform == "android" and error == -10331003 then
						local rsb = DefaultClientMessage(CM_BlockChainChannelLogin)

						MirTcpClient:getInstance():postRsb(rsb)
					end
				end)
			end

			GRedCoinSdk:getInstance():init(function(info)
				print("GRedCoinSdk:getInstance():init success")
				login()
			end, function(error)
				print("GRedCoinSdk:getInstance():init failed")
			end)
		end
	end
end

function mainui:onSM_IDSLevelChange(result, protoId)
	print("result.FNewLevel = ", result.FNewLevel)
	print("result.FChgTime = ", result.FChgTime)

	local curLoginSdk = platformSdk:getLoginSdk()

	if curLoginSdk == 2 then
		LdSdk:getInstance():setRoleLevel(tostring(result.FNewLevel))
		LdSdk:getInstance():setRoleLevelUpTime(tostring(result.FChgTime))

		if device.platform == "ios" then
			LdSdk:getInstance():doSubmitGameData(1)
			print("LdSdk:getInstance():doSubmitGameData")
		end
	end
end

function mainui:onSM_DIAMONDLIST(result, protoId)
	if result then
		g_data.player:setGemstonesInfo(result)
	end
end

function mainui:onSM_DiamondSpot(result, protoId)
	if result then
		g_data.player:setGemstonesUpgradeInfo(result)
	end
end

function mainui:onSM_Act_Tip(result, protoId)
	if result then
		if result.FShowTip == 1 then
			g_data.badgeSystem:set(badge.B_BOTTOM_ACTIVITY, true)
		end

		g_data.eventDispatcher:dispatch("M_ACT_TIP", result.FActId, result.FShowTip)
	end
end

function mainui:checkFirstGodDun()
	if not g_data.firstOpen:get("goddun") and g_data.client.serverState >= 4 then
		g_data.badgeSystem:set(badge.B_GODDUN_UPGRADE, true)
	end
end

function mainui:checkFirstGodBody()
	if not g_data.firstOpen:get("godBody") and g_data.client.serverState >= 1 then
		g_data.badgeSystem:set(badge.B_GODBODY_UPGRADE, true)
	end
end

function mainui:checkFirstSolider()
	if not g_data.firstOpen:get("solider") and g_data.funcOpen.isFuncOpen(gameFunc.FUNC1004) then
		g_data.badgeSystem:set(badge.B_SOLIDER_UPGRADE, true)
	end
end

function mainui:checkFirstCastingSoul()
	if not g_data.firstOpen:get("castingSoul") and main_scene.ground:isNewWorldMap() then
		g_data.badgeSystem:set(badge.B_ROLE_SOUL, true)
	end
end

function mainui:checkFirstGodCustome()
	if not g_data.firstOpen:get("godCustome") and main_scene.ground:isNewWorldMap() then
		g_data.badgeSystem:set(badge.B_ROLE_GODCUSTOME, true)
	end
end

function mainui:checkFirstGodRing()
	if common.isModuleOpen("godring") and not g_data.firstOpen:get("godring") then
		g_data.badgeSystem:set(badge.B_ROLE_RING, true)
	end
end

function mainui:checkGodRingTJ()
	if def.godring.checkOpen(5) and not g_data.firstOpen:get("godring_jt") then
		g_data.badgeSystem:set(badge.B_RING_JT, true)
	end
end

function mainui:checkGodRingJH()
	if FuncOpen.isFuncOpen(1018) and not g_data.firstOpen:get("godring_jh") then
		g_data.badgeSystem:set(badge.B_RING_JH, true)
	end
end

function mainui:checkFirstHorseSoul()
	if not g_data.firstOpen:get("horseSoul") and g_data.client.serverState >= 2 and g_data.client.openDay >= 110 then
		g_data.badgeSystem:set(badge.B_HORSE_SOUL, true)
	end
end

function mainui:checkFirstGoldEquip()
	if not g_data.firstOpen:get("goldEquip") and def.goldEquip:getGoldEquipOpen() then
		g_data.badgeSystem:set(badge.B_ROLE_GOLDEQUIP, true)
	end
end

function mainui:checkFirstMingWen()
	if not g_data.firstOpen:get("mingWen") and FuncOpen.isFuncOpen(gameFunc.FUNC1039) then
		g_data.badgeSystem:set(badge.B_ROLE_MINGWEN, true)
	end
end

function mainui:checkFirstStarChart()
	if not g_data.firstOpen:get("starChart") and FuncOpen.isFuncOpen(gameFunc.FUNC1040) then
		g_data.badgeSystem:set(badge.B_ROLE_STAR_CHART, true)
	end
end

function mainui:checkFirstBirthSign()
	if not g_data.firstOpen:get("birthSign") and FuncOpen.isFuncOpen(gameFunc.FUNC1046) then
		g_data.badgeSystem:set(badge.B_ROLE_ZODIAC, true)
	end
end

function mainui:checkFirstAmulet()
	if not g_data.firstOpen:get("amulet") and def.amulet:isOpen() then
		g_data.badgeSystem:set(badge.B_ROLE_AMULET, true)
	end
end

function mainui:checkFirstFootPoint()
	if not g_data.firstOpen:get("footPointView") and FuncOpen.isFuncOpen(gameFunc.FUNC1059) then
		g_data.badgeSystem:set(badge.B_ROLE_FOOTPOINT, true)
	end
end

function mainui:checkFirstMoh()
	if not g_data.firstOpen:get("btnMoH") and g_data.moh:isActive() and g_data.moh:isShowBadge() then
		g_data.badgeSystem:set(badge.B_INTERFACE_MOH, true)
	end
end

function mainui:checkFirstSoliderSoul()
	if not g_data.firstOpen:get("soliderSoul") and common.isModuleOpen("soliderSoul") then
		g_data.badgeSystem:set(badge.B_ROLE_SOLIDERSOUL, true)
	end
end

function mainui:checkPrivateBoss()
	g_data.badgeSystem:set(badge.B_PRIVATEBOSS, not g_data.firstOpen:get("privateBoss") and FuncOpen.isFuncOpen(1010))
end

function mainui:checkFirstWoc()
	if not g_data.firstOpen:get("btnWoC") and g_data.client.openDay <= 7 then
		g_data.badgeSystem:set(badge.B_INTERFACE_WOC, true)
	end
end

function mainui:checkFirstMeridian()
	if not g_data.firstOpen:get("meridian") and FuncOpen.isFuncOpen(gameFunc.FUNC1045) then
		g_data.badgeSystem:set(badge.B_ROLE_MERIDIAN, true)
	end
end

function mainui:checkFirstWuxing()
	if not g_data.firstOpen:get("wuxing") and FuncOpen.isFuncOpen(gameFunc.FUNC1048) then
		g_data.badgeSystem:set(badge.B_ROLE_WUXING, true)
	end
end

function mainui:checkFirstPenDant()
	if not g_data.firstOpen:get("pendantPanel") and FuncOpen.isFuncOpen(gameFunc.FUNC1049) then
		g_data.badgeSystem:set(badge.B_ROLE_PENDANT, true)
	end
end

function mainui:checkFirstTemple()
	if not g_data.firstOpen:get("temple") and FuncOpen.isFuncOpen(gameFunc.FUNC1051) then
		g_data.badgeSystem:set(badge.B_ROLE_TEMPLE, true)
	end
end

function mainui:checkFirstBagua()
	if not g_data.firstOpen:get("baguaUpgrade") and FuncOpen.isFuncOpen(gameFunc.FUNC1053) then
		g_data.badgeSystem:set(badge.B_ROLE_BAGUA, true)
	end
end

function mainui:checkFirstWuxue()
	if not g_data.firstOpen:get("wuxue") and g_data.wuxue:isOpen() then
		g_data.badgeSystem:set(badge.B_WUXUE_BASE, true)
		g_data.badgeSystem:set(badge.B_WUXUE_ROLE, true)
	end
end

function mainui:checkFirstWingEquip()
	local wingEquipTypes = {
		{
			value = "毛羽",
			key = "maoyu",
			badgeid = badge.B_WING_MAOYU
		},
		{
			value = "飞羽",
			key = "feiyu",
			badgeid = badge.B_WING_FEIYU
		},
		{
			value = "翎羽",
			key = "lingyu",
			badgeid = badge.B_WING_LINGYU
		}
	}

	for k, v in ipairs(wingEquipTypes) do
		local bUnLock = def.wingEquip.getWingEquipState(g_data.player.wingEquipInfoList, v.value)
		local bOpen = def.wingEquip.getIsOpen(v.value)

		if not g_data.firstOpen:get(v.key) and bOpen and not bUnLock then
			g_data.badgeSystem:set(v.badgeid, true)
		else
			g_data.badgeSystem:set(v.badgeid, false)
		end
	end
end

function mainui:checkFirstCardSys()
	if not g_data.firstOpen:get("cardSys") and FuncOpen.isFuncOpen(1008) then
		g_data.badgeSystem:set(badge.B_CARD_UPGRADE, true)
	end
end

function mainui:checkCardSysRedPoint(params)
	local needCheck = false
	local eventType = params.eventType

	if params.eventType == EType.Bag.refreshBag then
		needCheck = true
	else
		local name = params.data and params.data:getVar("name") or nil

		if name and def.cardSys.getCardMatIDByName(name) then
			needCheck = true
		end
	end

	if not needCheck then
		return
	end

	local cardTypes = def.cardSys.getOpenCardTypes()
	local bUpgrade = false
	local bCanOpenBox = false

	for i, v in ipairs(cardTypes) do
		local canUnlockCards, hasUnlockCards, canNotUnlockCards = def.cardSys.checkUnLockCards(v.CardType)

		if #canUnlockCards > 0 then
			bUpgrade = true

			g_data.badgeSystem:set(badge.B_CARD_UPGRADE, true)
			g_data.eventDispatcher:dispatch("M_CARDQUIP_DATA_CHG")

			return
		end

		if def.cardSys.checkCanOpenBox(v.CardType) then
			bCanOpenBox = true

			g_data.badgeSystem:set(badge.B_CARD_UPGRADE, true)
			g_data.eventDispatcher:dispatch("M_CARDQUIP_DATA_CHG")

			return
		end
	end

	if g_data.badgeSystem:isShow(badge.B_CARD_UPGRADE) and g_data.firstOpen:get("cardSys") and not bUpgrade then
		g_data.badgeSystem:set(badge.B_CARD_UPGRADE, false)
	end
end

function mainui:checkLimCardRedPoint(params)
	local data = params.data or nil
	local eventType = params.eventType
	local cardTypes = def.cardSys.getOpenCardTypes()
	local startStamp = socket.gettime()
	local famTypes = g_data.limCard:getTypeName()
	local needShowPointTip = false

	for _, typeName in ipairs(famTypes) do
		local tItems = g_data.limCard:getItemByTypeName(typeName)

		for _, cardItem in ipairs(tItems) do
			local lastTime = g_data.limCard:getInvalidTimeByCardID(cardItem.FID)

			if not lastTime then
				local tMaterials = g_data.limCard:getMaterialsByCardID(cardItem.FID)

				for _, mat in ipairs(tMaterials) do
					local count = g_data.bag:getItemCount(mat.name)

					if count > 0 then
						needShowPointTip = true

						break
					end
				end
			end

			if needShowPointTip then
				break
			end
		end

		if needShowPointTip then
			break
		end
	end

	local curStamp = socket.gettime()

	startStamp = curStamp

	if needShowPointTip then
		g_data.badgeSystem:set(badge.B_LIMCARD_ACTIVE, true)

		curStamp = socket.gettime()
	elseif g_data.badgeSystem:isShow(badge.B_LIMCARD_ACTIVE) then
		g_data.badgeSystem:set(badge.B_LIMCARD_ACTIVE, false)

		curStamp = socket.gettime()
	end
end

function mainui:checkGodGridRedPoint(params)
	local bUpgrade = g_data.godItem:isRedPointTip()

	if g_data.badgeSystem:isShow(badge.B_ROLE_GOD_ITEM) and g_data.firstOpen:get("godItem") and not bUpgrade then
		g_data.badgeSystem:set(badge.B_ROLE_GOD_ITEM, false)
	elseif bUpgrade then
		g_data.badgeSystem:set(badge.B_ROLE_GOD_ITEM, true)
	end
end

function mainui:checkFirstMumerology()
	if FuncOpen.isFuncOpen(1026) and not g_data.firstOpen:get("mumerology") then
		g_data.badgeSystem:set(badge.B_ROLE_MUMEROLOGY, true)
	end
end

function mainui:checkFirstPal()
	if FuncOpen.isFuncOpen(1030) and not g_data.firstOpen:get("pal") then
		g_data.badgeSystem:set(badge.B_ROLE_PAL, true)
	end
end

function mainui:checkFirstMagicWeapon()
	if FuncOpen.isFuncOpen(1033) and not g_data.firstOpen:get("magicWeapon") then
		g_data.firstOpen:set("magicWeapon", true)
		g_data.badgeSystem:set(badge.B_ROLE_MAGICWEAPON, true)
	end
end

function mainui:checkFirstGodItem()
	if g_data.godItem:isOpen() and not g_data.firstOpen:get("godItem") then
		g_data.badgeSystem:set(badge.B_ROLE_GOD_ITEM, true)
	end
end

function mainui:checkHighestPrivilege()
	if FuncOpen.isFuncOpen(gameFunc.FUNC1037) then
		local day = cache.getCVarN("highestPrivilege")

		day = day or 0

		local day1 = tonumber(os.date("%d", os.time()))

		if day and day ~= day1 then
			g_data.badgeSystem:set(badge.B_HIGHEST_PRIVILEGE, true)
		end

		cache.setCVar("highestPrivilege", day1)
	end
end

function mainui:onSM_QueryDirectBuyGiftBag(result, protoId)
	print("直购礼包消息体:")
	print_r(result)

	g_data.RMBGiftList = result and result.FGiftList or {}

	local tShowBuyBtn = {}

	for k, v in ipairs(result.FGiftList) do
		tShowBuyBtn[k] = false

		print(k .. "  我的渠道:  " .. self.channelID)
		print(k .. "  渠道配置:  " .. result.FGiftList[k].FChannelLimit)
		print(k .. "   判断渠道前:  ", tShowBuyBtn[k])

		local tableChannelIDShow = string.split(result.FGiftList[k].FChannelLimit, ";")

		for i, v in ipairs(tableChannelIDShow) do
			if v == self.channelID then
				tShowBuyBtn[k] = true

				break
			end
		end

		print(k .. "   判断渠道后:  ", tShowBuyBtn[k])

		local tPacName = string.split(result.FGiftList[k].FCliPackNameLimit, ";")
		local packageID = platformSdk:bundleid()

		print(k .. "  我的包名:  " .. packageID)
		print(k .. "  包名配置:  " .. result.FGiftList[k].FCliPackNameLimit)

		for i, v in ipairs(tPacName) do
			if v == packageID then
				tShowBuyBtn[k] = false

				break
			end
		end

		print(k .. "   判断包名渠道后:  ", tShowBuyBtn[k])
	end

	local function callback(allowOrder)
		main_scene.ui.waiting:close("GET_RECHARGE_FLAG_GIFT")

		if self.panels.activity then
			self.panels.activity:showRMBGiftPage(tShowBuyBtn, not allowOrder)
		end
	end

	if device.platform == "ios" then
		main_scene.ui.waiting:show(10, "GET_RECHARGE_FLAG_GIFT")
		callback(false)
		common.getRechargeOpen(callback)
	else
		callback(true)
	end
end

function mainui:onSM_QueryYZQKInfo(result, protoId)
	if self.panels.activity then
		self.panels.activity:showOneWorld(result)
	end
end

function mainui:onSM_QueryMFBKInfo(result, protoId)
	if self.panels.activity then
		self.panels.activity:lucky10Page(result)
	end
end

function mainui:onSM_QuerySelfDBGBTimes(result, protoId)
	print("mainui:onSM_QuerySelfDBGBTimes")
	print_r(result)

	local RMBGiftTimesList = result and result.FGiftTimesList or {}

	g_data.RMBGiftList = g_data.RMBGiftList or {}

	for k, v in ipairs(RMBGiftTimesList) do
		for key, value in ipairs(g_data.RMBGiftList) do
			if v.FProductId == value.FProductId then
				value.FBuyTimesLast = v.FBuyTimesLast

				break
			end
		end
	end

	if self.panels.activity then
		self.panels.activity:showRMBGiftPage()
	end
end

function mainui:onSM_LOL_RANK(result, protoId)
	main_scene.ui:togglePanel("battleRankList", result)
end

function mainui:onSM_LOL_BUFF(result, protoId)
	main_scene.ground.map:addMsg({
		roleid = result.FUserId,
		bufftype = result.FBuffType
	})
end

function mainui:onSM_SeverLevel(result, protoId)
	if not result then
		return
	end

	if g_data.login.serverLevel ~= result.FServerStep then
		self:checkServerActOpenInfo()
		self:handleDayOrServerLVChange(false, true)
	end

	g_data.login.serverLevel = result.FServerStep

	g_data.player:checkGemstoneCanActive()
end

function mainui:onSM_NumericDataChg(result, protoId)
	if not result then
		return
	end

	local ability = g_data.player.ability

	if result.FDataType == TNumericType.ntDonate then
		ability.FDonateValue = result.FCurData
	elseif result.FDataType == TNumericType.ntMerit then
		ability.FMeritValue = result.FCurData
	elseif result.FDataType == TNumericType.ntSpring then
		local old = ability.FSpringValue or 0
		local diff = result.FCurData - old

		ability.FSpringValue = result.FCurData

		if diff > 0 then
			self:tip("泉水 +" .. diff, 2)

			if main_scene.ui.panels.equip and main_scene.ui.panels.equip.page == "attributes" then
				main_scene.ui.panels.equip:showContent("attributes")
			end

			main_scene.ground.map.player:playWaterEffect()
		end
	elseif result.FDataType == TNumericType.ntCanJuan then
		ability.FCanJuanValue = result.FCurData
	elseif result.FDataType == TNumericType.ntContribute then
		ability.FContributeValue = result.FCurData
	elseif result.FDataType == TNumericType.ntYueLi then
		ability.FYueLiValue = result.FCurData
	elseif result.FDataType == TNumericType.ntJunGong then
		ability.FJunGongValue = result.FCurData
	elseif result.FDataType == TNumericType.ntWingSpirit then
		ability.FWingSpiritValue = result.FCurData
	elseif result.FDataType == TNumericType.ntForgePoint then
		ability.FForgePoint = result.FCurData

		g_data.eventDispatcher:dispatch("FORGE_DATA")
	elseif result.FDataType == TNumericType.ntexskillpoint then
		ability.FExSkillPoint = result.FCurData
	elseif result.FDataType == TNumericType.ntXinfapoint then
		ability.FXinfapoint = result.FCurData
	end
end

function mainui:autoBindDrug()
	g_data.needAutoBindDrug = true
end

function mainui:autoRecoveryCard(params)
	if params.eventType == EType.Bag.delItem or params.eventType == EType.Bag.duraChg then
		return
	end

	local itemList = params.data and {
		params.data
	} or g_data.bag.items

	if g_data.setting.base.recoveryCard then
		for k, v in pairs(itemList) do
			if v._item.stdMode == 180 and def.cardSys.checkIfUnLock(v._item.name) then
				local rsb = DefaultClientMessage(CM_Recycle_item)

				rsb.FItemIdent = v.FItemIdent

				MirTcpClient:getInstance():postRsb(rsb)
			end
		end
	end
end

local bagEvent2BindDrug = {
	[EType.Bag.addItem] = true,
	[EType.Bag.refreshBag] = true
}

function mainui:onM_BAGITEM_CHG(params)
	g_data.player:checkWingPointTip(params)
	self:checkCardSysRedPoint(params)
	self:autoRecoveryCard(params)
	self:checkLimCardRedPoint(params)
	self:checkGodGridRedPoint(params)
	g_data.bagua:checkRedPoint(params)
	g_data.wuxue:checkWuxueList(params)

	if bagEvent2BindDrug[params.eventType] then
		self:autoBindDrug()
	end
end

function mainui:onSM_SendIdentifyCode(result)
	main_scene.ui:showPanel("antiMotor")

	if main_scene.ui.panels.antiMotor then
		main_scene.ui.panels.antiMotor:updateImage(result)
	end
end

function mainui:onSM_SendResIdentifyCode(result)
	main_scene.ui:hidePanel("antiMotor")
end

function mainui:onSM_ShowVIPIcon(result)
	print("VIPICON消息体:")
	print_r(result)

	if result.FChannelID == "" then
		print("未配置VIPICON渠道ID")

		return
	end

	local vipIconChannelId = self.channelID

	if vipIconChannelId then
		print("未处理的vipIconChannelId = " .. vipIconChannelId)
	end

	if vipIconChannelId == "" then
		if device.platform == "ios" then
			vipIconChannelId = "50999"
		elseif device.platform == "android" then
			vipIconChannelId = "50998"
		end
	end

	print("本地平台：" .. device.platform)

	if vipIconChannelId then
		print("经处理后的最终vipIconChannelId =" .. vipIconChannelId)
	end

	local satisfyChannelCondition = false
	local tableShowIconChannels = string.split(result.FChannelID, ";")

	for i, v in ipairs(tableShowIconChannels) do
		if v == vipIconChannelId then
			satisfyChannelCondition = true

			break
		end
	end

	print("VIPIcon充值条件判定结果:" .. (result.FShowIconFlag == 1 and "满足" or "不满足"))
	print("VIPIcon渠道条件判定结果:" .. (satisfyChannelCondition and "满足" or "不满足"))

	if result.FShowIconFlag == 1 and satisfyChannelCondition then
		self.timeLimitWidgets.focusVIPWechat.enable = true

		if self.console then
			local data = self.timeLimitWidgets.focusVIPWechat.data

			data = data or g_data.widgetDef.getData("focusVIPWechat")

			self.console:addWidget(data)
		end
	else
		self.timeLimitWidgets.focusVIPWechat.enable = false
		self.timeLimitWidgets.focusVIPWechat.data = nil

		if self.console then
			self.console:removeWidget("focusVIPWechat")
		end
	end
end

function mainui:onSM_AchievementInfo(result, protoId)
	g_data.achieve.canOpen = true

	g_data.achieve:initCfg()

	if result and result.FInfoList then
		g_data.achieve:setAchInfo(result.FInfoList)
	end
end

function mainui:onSM_SingleAchievementInfo(result, protoId)
	if result and result.FAchievement then
		g_data.achieve:setSingleAchInfo(result.FAchievement)
	end
end

function mainui:onSM_AchievementPoint(result, protoId)
	if result then
		g_data.achieve:setAchPoint(result)
	end
end

function mainui:onSM_AchievementAwardInfo(result, protoId)
	if result and result.FInfoList then
		g_data.achieve:setAchAwardInfo(result.FInfoList)
	end
end

function mainui:onSM_SendDiamTaskTag(result, protoId)
	if result then
		g_data.player:setDiamondTaskCompleteFlag(result.FHaveComplete)
	end
end

function mainui:changeHorseState()
	if g_data.player.horseInfo.state == 0 then
		self:upHorse()
	elseif g_data.player.horseInfo.state == 1 then
		self:downHorse()
	end
end

function mainui:changePetState()
	local map = main_scene.ground.map

	if g_data.client:checkLastTime("btnPet2", 3) then
		g_data.client:setLastTime("btnPet2", true)

		local rsb = DefaultClientMessage(CM_CallOrBackPet)

		MirTcpClient:getInstance():postRsb(rsb)
	end
end

function mainui:changeFlagState()
	if g_data.client:checkLastTime("OperateWarFlag", 5) then
		g_data.client:setLastTime("OperateWarFlag", true)

		local rsb = DefaultClientMessage(CM_OperateWarFlag)

		MirTcpClient:getInstance():postRsb(rsb)
	end
end

function mainui:changePalState()
	local map = main_scene.ground.map

	if g_data.client:checkLastTime("btnPal", 3) then
		g_data.client:setLastTime("btnPal", true)

		local rsb = DefaultClientMessage(CM_CallorBackPal)

		MirTcpClient:getInstance():postRsb(rsb)
	end
end

function mainui:upHorse()
	if g_data.client:checkLastTime("btnHorse", 0.5) then
		g_data.client:setLastTime("btnHorse", true)
		main_scene.ui.loading:show(1, {
			keep_visible = true,
			lbl_format = "上马中...",
			timeout_cb = function()
				self:requestChangeRideState()
			end
		})
	end
end

function mainui:downHorse()
	self:requestChangeRideState()
end

function mainui:requestChangeRideState()
	local rsb = DefaultClientMessage(CM_ChangeRideState)

	MirTcpClient:getInstance():postRsb(rsb)
end

function mainui:onSM_MonCrystalLv(result)
	g_data.player.medalEnhantingLevel = result.FMonCrystalLv
end

function mainui:onSM_QuaryMonSoulInfo(result)
	g_data.player.monSoulLevel = result.FMonSoulLevel

	if main_scene.ui.panels.horseUpgrade then
		main_scene.ui.panels.horseUpgrade:onSM_QuaryMonSoulInfo(result)
	end
end

function mainui:onSM_QueryGodRingList(result)
	if result then
		g_data.player.godRingList = def.godring.setGodRingData(g_data.player.godRingList, result.FGodRingList, result.FGodRingExtList or {})

		g_data.eventDispatcher:dispatch("M_GODRING_DATA_CHG")
	end
end

function mainui:onSM_ArenaReqList(result, protoId)
	if g_data.map.state == 10 then
		return
	end

	if not result then
		return
	end

	local msg = {
		[-6] = "活动暂未开启，请关注游戏内公告",
		[-8] = "开服第40天开启跨服竞技活动",
		[-4] = "在安全区才可以参加跨服竞技",
		[-7] = "活动即将开启，敬请期待",
		[-3] = "每5秒可刷新一次",
		[-5] = "等级达到40级可参与跨服竞技活动",
		[-2] = "当前赛季未开启"
	}

	if result.Fres and msg[result.Fres] then
		if result.Fres == -3 then
			main_scene.ui:tip("还剩" .. math.floor(result.Fmsectoref / 1000) + 1 .. "秒可刷新", 6)

			if #result.Flist == 0 then
				return
			end
		else
			main_scene.ui:tip(msg[result.Fres], 6)

			return
		end
	end

	local bShowBtn = true

	if result.Fstatus and result.Fstatus == 2 then
		bShowBtn = false

		if result.Fisrefresh == 0 then
			main_scene.ui:tip("当前赛季挑战已结束，仅展示赛季前五名的勇士", 6)
		end
	end

	if not main_scene.ui.panels.arena then
		main_scene.ui:togglePanel("arena")
	end

	main_scene.ui.panels.arena:onSM_ArenaReqList(result, bShowBtn)
end

function mainui:onSM_ArenaReqUserinfo(result)
	local msg = {
		[-3] = "该玩家当前不在榜内",
		[-1] = "当前赛季已结束，无法查看",
		[-2] = "等级不足",
		[-4] = "该玩家信息无法查看"
	}

	if result and msg[result.Fres] then
		main_scene.ui:tip(msg[result.Fres], 6)
	end
end

function mainui:onSM_EquipSuiteActList(result)
	g_data.player.equipSuiteActList = result.FESActList

	if main_scene.ui.panels.equipSuiteAct then
		main_scene.ui.panels.equipSuiteAct:upRightPropScroll(result.FESActList)
	end

	g_data.eventDispatcher:dispatch("M_EQUIPSUITE_DATA_CHG")
end

function mainui:onSM_QueryHongFuBagInfo(result, protoId)
	local openNeedJQ = -1
	local refreshNeedJQ = -1

	for k, v in pairs(result.FList) do
		if v.FActType == g_data.luckyGift.giftType then
			openNeedJQ = v.FOpenNeedJQ
			refreshNeedJQ = v.FShuaNeedJQ
		end
	end

	local status
	local tLBInfo = {}

	for k, v in pairs(result.FPlayerHongFuBagList) do
		if v.FHongFuBagActType == g_data.luckyGift.giftType then
			status = v.FHongFuBagOpenStatus
			tLBInfo = v
		end
	end

	local textType = ""

	if g_data.luckyGift.giftType == 0 then
		textType = "一"
	elseif g_data.luckyGift.giftType == 1 then
		textType = "二"
	elseif g_data.luckyGift.giftType == 2 then
		textType = "三"
	elseif g_data.luckyGift.giftType == 3 then
		textType = "四"
	elseif g_data.luckyGift.giftType == 4 then
		textType = "五"
	elseif g_data.luckyGift.giftType == 5 then
		textType = "六"
	elseif g_data.luckyGift.giftType == 6 then
		textType = "七"
	elseif g_data.luckyGift.giftType == 7 then
		textType = "八"
	end

	if status then
		local texts = {}

		texts[1] = {
			"你"
		}
		texts[2] = {
			"已开启鸿福袋(" .. textType .. ")[未领奖品]",
			display.COLOR_RED
		}
		texts[3] = {
			",\n将为你打开已开启的鸿福袋(" .. textType .. ")\n("
		}
		texts[4] = {
			"不消耗奖券和剩余次数",
			display.COLOR_RED
		}
		texts[5] = {
			")"
		}

		an.newMsgbox(texts, function(idx)
			if idx == 1 then
				main_scene.ui:togglePanel("luckyGift", {
					type = g_data.luckyGift.giftType,
					openNum = openNeedJQ,
					refreshNum = refreshNeedJQ,
					tInfo = tLBInfo
				})
			end
		end, {
			disableScroll = true,
			title = "提示",
			center = true,
			hasCancel = true,
			btnTexts = {
				"确定",
				"取消"
			}
		})
	else
		local texts = {}

		texts[1] = {
			"确认消耗"
		}
		texts[2] = {
			openNeedJQ .. "奖券",
			display.COLOR_RED
		}
		texts[3] = {
			"开启"
		}
		texts[4] = {
			"鸿福袋(" .. textType .. ")",
			display.COLOR_RED
		}
		texts[5] = {
			"吗?"
		}

		an.newMsgbox(texts, function(idx)
			if idx == 1 then
				if g_data.player:getCoupon() < openNeedJQ then
					main_scene.ui:tip("奖券不足，请前往商城兑换")
				else
					main_scene.ui:togglePanel("luckyGift", {
						type = g_data.luckyGift.giftType,
						openNum = openNeedJQ,
						refreshNum = refreshNeedJQ
					})
				end
			end
		end, {
			disableScroll = true,
			title = "提示",
			center = true,
			hasCancel = true,
			btnTexts = {
				"确定",
				"取消"
			}
		})
	end
end

function mainui:onSM_QueryWingEquipInfo(result)
	if result then
		g_data.player.wingEquipInfoList = result.FWEInfoList or {}

		g_data.eventDispatcher:dispatch("M_WINGEQUIP_DATA_CHG")
		g_data.eventDispatcher:dispatch("M_WINGEQUIP_CHG")
		self:refreshRedPoint()
	end
end

function mainui:onSM_Group5v5PkInfo(result)
	if result then
		if result.FSelfIsA then
			self.leftTopTip:showPVP(result.FAList, result.FBList)
		else
			self.leftTopTip:showPVP(result.FBList, result.FAList)
		end
	end
end

function mainui:onSM_QueryEquipBarInfo(result, protoId)
	if result then
		g_data.equipGrid:setEquipBarInfo(result)

		local equip = main_scene.ui.panels.equip

		if equip and equip.page and equip.page == "equip" and equip.lblEquipBarTips then
			equip:showContent("equip")

			local protector = equip:getChildByName("equipProtector")

			if protector then
				protector:refeshGridInfo()
			end
		end
	end
end

function mainui:onSM_PsychicPowerInfo(result, protoId)
	if result then
		g_data.zodiac:setServerData(result)
	end
end

function mainui:onSM_QueryRankEquipInfo(result, protoId)
	if result then
		g_data.player:setMilitaryEquipList(result.FREInfoList)

		if main_scene.ui.panels.militaryEquipUpgrade then
			main_scene.ui.panels.militaryEquipUpgrade:refreshView()
		end
	end
end

function mainui:onSM_QueryCardInfo(result)
	if result then
		for k, v in ipairs(result.FCardTypeInfoList) do
			local bIn = false

			for _k, _v in ipairs(g_data.player.cardSysInfoList) do
				if v.FCardType == _v.FCardType then
					g_data.player.cardSysInfoList[_k] = v
					bIn = true
				end
			end

			if not bIn then
				g_data.player.cardSysInfoList[#g_data.player.cardSysInfoList + 1] = v
			end
		end

		g_data.eventDispatcher:dispatch("M_CARDQUIP_DATA_CHG")
		g_data.eventDispatcher:dispatch("M_CARD_DATA_CHG")
		g_data.eventDispatcher:dispatch("M_CARD_UPDATE_DATA_CHG")
		g_data.eventDispatcher:dispatch("M_CARD_PROP_DATA_CHG")
		self:checkCardSysRedPoint({
			eventType = EType.Bag.refreshBag
		})
		self:autoRecoveryCard({
			eventType = EType.Bag.refreshBag
		})
	end
end

function mainui:onSM_GetCardReward(result)
	if result then
		local texts = {}
		local reward = def.cardSys.getCardReward(result.FCardRewardIdx)
		local rwStrs = def.cardSys.splitReward(reward)

		texts[1] = {
			"恭喜您获得了：\n"
		}

		for k, v in ipairs(rwStrs) do
			texts[#texts + 1] = {
				v .. "\n"
			}
		end

		an.newMsgbox(texts, function(idx)
			return
		end, {
			contentLabelSize = 20,
			title = "提示",
			hasCancel = false
		})
	end
end

function mainui:onSM_ClientQueryMIInfo(result, protoId)
	if result then
		g_data.player.medalImpressInfo = result.FmiList
		g_data.player.medalImpressNum = result.FmiHaveCount

		local medalImpress = main_scene.ui.panels.medalImpress

		if medalImpress then
			medalImpress:onSelectWZ(medalImpress.selIdx or 1)
		end
	end
end

function mainui:onSM_QueryEmoticonInfo(result, protoId)
	if result then
		g_data.player:setEmojiList(result.FEmoticonInfos)

		if main_scene.ui.panels.fashion then
			main_scene.ui.panels.fashion:updateEmotionView()
		end
	end
end

function mainui:onSM_HellPKGetDetail(result, protoId)
	if not result then
		return
	end

	if self.crossChallenge then
		local crossChallenge = self.crossChallenge

		crossChallenge:setDetail(result.Fid1get, result.Fid2get, result.Fid3get, result.Fid4get, result.Fclose)
		print("onSM_HellPKGetDetail 跨服炼狱挑战： ", result.Fid1get, result.Fid2get, result.Fid3get, result.Fid4get, result.Fclose)
	end
end

function mainui:onSM_NewHellPK_RankInfo(result, protoId)
	if not result then
		-- block empty
	end

	if not self.newCrossChallenge then
		return
	end

	print("onSM_NewHellPK_RankInfo 新跨服炼狱挑战： ", result.FScore, result.FRank, result.FCloseUI)
	self.newCrossChallenge:setDetail(result.FScore, result.FRank, result.FCloseUI)
end

function mainui:showGlobleTimeLabel(tipStr, pos, freeTime, node, val)
	function getHasTime(freeTime)
		local h, m, s, tmp = 0, 0, 0, freeTime

		h = math.floor(tmp / 3600)
		tmp = tmp - h * 3600
		m = math.floor(tmp / 60)
		tmp = tmp - m * 60
		s = tmp

		if h < 10 then
			h = "0" .. h
		end

		if m < 10 then
			m = "0" .. m
		end

		if s < 10 then
			s = "0" .. s
		end

		return h .. ":" .. m .. ":" .. s
	end

	if self.globleTimeLabel and self.globleTimeLabel.state and self.globleTimeLabel.state == 1 then
		return
	end

	self.globleTimeLabel = {}
	self.globleTimeLabel.state = 1
	self.globleTimeLabel.lbl = an.newLabel(tipStr .. getHasTime(freeTime), 20, 1, {
		color = def.colors.Cf1ed02
	}):anchor(0, 0):addto(node):pos(pos.x, pos.y)

	self.globleTimeLabel.lbl:retain()

	self.globleTimeLabel.schedul = scheduler.scheduleGlobal(function()
		freeTime = freeTime - 1

		if self.globleTimeLabel and self.globleTimeLabel.state == 1 and freeTime >= 0 and self.globleTimeLabel.lbl then
			self.globleTimeLabel.lbl:setText("活动剩余时间：" .. getHasTime(freeTime))

			return
		end

		if self.closeGlobleTimeLabel then
			self:closeGlobleTimeLabel(val)
		end
	end, val)
end

function mainui:closeGlobleTimeLabel(val)
	if self.globleTimeLabel and self.globleTimeLabel.state and self.globleTimeLabel.state == 0 then
		return
	end

	if self.globleTimeLabel then
		self.globleTimeLabel.state = 0

		if self.globleTimeLabel and self.globleTimeLabel.schedul then
			scheduler.unscheduleGlobal(self.globleTimeLabel.schedul)

			if not self.globleTimeLabel then
				self.globleTimeLabel = {}
			end

			self.globleTimeLabel.schedul = nil
			self.globleTimeLabel.lbl = nil
			self.globleTimeLabel.state = 0
		end
	end
end

function mainui:onSM_ChangeSexSucess(result, protoId)
	print_r(result)

	if not result then
		return
	end

	main_scene:smallExit()
end

function mainui:onSM_ClientGetUserConfig(result)
	if not result then
		return
	end

	for k, v in pairs(result.FucList) do
		local key = v.FKeyStr

		g_data.setting.setStrSetting(key, v.FValueStr)
		cache.saveSetting(common.getPlayerName(), key)
	end

	if main_scene.ui.panels.setting then
		main_scene.ui.panels.setting:updateSetting()
	end

	if result.Fretcode == 1 then
		if #result.FucList == 0 then
			self:tip("尚未保存设置！", 6)
			common.addMsg("尚未保存设置！", Lobyte(65499), Hibyte(65499), true)
		else
			self:tip("读取设置成功！", 6)
			common.addMsg("读取设置成功！", Lobyte(65499), Hibyte(65499), true)
		end
	else
		self:tip("读取设置失败！", 6)
		common.addMsg("读取设置失败！", Lobyte(65499), Hibyte(65499), true)
	end
end

function mainui:openWoC()
	local data = {
		yori = 2,
		key = "btnWoC",
		xFix = -1,
		y = 575,
		xori = 2,
		x = 690
	}

	if not self.WoCIcon then
		self.WoCIcon = self.console:addWidget(data)
	end
end

function mainui:onSM_PLevelNpcSendIcon(result, protoId)
	local data = {
		yori = 2,
		key = "cruiseMerchant",
		xFix = -1,
		y = 575,
		xori = 2,
		x = 160
	}

	if not self.merchantIcon and result.Fleftsec > 0 and result.Fcontleft then
		self.merchantIcon = self.console:addWidget(data)
	end

	local function removeMerchantIcon()
		self.console:removeWidget("cruiseMerchant")

		self.merchantIcon = nil

		if self.panels.cruiseMerchant then
			self.panels.cruiseMerchant:hidePanel()
		end

		if main_scene.ui.cruiseMerchantDialogue then
			main_scene.ui.cruiseMerchantDialogue:removeSelf()

			main_scene.ui.cruiseMerchantDialogue = nil
		end
	end

	if result.Fcontleft <= 0 then
		self.console:removeWidget("cruiseMerchant")

		self.merchantIcon = nil
	end

	if not self.merchantIcon then
		return
	end

	if self.leftTimeCruiseMerchant then
		self.leftTimeCruiseMerchant:removeSelf()

		self.leftTimeCruiseMerchant = nil
	end

	local leftSec = result.Fleftsec

	self.leftTimeCruiseMerchant = an.newLabel(common.getTimeStringBySec(leftSec), 20, 1, {
		color = def.colors.Cdcd2be
	}):anchor(0.5, 0.5):addTo(self.merchantIcon):pos(self.merchantIcon:getw() / 2, -12)

	local function scheCB()
		leftSec = leftSec - 1

		if leftSec > 0 then
			self.leftTimeCruiseMerchant:setString(common.getTimeStringBySec(leftSec))
		else
			self.leftTimeCruiseMerchant:setString("已结束")
			self.leftTimeCruiseMerchant:stopAllActions()

			if self.merchantIcon then
				removeMerchantIcon()
			end
		end
	end

	self.leftTimeCruiseMerchant:schedule(scheCB, 1)

	local numbg = res.get2("pic/console/notice/num.png"):addTo(self.merchantIcon):pos(self.merchantIcon:getw() + 10, self.merchantIcon:geth() + 9):anchor(1, 1)

	if self.leftItems then
		self.leftItems:removeSelf()

		self.leftItems = nil
	end

	self.leftItems = an.newLabel(result.Fcontleft, 20, 1):addTo(numbg):pos(numbg:getw() / 2, numbg:geth() / 2):anchor(0.5, 0.5)

	if self.panels.cruiseMerchant and leftSec == 86400 then
		self.panels.cruiseMerchant:hidePanel()

		local rsb = DefaultClientMessage(CM_PLevelReqAllGoods)

		MirTcpClient:getInstance():postRsb(rsb)
	end
end

function mainui:onSM_PLevelNpcSendAll(result, protoId)
	main_scene.ui:togglePanel("cruiseMerchant", result)
end

function mainui:startNewassCountdown(result, _)
	local isNewassShow

	if result and #result >= 3 then
		isNewassShow = result[1] == "1"
	end

	if not isNewassShow then
		return
	end

	self._newassIcon = main_scene.ui.console:get("newassGift")

	if not self._newassIcon then
		return
	end

	local iconPath = "pic/console/panel-icons/newassGift" .. (result[3] or 1) .. ".png"
	local tx = res.gettex2(iconPath)

	self._newassIcon.btn.sprite:setTexture(tx)

	local function removeNewassIcon()
		self.console:removeWidget("newassGift")

		self._newassIcon = nil

		if self.panels.activity and self.panels.activity.tag2 == 1183 then
			if self._newassDialogue then
				self._newassDialogue:removeSelf()

				self._newassDialogue = nil
			end

			self.panels.activity:closeAll()
		end
	end

	if self._leftTimeNewass then
		self._leftTimeNewass:removeSelf()

		self._leftTimeNewass = nil
	end

	local year, month, day, hours, minutes, seconds = result[2]:match("^(%d%d%d%d)-(%d%d)-(%d%d) (%d%d):(%d%d):(%d%d)$")
	local date = os.time({
		year = year or 0,
		month = month or 0,
		day = day or 0,
		hour = hours or 0,
		min = minutes or 0,
		sec = seconds or 0
	})
	local leftSec = (date or 0) + 86400 - os.time()

	self._leftTimeNewass = an.newLabel(common.getTimeStringBySec2(leftSec), 20, 1, {
		color = def.colors.Cdcd2be
	}):anchor(0.5, 0.5):addTo(self._newassIcon):pos(self._newassIcon:getw() / 2, -12)

	local function scheduleCB()
		leftSec = leftSec - 1

		if leftSec > 0 then
			self._leftTimeNewass:setString(common.getTimeStringBySec2(leftSec))
		else
			self._leftTimeNewass:setString("已结束")
			self._leftTimeNewass:stopAllActions()

			if self._newassIcon then
				removeNewassIcon()
			end
		end
	end

	self._leftTimeNewass:schedule(scheduleCB, 1)
end

function mainui:onSM_YbBoxSendAll(result, protoId)
	main_scene.ui.waiting:close("YBBOX_TAG")

	if self.panels.activity then
		self.panels.activity:showYBBox(result)
	else
		main_scene.ui:showPanel("activity", {
			dftab2 = 1179
		})

		local rsb = DefaultClientMessage(CM_Act_DetailRequest)

		rsb.FActId = 0
		rsb.FParamA = 0
		rsb.FParamB = 0
		rsb.FStrParam = "tab1"

		MirTcpClient:getInstance():postRsb(rsb)
	end
end

function mainui:onSM_QueryReportReason(result, protoId)
	main_scene.ui:togglePanel("juBao", {
		FInfoList = result.FInfoList
	})
end

function mainui:onSM_BoReviveRingOk(result, protoId)
	g_data.player.canRelive = result.FCanRevive
	g_data.player.canSHSDRelive = result.FCanBeRedemption
end

function mainui:onSM_QueryGodBodyInfo(result, protoId)
	g_data.godBody:setGodBodyList(result.FInfoList)
end

function mainui:onSM_ReviveProtect(result, protoId)
	if not g_data.setting.protected.role.relive then
		local logMsg = "data.setting中reset中是否找到了relive:" .. tostring(not g_data.setting.____reliveResetFlag) .. "data.setting中getSetting中是否找到了relive:" .. tostring(not g_data.setting.____reliveSavedFlag) .. "\nprotected.role:" .. serializeTable(g_data.setting.protected.role or {})

		luaReportException("Relive_Nil_Value", "attempt to index field 'relive'", logMsg)

		g_data.setting.protected.role.relive = {
			lastTime = 0,
			space = 10000,
			uses = "随机传送卷",
			isPercent = false,
			value = 0,
			enable = false
		}
	end

	g_data.setting.protected.role.relive.enable = result.FBoReviveProtect

	if not g_data.setting.protected.role.shsdRelive then
		g_data.setting.protected.role.shsdRelive = {
			lastTime = 0,
			space = 10000,
			uses = "随机传送卷",
			isPercent = false,
			value = 0,
			enable = false
		}
	end

	g_data.setting.protected.role.shsdRelive.enable = result.FBoRedemptionProtect
end

local ccuiWidgets = {
	["ccui.CheckBox"] = 1,
	["ccui.LoadingBar"] = 1,
	["ccui.Button"] = 1,
	["ccui.ScrollView"] = 1,
	["ccui.TextField"] = 1,
	["ccui.TextAtlas"] = 1,
	["ccui.ListView"] = 1,
	["ccui.Layout"] = 1,
	["ccui.PageView"] = 1,
	["ccui.TextBMFont"] = 1,
	["ccui.Text"] = 1,
	["ccui.ImageView"] = 1,
	["ccui.Slider"] = 1
}

function mainui:cancelAllMask(key)
	masks = self.maskWidgets[key] or {}

	for k, v in pairs(masks) do
		if v and not tolua.isnull(v) and v.setTouchEnabled then
			v:setTouchEnabled(true)
		end
	end
end

function mainui:maskCCUIWidget(node, rets)
	if not node then
		return {}
	end

	local children = node:getChildren()

	for k, v in pairs(children) do
		self:maskCCUIWidget(v, rets)

		if v:isVisible() and v:isTouchEnabled() and ccuiWidgets[tolua.type(v)] then
			rets[#rets + 1] = v

			v:setTouchEnabled(false)
		end
	end
end

function mainui:maskCCSPanels(key, panels)
	if not key then
		return
	end

	panels = panels or {}

	local masks = {}

	for k, v in pairs(panels) do
		if v and v:isVisible() then
			self:maskCCUIWidget(v, masks)
		end
	end

	self.maskWidgets[key] = masks
end

function mainui:onSM_QueryCliTLCardInfo(result)
	g_data.limCard:setPlayerLimCardData(result)
	g_data.eventDispatcher:dispatch("M_LIMCARD_DATA_CHG")

	for k, v in ipairs(result.FCliTLCardInfoList) do
		local needShowTip = true

		for m, n in ipairs(g_data.limCard.tLastGetData) do
			if v.FID == n.FID then
				needShowTip = false
			end
		end

		if needShowTip and self.firstGetLimCardData then
			g_data.badgeSystem:set(badge.B_LIMCARD_VALID, true)

			g_data.limCard.needShowEqTip = true
		end
	end

	g_data.limCard.tLastGetData = result.FCliTLCardInfoList
	self.firstGetLimCardData = true
end

function mainui:onSM_AskTLCardCfg(result)
	g_data.limCard:setLimCardCfg(result)
	self:checkFirstCardSys()
end

function mainui:onSM_PSoulAbilSendAll(result, protoId)
	if not result then
		return
	end

	g_data.castingSoul:fillData(result)
	g_data.eventDispatcher:dispatch("M_CASTINGSOUL_DATA_CHG")
end

function mainui:onSM_PSoulAbilQueryOther(result, protoId)
	if not result then
		return
	end

	g_data.eventDispatcher:dispatch("M_OTHER_CASTINGSOUL_DATA", result)
end

function mainui:onSM_OpenWindow(result)
	if not result then
		return
	end

	if result.Fid == 1 then
		if main_scene.ui.panels.medalOfHonor then
			main_scene.ui:togglePanel("medalOfHonor")
		end

		main_scene.ui:togglePanel("medalOfHonor")
	end
end

function mainui:onSM_PKFDTError(result)
	if not result then
		return
	end

	if main_scene.ui.panels.medalOfHonor then
		main_scene.ui:togglePanel("medalOfHonor")
	end
end

function mainui:onSM_SendFlag(result)
	if not result then
		return
	end

	if result.Fid == 1 then
		g_data.moh:setActive(result.Fval == 1)
	end

	if result.Fid == 2 then
		g_data.moh:setCanHangUp(result.Fval > 0)
	end

	if result.Fid == 3 then
		if result.Fval == 100 then
			g_data.godItem:setIsOpen(true)
			self:queryGodItemInfo()
		else
			g_data.godItem:setIsOpen(false)
		end

		if not g_data.firstOpen:get("godItem") then
			self:checkFirstGodItem()
		end
	end

	if result.Fid == 4 then
		self.__starOpen = result.Fval == 100

		if not g_data.firstOpen:get("starChart") then
			self:checkFirstStarChart()
		end
	end

	if result.Fid == 5 then
		self.__godSwordOpen = result.Fval == 100
	end

	if result.Fid == 6 then
		self.__meridianOpen = result.Fval == 100

		if not g_data.firstOpen:get("meridian") then
			self:checkFirstMeridian()
		end
	end

	if result.Fid == 7 then
		self.__birthSignOpen = result.Fval == 100

		if not g_data.firstOpen:get("birthsign") then
			self:checkFirstBirthSign()
		end
	end

	if result.Fid == 8 and result.Fval == 100 then
		g_data.unrealBanner:setIsOpen(true)

		local rsb = DefaultClientMessage(CM_QueryUnrealBanner)

		MirTcpClient:getInstance():postRsb(rsb)
	end

	if result.Fid == 9 then
		self.__wuxingOpen = result.Fval == 100

		if not g_data.firstOpen:get("wuxing") then
			self:checkFirstWuxing()
		end
	end

	if result.Fid == 11 then
		self.__penDantOpen = result.Fval == 100

		if not g_data.firstOpen:get("pendantPanel") then
			self:checkFirstPenDant()
		end
	end

	if result.Fid == 12 then
		g_data.crossServerScoreShop:setActive(result.Fval == 1)
	end

	if result.Fid == 13 then
		self.__baguaOpen = result.Fval == 100

		if not g_data.firstOpen:get("baguaUpgrade") then
			g_data.badgeSystem:set(badge.B_ROLE_BAGUA, true)
		end
	end

	if result.Fid == 14 then
		self.__godArmorOpen = result.Fval == 100
	end

	if result.Fid == 15 then
		g_data.territoryBattle:setActive(result.Fval ~= 0)
	end
end

function mainui:queryGodItemInfo()
	self.__godItemFlag = true

	local rsb = DefaultClientMessage(CM_QueryGodItemInfo)

	MirTcpClient:getInstance():postRsb(rsb)
end

function mainui:onSM_PAmuletRedPoint(result)
	if not result then
		return
	end

	if IS_PLAYER_DEBUG then
		print_r(result)
	end

	if result.Fres and not g_data.badgeSystem:isShow(badge.B_ROLE_AMULET) then
		g_data.firstOpen:set("amulet", false)
		g_data.badgeSystem:set(badge.B_ROLE_AMULET, true)
	end
end

function mainui:changeActivityTab(title, tabIdx, ActId)
	if not self.panels.activity then
		return
	end

	self.panels.activity.title:setText(title)

	local map = self.panels.activity.tabs

	for i, v in ipairs(map) do
		if i == tabIdx then
			v:select()

			v.state = true

			v:setLocalZOrder(10)
			v.label:setColor(def.colors.Cf0c896)
		else
			v:unselect()

			v.state = false

			v:setLocalZOrder(10 - i)
			v.label:setColor(def.colors.Ca6a197)
		end
	end

	local rsb = DefaultClientMessage(CM_Act_ButtonClick)

	rsb.FActId = ActId
	rsb.FButtonId = 0
	rsb.FParamA = 0
	rsb.FParamB = 0
	rsb.FStrParam = "tab2"

	MirTcpClient:getInstance():postRsb(rsb)
	main_scene.ui.waiting:show(10, "ACTIVITY_TAB2")
end

function mainui:onSM_QueryFightSpiritInfo(result)
	if not result then
		return
	end

	g_data.soliderSoul:fillData(result.FInfoList)
	g_data.eventDispatcher:dispatch("M_SOLIDERSOUL_DATA_CHG")
end

function mainui:onSM_GildBattleScore(result)
	if not result then
		return
	end

	self.battleScore:setData({
		gildList = result.FGildList,
		curName = result.FGildName,
		curScore = result.FScore,
		curIdx = result.FIdx
	})
	self.battleScore:updateScoreContent()
end

function mainui:onSM_TowelLv(result)
	if result then
		g_data.player.maskLevel = result.FTowelLv
		g_data.player.maskIllusionLevel = result.FTowelVaryLv
		g_data.player.maskStampLevel = result.FTowelStepLv
	end
end

function mainui:onSM_PrestNum(result)
	if result and g_data.player.ability then
		g_data.player.ability.FPrestValue = result.FPrestNum
	end
end

function mainui:requestPenDant()
	local rsb

	rsb = DefaultClientMessage(CM_TNBAbilSendAll)
	rsb.FAppid = 6

	MirTcpClient:getInstance():postRsb(rsb)
end

function mainui:onSM_TNBAbilSendAll(result)
	if result.FAppid == 6 then
		g_data.pendant:equipPD(result)
	end
end

function mainui:onSM_TNBAbilQueryOther(result)
	if result.FAppid == 6 then
		g_data.pendant:equipOtherPD(result)
	end
end

function mainui:onSM_QueryGodRingEchoList(result)
	if result then
		def.godring.setRingJHData(result.FGodRingEchoList)
		g_data.eventDispatcher:dispatch("M_GODRINGJH_DATA_CHG")
	end
end

function mainui:useGodMonsterItem(itemData)
	if not itemData then
		return
	end

	if main_scene.ui.console:get("btnGodMonsterItem") then
		local x = main_scene.ui.console:get("btnGodMonsterItem")

		if x.skillData and x.skillData.iscdBegin then
			return
		end
	end

	local targetId = self.console.controller.lock.tarRoleID

	if targetId then
		local target = main_scene.ground.map:findRole(targetId)

		if not target or target.race ~= 19 and target.appr ~= 962 then
			targetId = nil
		end
	end

	if not targetId and main_scene.ground and main_scene.ground.map then
		for k, v in pairs(main_scene.ground.map.mons) do
			if v.race == 19 and v.appr == 962 and not v.die then
				targetId = k
			end
		end
	end

	if not targetId then
		main_scene.ui:tip("轰天雷只能对神兽使用")

		return
	end

	local rsb = DefaultClientMessage(CM_GodMonsterItem)

	rsb.FItemIdent = itemData.FItemIdent
	rsb.FItemName = itemData:getVar("name")
	rsb.FGodMonsterId = targetId or "0"

	MirTcpClient:getInstance():postRsb(rsb)

	local player = main_scene.ground.player

	if player then
		local tmp = {
			roleid = g_data.player.roleid,
			ident = SM_SPELL,
			roleParams = {
				targetY = player.y,
				targetX = player.x,
				effect = {
					magicLevel = 0,
					effectID = -1,
					magicId = 1
				}
			}
		}

		main_scene.ground.map:addMsg(tmp)
	end
end

function mainui:queryAirborneBox(targetId)
	if not main_scene.ui.panels.airborneBox then
		local rsb = DefaultClientMessage(CM_SendBoxMonster)

		rsb.FMonid = targetId

		MirTcpClient:getInstance():postRsb(rsb)
	end
end

function mainui:onSM_GodMonsterItem(result)
	if result then
		if main_scene.ui.console:get("btnGodMonsterItem") then
			local x = main_scene.ui.console:get("btnGodMonsterItem")

			if not x.skillData then
				local shit = {}

				shit.iscdBegin = true
				x.data.magicId = 111
				x.skillData = shit
				x.cdtime = 1
				x.isHongTianLei = true
			end

			main_scene.ui.console:get("btnGodMonsterItem"):startCd()
		end

		main_scene.ui.console:fillPropTest()
	end

	if main_scene.ui.console:get("btnRobber") then
		main_scene.ground:startBtnCd("btnRobber", 1)
	end
end

function mainui:onSM_FirstRecharge(result)
	if result then
		main_scene.ui:togglePanel("firstRechargeView", result)
	end
end

function mainui:onSM_ShowRedPoint(result)
	if result then
		g_data.activity:showRedPoint(result)

		if result.Fname == "TeamBattleJob" or result.Fname == "TeamBattleGuess" then
			g_data.firstOpen:set("TeamBattlecontest", false)
			g_data.badgeSystem:set(badge.B_INTERFACE_BATTLE_CONTEST, true)

			g_data.moh.battleRed = result.Fname

			g_data.eventDispatcher:dispatch("TEAM_BATTLE_SHOW_RED_POINT")
		end
	end
end

function mainui:onSM_DisablePage(result)
	if result then
		g_data.activity:disablePage(result)
	end
end

function mainui:onSM_QueryHMPCharmInfo(result)
	if not result then
		return
	end

	g_data.bloodStoneCell:setInfo(result)
	g_data.eventDispatcher:dispatch("M_BLOODSTONE_DATA_CHG")
end

function mainui:onSM_ExtLEVELUP(result, protoId)
	if result then
		local data = g_data.select.roles[g_data.select.selectIndex]

		MirSDKAgent:logEvent("OnRoleLevelExtup", {
			createTimestamp = data.createTime,
			roleId = data.userId,
			roleLevel = result.FCurLv,
			roleName = data.name,
			sex = data.sex,
			job = data.job,
			zoneId = g_data.login.localLastSer.id,
			zoneName = g_data.login.localLastSer.name
		})

		g_data.player.ability.ExtLevel = result.FCurLv

		self:tip("升级!")
		main_scene.ui.console:call("infoBar", "uptLevel")
		self:checkServerActOpenInfo()
	end
end

function mainui:onSM_WinExtExp(result, protoId)
	if result then
		if g_data.player.ability then
			g_data.player.ability.CurrExtExp = result.FCurExp
		end

		local ExpType = {
			TExpTypeActivity = 5,
			TEtscore105 = 11,
			TExpTypeMultiScroll = 2,
			TEtscore106 = 12,
			TExpTypeNewGrowth = 6,
			TEtScore103 = 9,
			TEtscore107 = 13,
			TEtScore104 = 8,
			TExpTypePower = 4,
			TEtScore101 = 7,
			TEtScore102 = 10,
			TExpTypeNormal = 0,
			TExpTypeBaseExp = 1,
			TExpTypeEnergy = 3
		}

		if not g_data.setting.base.showExpEnable or g_data.setting.base.showExpEnable and g_data.setting.base.showExpValue <= tonumber(result.FAddExp) then
			if result.Flag == ExpType.TExpTypeEnergy then
				self:tip("精力巅峰经验值 +" .. result.FAddExp, 2)
			elseif result.Flag == ExpType.TExpTypePower then
				self:tip("活力巅峰经验值 +" .. result.FAddExp, 2)
			elseif result.Flag == ExpType.TExpTypeBaseExp then
				self:tip("巅峰经验值 +" .. result.FAddExp, 2)
			end
		end

		self.console:call("bottom", "upt")
		self.console.autoRat:onExpUpdate()
	end
end

function mainui:onSM_QueryBattleTeamEndInfo(result, protoId)
	if not result then
		return
	end

	if result.FSecType == 2 then
		self.countDown:stop(self.countDown.TYPE.BATTLECONTEST)
	else
		self.countDown:set(self.countDown.TYPE.BATTLECONTEST, {
			timeType = result.FSecType,
			totalSec = result.FSecValue
		})
	end
end

function mainui:onSM_PlayerBTBattleInfo(result, protoId)
	if not result then
		return
	end

	main_scene.ui:hidePanel("battleContestResult")
	main_scene.ui:showPanel("battleContestResult", {
		netData = result
	})
end

function mainui:onSM_MCVipTime(result, protoId)
	if not result then
		return
	end

	if result.FEndTime then
		g_data.player.monthCardEndTime = tonumber(result.FEndTime)
	end

	if result.FZXEndTime then
		g_data.player.ZXCardEndTime = tonumber(result.FZXEndTime)
	end

	if result.FJJEndTime then
		g_data.player.JJCardEndTime = tonumber(result.FJJEndTime)
	end

	if result.FSCEndTime then
		g_data.player.seasonCardEndTime = tonumber(result.FSCEndTime)
	end

	g_data.player.privilegedItemNum = result.FHaveItemCount or 0
end

function mainui:onSM_QueryFateInfo(result, protoId)
	if result and g_data.mumerology then
		g_data.mumerology:updateByNetData(result, protoId)
		g_data.eventDispatcher:dispatch("M_FATE_LIST_CHG")
	end
end

function mainui:onSM_QueryOtherFateInfo(result, protoId)
	if not result then
		return
	end

	g_data.eventDispatcher:dispatch("M_OTHER_MUMEROLOGY_DATA", result, protoId)
end

function mainui:onSM_New_Act_Request_Res(result, protoId)
	main_scene.ui.waiting:close("CM_NEW_ACT_REQUEST")

	if not result then
		return
	end

	if ActivityType.ACT_DECADE == result.FActType then
		if result.FActId == 0 then
			g_data.newActivity:parseResult(result)

			if g_data.newActivity.iconClick then
				if #g_data.newActivity.decadeActivities > 0 and not g_data.player:getIsCrossServer() then
					main_scene.ui:togglePanel("newActivity", {
						index = 1
					})
				end

				g_data.newActivity.iconClick = false
			end

			if self.panels.newActivity then
				self.panels.newActivity:updatePageTabList()
			end
		elseif result.FActId == 1000022 then
			local temp = result.FContent == "" and {} or quickJson.decode(result.FContent) or {}

			if not temp.Act then
				return
			end

			if self.panels.welcome then
				self.panels.welcome:hidePanel()
			end

			main_scene.ui:togglePanel("welcome", temp)
		else
			if not self.panels.newActivity then
				self:togglePanel("newActivity", {
					index = 1
				})
			end

			if self.panels.newActivity:getselectedActId() == result.FActId then
				local viewName = g_data.newActivity:getActNameById(result.FActId)

				if result.FButtonId == 0 then
					local temp = quickJson.decode(result.FContent) or {}

					if not temp.Act then
						return
					end

					self.panels.newActivity:refreshActivityDetailView(temp.Act or "", viewName)
				elseif result.FButtonId == 9999 then
					local temp = json.decode(result.FContent)
					local str = temp.Act[1].Question

					if str and str ~= "" then
						local strListTemp = string.split(str, "|")
						local strList = {}

						for i, v in ipairs(strListTemp) do
							table.insert(strList, {
								v .. "\n"
							})
						end

						an.newMsgbox(strList, nil, {
							contentLabelSize = 20
						})
					end
				else
					local _selectedPageView = self.panels.newActivity:getSelectedPageView()

					if _selectedPageView then
						local pageName = _selectedPageView:getName()

						if pageName == viewName then
							_selectedPageView:btnCb(result)
						else
							print(" ============>>>>> 当前选中的 pageview 跟之前加载的不是同一个 view？", pageName, viewName)

							return
						end
					else
						print(" ============>>>>> 没找到当前选中的 pageview？")

						return
					end
				end
			end
		end
	elseif ActivityType.ACT_NORMALDAY == result.FActType then
		if ActivityType.ACT_NORMALDAY_ACTID == result.FActId then
			g_data.newActivity:parseResult(result)
		elseif not g_data.newActivity:npcActIconShown() then
			self:tip("活动已过期！")

			if self.panels.npcActivity then
				self:togglePanel("npcActivity")
			end

			return
		elseif self.panels.npcActivity then
			if self.panels.npcActivity.selectedSecondId ~= result.FActId then
				print(" ============>>>> 切换2级菜单")
				self.panels.npcActivity:selectedSecondMenu(result.FActId)

				local thirdMenuData = temp.Act

				if thirdMenuData and #thirdMenuData > 0 then
					local selectedThirdIdx = 1

					self.panels.npcActivity:refreshThirdBtnMenu(result.FActId, thirdMenuData)
					self.panels.npcActivity:selectedThirdMenu(result.FActId, selectedThirdIdx)

					local rsb = DefaultClientMessage(CM_New_Act_Request)

					rsb.FActType = ActivityType.ACT_NORMALDAY
					rsb.FActId = thirdMenuData[selectedThirdIdx].AID
					rsb.FButtonId = thirdMenuData[selectedThirdIdx].ButtonId
					rsb.FExtraStr = ""

					MirTcpClient:getInstance():postRsb(rsb)
					main_scene.ui.waiting:show(10, "CM_NEW_ACT_REQUEST")
				end
			else
				print(" ==========<><><><><><><><><><> 2级菜单对应上了？ ", self.panels.npcActivity.selectedThirdId)

				if self.panels.npcActivity.selectedThirdId ~= result.FButtonId then
					print(" ============>>>> 切换3级菜单")

					local btnIdx = self.panels.npcActivity:findThirdMenuIdx(result.FActId, result.FButtonId)

					if btnIdx > 0 then
						self.panels.npcActivity:selectedThirdMenu(result.FActId, btnIdx)
					end
				end

				self.panels.npcActivity:refreshContent(temp.Act)
			end
		end
	elseif ActivityType.ACT_FIRSTCHARGE == result.FActType then
		if ActivityType.ACT_FIRSTCHARGE_ACTID == result.FActId then
			if result.FExtraStr == "" then
				g_data.firstChargeGift:setContent(temp)
			else
				local rsb = DefaultClientMessage(CM_New_Act_Request)

				rsb.FActType = ActivityType.ACT_FIRSTCHARGE
				rsb.FActId = ActivityType.ACT_FIRSTCHARGE_ACTID
				rsb.FButtonId = 0
				rsb.FExtraStr = ""

				MirTcpClient:getInstance():postRsb(rsb)
			end
		end
	elseif ActivityType.ACT_NEWMANPASSION == result.FActType then
		if ActivityType.ACT_NEWMANPASSION_ACTID == result.FActId then
			if result.FButtonId <= 0 then
				g_data.rookieCarnival:parseResult(result)
			else
				g_data.rookieCarnival:updateActivityList(result)
			end

			if self.panels.rookieCarnival then
				if result.FButtonId > 0 then
					self.panels.rookieCarnival:refreshActivityListTabs(result)
				else
					self.panels.rookieCarnival:refreshActivityTypeTab()
				end
			end
		elseif self.panels.rookieCarnival then
			if result.FButtonId > 0 and result.FButtonId < 10000 then
				self.panels.rookieCarnival:refreshActivityListTabs(result)
			else
				self.panels.rookieCarnival:refreshActivityDetailView(result)
			end
		end
	elseif ActivityType.ACT_GROWTHROAD == result.FActType then
		local growthRoad = main_scene.ui.panels.growthRoad

		function setCreateDay(createDay)
			createDay = createDay or 1

			local oldDay = g_data.player.createDay

			if createDay > 30 then
				self.console:removeWidget("btnGrowthRoad")
				self:checkFirstGrowthRoad()
			end

			if growthRoad and oldDay ~= createDay then
				if createDay > 30 then
					self:hidePanel("growthRoad")
				else
					growthRoad:refreshDayList(g_data.player.createDay, true)
				end
			end
		end

		if result.FActId == ActivityType.ACT_GROWTHROAD_ACTID_LIMITSHOP and result.FButtonId == 0 then
			setCreateDay(tonumber(result.FExtraStr))
		end

		if not growthRoad then
			return
		end

		if result.FActId == ActivityType.ACT_GROWTHROAD_ACTID_GROWTASK then
			if result.FButtonId == 0 then
				local actTable = quickJson.decode(result.FContent)

				if actTable and actTable.Act then
					local createDay = 1

					for k, v in pairs(actTable.Act) do
						if v.BState == 1 and createDay < v.ButtonId then
							createDay = v.ButtonId
						end
					end

					setCreateDay(createDay)
				end
			elseif result.FButtonId >= 1 and result.FButtonId <= 30 then
				local actTable = quickJson.decode(result.FContent)

				if actTable then
					growthRoad:showGrowTaskList(actTable.Act)
				end
			else
				local retVal = tonumber(result.FExtraStr) or -1

				if retVal == 1 then
					growthRoad:refreshGrowTaskFrame()
				end
			end
		elseif result.FActId == ActivityType.ACT_GROWTHROAD_ACTID_LIMITSHOP then
			if result.FButtonId >= 1 and result.FButtonId <= 30 then
				local actTable = quickJson.decode(result.FContent)

				if actTable then
					growthRoad:showLimitShopItem(actTable.Act_FixTimeSale)
				end
			elseif result.FButtonId == 52001 then
				local retVal = tonumber(result.FExtraStr) or -1

				if retVal == 1 then
					self:tip("购买成功！")
					growthRoad:refreshLimitItemFrame()
				end
			end
		elseif result.FActId == ActivityType.ACT_GROWTHROAD_ACTID_SCORESHOP then
			if result.FButtonId == 0 then
				local actTable = quickJson.decode(result.FContent)

				if actTable then
					growthRoad:showExchangeShopPage(actTable.Act)
				end
			else
				local retVal = tonumber(result.FExtraStr) or -1

				if retVal == 1 then
					self:tip("购买成功！")
					growthRoad:refreshExchangeItemFrame()
				end
			end
		end
	elseif ActivityType.ACT_PASSIONRACE == result.FActType then
		if ActivityType.ACT_PASSIONRACE_ACTID == result.FActId then
			g_data.passionRace:parseResult(result)

			if self.panels.passionRace then
				self.panels.passionRace:updatePageTabList()
			end
		else
			if not g_data.passionRace:PRIconShown() then
				self:tip("活动已过期！")

				if self.panels.passionRace then
					self:togglePanel("passionRace")
				end

				return
			elseif not self.panels.passionRace then
				self:togglePanel("passionRace")
			end

			if self.panels.passionRace and self.panels.passionRace._selectedActId ~= result.FActId then
				print(" ============>>>> 切换页签 ")
				self.panels.passionRace:selectActivityId(result.FActId)

				local rsb = DefaultClientMessage(CM_New_Act_Request)

				rsb.FActType = ActivityType.ACT_PASSIONRACE
				rsb.FActId = result.FActId
				rsb.FButtonId = 0
				rsb.FExtraStr = ""

				MirTcpClient:getInstance():postRsb(rsb)
				main_scene.ui.waiting:show(10, "CM_NEW_ACT_REQUEST")
			else
				local viewName = g_data.passionRace:getActNameById(result.FActId)

				if result.FButtonId == 0 then
					local temp = quickJson.decode(result.FContent)

					if not temp then
						if self.panels.passionRace then
							self:togglePanel("passionRace")
						end

						g_data.passionRace:clearResult()

						local rsb = DefaultClientMessage(CM_New_Act_Request)

						rsb.FActType = ActivityType.ACT_PASSIONRACE
						rsb.FActId = ActivityType.ACT_PASSIONRACE_ACTID
						rsb.FButtonId = 0
						rsb.FExtraStr = ""

						MirTcpClient:getInstance():postRsb(rsb)
						main_scene.ui.waiting:show(10, "CM_NEW_ACT_REQUEST")
						main_scene.ui:togglePanel("passionRace")

						return
					end

					if self.panels.passionRace then
						self.panels.passionRace:refreshActivityDetailView(temp.Act, viewName, temp.extraData)
					end
				else
					if not self.panels.passionRace then
						return
					end

					local _selectedPageView = self.panels.passionRace._selectedPageView

					if _selectedPageView then
						local pageName = _selectedPageView:getName()

						if pageName == viewName then
							if result.FExtraStr == "" then
								if main_scene.ui.panels.passionRace then
									main_scene.ui:togglePanel("passionRace")
								end

								g_data.passionRace:clearResult()

								local rsb = DefaultClientMessage(CM_New_Act_Request)

								rsb.FActType = ActivityType.ACT_PASSIONRACE
								rsb.FActId = ActivityType.ACT_PASSIONRACE_ACTID
								rsb.FButtonId = 0
								rsb.FExtraStr = ""

								MirTcpClient:getInstance():postRsb(rsb)
								main_scene.ui.waiting:show(10, "CM_NEW_ACT_REQUEST")
								main_scene.ui:togglePanel("passionRace")

								return
							elseif result.FButtonId == 9999 then
								local temp = json.decode(result.FContent)
								local str = temp.Act[1].Question

								if str and str ~= "" then
									local strListTemp = string.split(str, "|")
									local strList = {}

									for i, v in ipairs(strListTemp) do
										table.insert(strList, {
											v .. "\n"
										})
									end

									an.newMsgbox(strList, nil, {
										contentLabelSize = 20
									})
								end
							else
								_selectedPageView:btnCb(result)
							end
						else
							print(" ============>>>>> 当前选中的 pageview 跟之前加载的不是同一个 view？", pageName, viewName)

							return
						end
					else
						print(" ============>>>>> 没找到当前选中的 pageview？")

						return
					end
				end
			end
		end
	elseif ActivityType.ACT_GUILDTASK == result.FActType then
		if not self.panels.guild then
			return
		else
			self.panels.guild:onACTCallback(result)
		end
	elseif ActivityType.ACT_RINGPROMISE == result.FActType then
		self:frefreshRingPromiseRed(result)

		if not self.panels.ringPromise then
			return
		else
			self.panels.ringPromise:handlePanelEvent(result)
		end
	elseif ActivityType.ACT_WEEKPRIVILEGE == result.FActType then
		if not self.panels.weekPrivilege then
			return
		else
			self.panels.weekPrivilege:handlePanelEvent(result)
		end
	elseif ActivityType.ACT_MONTHACTIVITY == result.FActType then
		if ActivityType.ACT_YueDuKuangHuanZhou == result.FActId then
			local data = json.decode(result.FContent)
			local firstTabList = g_data.monthActivityModule:initActivityTabList(data)

			if self.panels.monthActivityView then
				local firstTabList = g_data.monthActivityModule:getActivityFirstTabList()

				self.panels.monthActivityView:refreshFirstTabList(firstTabList)
			end
		elseif ActivityType.ACT_ChuanQiBaoXian == result.FActId then
			if self.panels.legendBoxView then
				if result.FButtonId ~= 0 then
					if result.FExtraStr == "" then
						self.panels.legendBoxView.isBuying = false

						return
					else
						local awardResult = json.decode(result.FExtraStr)

						self.panels.legendBoxView:buyResult(awardResult)
					end
				end

				local data = json.decode(result.FContent)

				g_data.monthActivityModule:setLegendBoxPreviewAward(data.extraData.AllAward)
				self.panels.legendBoxView:loadRecordData(data.Notice)
			end
		elseif ActivityType.ACT_ZhaJinDan == result.FActId then
			if not self.panels.goldEgg then
				return
			else
				g_data.goldEgg:handleData(result)
				self.panels.goldEgg:refreshPanel()
			end
		elseif ActivityType.ACT_MeiRiYingXiongLing == result.FActId then
			self.panels.monthActivityView:requestOperateInfo(10, 10000000, 0)
		end
	elseif ActivityType.ACT_NEWMAGICBOX == result.FActType then
		local data = json.decode(result.FContent)
		local extraStr = result.FExtraStr

		if self.panels.newMagicBoxView then
			if extraStr ~= "" and tonumber(extraStr) > 0 then
				self.panels.newMagicBoxView:openBoxSucceed()
			end

			self.panels.newMagicBoxView:refreshUI(data)
		end
	end
end

function mainui:onSM_Senddmlist(result, protoId)
	if not result then
		return
	end

	g_data.newActivity:setDMList(result.Fdmlist)
	g_data.newActivity:setDMCount(result.FCanUseTimes_Low, result.FCanUseTimes_High)
	g_data.eventDispatcher:dispatch("M_CURTAINNUM_CHG")
end

function mainui:onSM_SendDMbroad(result, protoId)
	if not result then
		return
	end

	local dmStr = table.concat({
		result.FName,
		"：",
		result.FText
	})
	local curtainViewTemp = main_scene.curtainView

	if not curtainViewTemp or tolua.isnull(curtainViewTemp) then
		print(" ==============>>>> 飘屏弹幕层不存在 ")

		return
	end

	curtainViewTemp:insertCurtain(dmStr, result.FColorID)
end

function mainui:onSM_QueryLoginActInfo(result, protoId)
	if not result then
		return
	end

	g_data.signWeekly:updateByNetData(result, protoId)

	if g_data.signWeekly:needAutoShow() then
		self:hidePanel("signWeeklyPanel")
		self:showPanel("signWeeklyPanel")
	end
end

function mainui:onSM_RedRainACtion(result, _)
	if result then
		local RedPacketRain = require("mir2.scenes.main.panel.newActivityViews.redPacketRain")

		RedPacketRain.raining(result)
	end
end

function mainui:onSM_GetRedRain(result, _)
	main_scene.ui.waiting:close("CM_GetRedRain")

	if result and result.Fok == 2 then
		local RedPacketRain = require("mir2.scenes.main.panel.newActivityViews.redPacketRain")
		local alert = RedPacketRain.new(result)

		alert:pos(display.cx, display.cy):addTo(self, 9999)
	end
end

function mainui:onSM_SendBoxMonster(result)
	if IS_PLAYER_DEBUG then
		package.loaded["mir2.scenes.main.panel.airborneBox"] = nil

		require("mir2.scenes.main.panel.airborneBox")
	end

	if result and not main_scene.ui.panels.airborneBox then
		main_scene.ui:togglePanel("airborneBox", result)
	end
end

function mainui:onSM_RedPStatus(result, protoId)
	if result and result.FRedPStatus then
		self.newActivityRedLists = {}

		local strs = string.split(result.FRedPStatus, "/")

		if strs and #strs > 0 then
			for i = 1, #strs do
				local tmps = string.split(strs[i], ":")
				local id = tonumber(tmps[1])

				if id then
					self.newActivityRedLists[id] = {
						id = id,
						show = tonumber(tmps[2]) == 1 and true or false
					}
				end
			end

			local act = self.panels.newActivity

			if act then
				act:refreshRed()
			end

			local passionRace = self.panels.passionRace

			if passionRace then
				passionRace:refreshRed()
			end
		end
	end
end

function mainui:handleDayOrServerLVChange(dayChange, serverLvChange)
	if dayChange then
		-- block empty
	end

	if serverLvChange then
		-- block empty
	end

	if self.panels.newActivity then
		self.panels.newActivity:dayOrServerLVChange()
	end

	if self.panels.passionRace then
		self.panels.passionRace:dayOrServerLVChange()
	end
end

function mainui:onSM_GetMoneyIcon(result, protoId)
	if not result then
		return
	end

	main_scene.ui.earnOnlineOpen = true

	local code = main_scene.ui.earnOnlineOpen and 1 or 0
	local chkOpen = {
		{
			name = "网赚",
			funcId = gameFunc.EARNONLINE,
			info = {
				code
			}
		}
	}

	main_scene.ui.console:checkOpen(chkOpen)
end

function mainui:onSM_WMSecNotice(result, protoId)
	if not result then
		return
	end

	g_data.eventDispatcher:dispatch("ACTIVITY_BRAVE_TEXT", result.FSec, result.FNeedSec, result.Fflags == 1)
end

function mainui:onSM_QueryAllPalInfo(result, protoId)
	if not result then
		return
	end

	g_data.pal:updateByNetData(result, protoId)
	g_data.eventDispatcher:dispatch("PAL_LIST_CHG")

	local palData = g_data.pal:getPalUsing()

	if palData then
		g_data.pal.requestPalEquips(palData.index)
	end
end

function mainui:onSM_PalFetterQuery(result, protoId)
	main_scene.ui.waiting:close(KEY_LOADING)

	if not result then
		return
	end

	g_data.pal:updateByNetData(result, protoId)
end

function mainui:onSM_CallorBackPal(result, protoId)
	main_scene.ui.console.controller.lock:stop()
end

function mainui:onSM_QueryPalAbility(result, protoId)
	if not result then
		return
	end

	g_data.pal:updateByNetData(result, protoId)
	g_data.eventDispatcher:dispatch("PAL_ABILITY_CHG")
end

function mainui:onSM_QueryPalQA(result, protoId)
	if not result then
		return
	end

	g_data.pal:updateByNetData(result, protoId)
	g_data.eventDispatcher:dispatch("PAL_QA_CHG")
end

function mainui:onCliQueryPalDisPatch(result, protoId)
	if not result then
		return
	end

	g_data.pal:updateByNetData(result, protoId)
	g_data.eventDispatcher:dispatch("PAL_DESPATCH_CHG")
end

function mainui:onSM_CliPalDisPatch(result)
	if not result then
		return
	end

	if result.FRetValue == 0 then
		g_data.eventDispatcher:dispatch("PAL_DESPATCH_CHG")
	elseif result.FRetValue == -1 then
		main_scene.ui:tip("派遣任务已达上限")
	elseif result.FRetValue == -2 then
		main_scene.ui:tip("无效地图等级")
	elseif result.FRetValue == -3 then
		main_scene.ui:tip("赠送伙伴派遣信息未找到")
	elseif result.FRetValue == -4 then
		main_scene.ui:tip("赠送伙伴派遣次数不足")
	elseif result.FRetValue == -5 then
		main_scene.ui:tip("赠送伙伴派遣只能去初级地图")
	elseif result.FRetValue == -6 then
		main_scene.ui:tip("玩家没有该伙伴")
	elseif result.FRetValue == -7 then
		main_scene.ui:tip("伙伴品质配置未找到")
	elseif result.FRetValue == -8 then
		main_scene.ui:tip("伙伴只能去不高于自己品质的地图")
	elseif result.FRetValue == -9 then
		main_scene.ui:tip("玩家派遣信息未找到")
	elseif result.FRetValue == -10 then
		main_scene.ui:tip("上一次派遣未结束")
	elseif result.FRetValue == -11 then
		main_scene.ui:tip("上一次派遣未领取奖励")
	elseif result.FRetValue == -12 then
		main_scene.ui:tip("该伙伴正在出战")
	end
end

function mainui:onSM_CliGetPalDisPatch(result)
	if not result then
		return
	end

	if result.FRetValue == 0 then
		g_data.eventDispatcher:dispatch("PAL_DESPATCH_CHG")
	elseif result.FRetValue == -1 then
		main_scene.ui:tip("未找到该战宠派遣信息")
	elseif result.FRetValue == -2 then
		main_scene.ui:tip("该战宠派遣未完成")
	elseif result.FRetValue == -3 then
		main_scene.ui:tip("没有可领取奖励")
	elseif result.FRetValue == -4 then
		main_scene.ui:tip("无效地图等级")
	elseif result.FRetValue == -5 then
		main_scene.ui:tip("玩家没有该伙伴")
	elseif result.FRetValue == -6 then
		main_scene.ui:tip("伙伴品质配置未找到")
	elseif result.FRetValue == -7 then
		main_scene.ui:tip("伙伴品质配置未找到")
	elseif result.FRetValue == -8 then
		main_scene.ui:tip("获取奖励错误")
	end
end

function mainui:onCC_KEYPAD_EVENT(event)
	if DEBUG > 0 and device.platform == "windows" and IS_PC_SIMUALTOR then
		if event.code == 141 then
			local msg = g_data.chat:getSendMsgHistory()

			if msg then
				common.say(msg)
			end
		elseif event.code == 140 then
			common.gotoLogin(true)
		end
	end
end

function mainui:onSM_QueryPalEquips(result, protoId)
	if not result then
		return
	end

	g_data.pal:updateByNetData(result, protoId)
	g_data.eventDispatcher:dispatch("PAL_EQUIP_CHG", {
		palIndex = result.FPalIndex
	})
end

function mainui:onSM_TakeOnPalEquip(result, protoId)
	if not result then
		return
	end

	if result.Flag < 0 then
		local str

		if result.Flag == -1 then
			str = "该装备无法穿戴"
		elseif result.Flag == -2 then
			str = "穿戴位置不正确"
		elseif result.Flag == -6 then
			str = "装备条件不足"
		elseif result.Flag == -16 then
			str = "未知战宠或战宠不存在"
		elseif result.Flag == -17 then
			str = "伙伴处于召唤中"
		end

		if str then
			self:tip(str)
		end
	else
		g_data.pal.requestPalEquips(result.FPalIndex)
	end
end

function mainui:onSM_TakeOffPalEquip(result, protoId)
	if not result then
		return
	end

	if result.Flag <= 0 then
		local str

		if result.Flag == 0 then
			str = "脱下装备失败"
		elseif result.Flag == -3 then
			str = "背包空间不足"
		elseif result.Flag == -6 then
			str = "装备条件不足"
		elseif result.Flag == -16 then
			str = "未知战宠或战宠不存在"
		elseif result.Flag == -17 then
			str = "伙伴处于召唤中"
		end

		if str then
			self:tip(str)
		end
	elseif result.Flag == 1 then
		g_data.pal.requestPalEquips(result.FPalIndex)
	end
end

function mainui:onSM_RingLeechEffect(result)
	if main_scene.ground.player then
		main_scene.ground.player.info:showXX(result.FEffectType, result.FLeechNum)
	end
end

function mainui:onSM_GetMoneyQueryMain(result, protoId)
	main_scene.ui.waiting:close(KEY_LOADING)

	if not result then
		return
	end

	g_data.earnOnline:updateByNetData(result, protoId)

	if result.Fstatus == 0 or result.Fstatus == 1 then
		main_scene.ui:showPanel("earnOnlineRedPack", {
			type = 1
		})
	elseif result.Fstatus == 2 then
		main_scene.ui:showPanel("earnOnlineRedPack", {
			type = 2
		})
	elseif result.Fstatus == 3 then
		main_scene.ui:showPanel("earnOnline")
	end
end

function mainui:onSM_QueryLimitGroupBuy(result)
	if not result then
		return
	end

	g_data.groupBuy:parseResult(result)
end

function mainui:onSM_JoinGroupBuy(result)
	local retValue = result.FretValue
	local str = ""

	if retValue == 1 then
		str = "参团成功！"

		g_data.groupBuy:buySuccess(result.FRetID)
	elseif retValue == -2 then
		str = "参团人数已达上限！"
	elseif retValue == -3 then
		str = "元宝不足！"
	elseif retValue == -4 then
		str = "不可重复参与！"
	elseif retValue == -5 then
		str = "本期活动已结束！"

		local curTime = g_data.serverTime:getTime() or socket.gettime()
		local curTimeStr = os.date("%Y-%m-%d-%H-%M-%S", curTime)
		local curTimeList = string.split(curTimeStr, "-")
		local curHour = tonumber(curTimeList[4])

		if curHour <= 0 then
			str = "本期活动未开启！"
		end
	end

	self:tip(str)
end

function mainui:onSM_QueryCreateDay(result)
	if not result then
		return
	end

	g_data.player.createDay = result.FCreateDay

	if g_data.player.createDay > 30 then
		self.console:removeWidget("btnGrowthRoad")
		self:checkFirstGrowthRoad()
	end
end

function mainui:checkFirstGrowthRoad()
	local btn = self.console:get("btnGrowthRoad")

	if g_data.player.createDay <= 30 and not btn and not g_data.firstOpen:get("btnGrowthRoad") then
		g_data.badgeSystem:set(badge.B_GROWTH_ROAD, true)
	else
		if not g_data.firstOpen:get("btnGrowthRoad") then
			g_data.firstOpen:set("btnGrowthRoad", true)
		end

		g_data.badgeSystem:set(badge.B_GROWTH_ROAD, false)
	end
end

function mainui:onSM_QueryAttSkyInfo(result)
	if not result or main_scene.ui.panels and main_scene.ui.panels.attSkyUse then
		-- block empty
	else
		main_scene.ui:togglePanel("attSkyUse", result)
	end
end

function mainui:onSM_UseAttSky(result)
	if result then
		if IS_PLAYER_DEBUG then
			package.loaded["mir2.scenes.main.panel.attSkyMsg"] = nil
		end

		local AttSkyMsg = require("mir2.scenes.main.panel.attSkyMsg")

		AttSkyMsg.new(result)
	end
end

function mainui:onSM_QueryGodItemInfo(result)
	if result and self.__godItemFlag then
		self.__godItemFlag = false

		if g_data.player.crossServerState > 0 then
			g_data.godItem:setIsOpen(true)
		end

		g_data.godItem:setCurData(result)
		self:checkGodGridRedPoint()
	end
end

function mainui:handleCrossServer()
	if g_data.player.crossServerState > 0 then
		self:queryGodItemInfo()
		self:showAirborneSoliderIcon(false)
	end
end

function mainui:onSM_GildMonsterCmdRes(result)
	if result and result.Fid == 3 then
		if result.FRetCode == -4 then
			main_scene.ui.gildMonsterOpen = true
		else
			main_scene.ui.gildMonsterOpen = false
		end

		local code = main_scene.ui.gildMonsterOpen and 1 or 0
		local chkOpen = {
			{
				name = "镖车",
				funcId = gameFunc.ESCORT,
				info = {
					code
				}
			}
		}

		main_scene.ui.console:checkOpen(chkOpen)
	end
end

function mainui:onSM_ChallengeResult(result)
	if result.FResult == 1 then
		main_scene.ui:tip("挑战成功!")
	elseif result.FResult == 2 then
		main_scene.ui:tip("挑战失败!")
	elseif result.FResult == 3 then
		main_scene.ui:tip("背包不足!")
	elseif result.FResult == 4 then
		main_scene.ui:tip("首通成功!")
	end
end

function mainui:frefreshRingPromiseRed(data)
	local content = json.decode(data.FContent)

	content = content and (content.Act and content.Act[1] or nil)

	local btn = self.console:get("btnRingPromise")

	if not content and (not data.FExtraStr or data.FExtraStr == "") then
		if btn then
			self.console:removeWidget("btnRingPromise")
		end
	elseif content and next(content) then
		if not btn then
			self.console:addWidget({
				yori = 2,
				key = "btnRingPromise",
				xFix = -1,
				y = 510,
				xori = 2,
				x = 430
			})

			btn = self.console:get("btnRingPromise")
		end

		local red = false

		if not g_data.firstOpen:get("btnRingPromise") then
			red = true

			g_data.firstOpen:set("btnRingPromise", true)
		end

		local info = string.split(content.sLimitYB, ",")

		for i, v in ipairs(info) do
			if content.iCostYB >= tonumber(v) and i > content.iStatus then
				red = true

				break
			end
		end

		if btn and not tolua.isnull(btn) then
			if red and not btn:getChildByName("node_red") then
				local rad = math.rad(45)
				local len = math.sin(rad) * (btn:getw() / 2)

				display.newSprite(res.gettex2("pic/panels/mail/newMailTip.png")):anchor(0, 0):add2(btn):pos(btn:getw() / 2 + len, btn:geth() / 2 + len):setName("node_red")
			end

			if btn:getChildByName("node_red") then
				btn:getChildByName("node_red"):setVisible(red)
			end
		end
	end
end

function mainui:onSM_QueryHonourTitle(result)
	if result then
		g_data.player:setTitlesInfo(result)
	end
end

function mainui:onSM_QueryAspectsInfo(result)
	if result then
		g_data.godBody:setFaXiangData(result.FAspectsInfoList)
	end
end

function mainui:onSM_Horse_SkillUpLevel(result)
	local failMsg = {
		"升级成功",
		[-2] = "服务器阶段不足",
		[-106] = "材料不足",
		[-4] = "开服天数不足",
		[-3] = "玩家等级不足",
		[-105] = "金币不足"
	}

	if result and result.FretValue then
		if result.FretValue == 100 then
			g_data.horse:setQiShuData(result)
		elseif result.FretValue == 1 then
			g_data.horse:setQiShuData(result)
			g_data.eventDispatcher:dispatch("QISHU_CHANGE", result)
		end

		if failMsg[result.FretValue] then
			main_scene.ui:tip(failMsg[result.FretValue])
		end
	end
end

function mainui:onSM_HorseBaseSkillLV(result)
	if result then
		g_data.horse:setHorseSkillData(result)
	end
end

function mainui:onSM_NormalStrKv(result)
	if result then
		g_data.eventDispatcher:dispatch("SERVER_KV_CHANGED", result)
	end
end

function mainui:onSM_NormalStrKvList(result)
	if result then
		g_data.eventDispatcher:dispatch("SERVER_KVLIST_CHANGED", result)
	end
end

function mainui:onSM_TianLeiInfo(result)
	if result then
		g_data.thunderStrike:updateThunderData(result)
	end
end

function mainui:handleServerKVChanged(result)
	if result.FKey == "Gild_Score" then
		if not self.gildScoreLabel or tolua.isnull(self.gildScoreLabel) then
			self.gildScoreLabel = an.newLabel("战团积分:" .. result.FValue, 16, 1, {
				color = display.COLOR_RED
			}):add2(self, 100):anchor(0, 0):pos(315, 260)
		else
			self.gildScoreLabel:setString("战团积分:" .. result.FValue)
		end
	elseif result.FKey == "Gild_BossScore" then
		if not self.gildBossScoreLabel or tolua.isnull(self.gildBossScoreLabel) then
			self.gildBossScoreLabel = an.newLabel("击杀boss:" .. result.FValue, 16, 1, {
				color = display.COLOR_RED
			}):add2(self, 100):anchor(0, 0):pos(315, 235)
		else
			self.gildBossScoreLabel:setString("击杀boss:" .. result.FValue)
		end
	elseif result.FKey == "七怖天清理血条和战团伤害显示" and result.FValue == 1 then
		self:clearGildInfos()
	end
end

function mainui:handleServerKVListChanged(result)
	if result.FMKey == "Score" then
		for i = 1, 3 do
			if result.FKvList[i] then
				if not self.gildRankLabel then
					self.gildRankLabel = {}
				end

				if not self.gildRankLabel[i] or tolua.isnull(self.gildRankLabel[i]) then
					self.gildRankLabel[i] = an.newLabel("" .. i .. " . " .. result.FKvList[i].FKey .. "   " .. result.FKvList[i].FValue, 16, 1, {
						color = display.COLOR_RED
					}):add2(self, 100):anchor(0, 0):pos(315, 235 - 25 * i)
				else
					self.gildRankLabel[i]:setString("" .. i .. " . " .. result.FKvList[i].FKey .. "   " .. result.FKvList[i].FValue)
				end
			else
				if not self.gildRankLabel then
					self.gildRankLabel = {}
				end

				if self.gildRankLabel[i] and not tolua.isnull(self.gildRankLabel[i]) then
					self.gildRankLabel[i]:setString("")
				end
			end
		end
	elseif result.FMKey == "七怖天活动伤害第一战团" then
		if not self.gildBossFirstNameLabel or tolua.isnull(self.gildBossFirstNameLabel) then
			self.gildBossFirstNameLabel = an.newLabel("伤害第一战团:" .. result.FKvList[1].FKey, 16, 1):add2(self, 100):anchor(0, 0):pos(410, 470)
		else
			self.gildBossFirstNameLabel:setString("伤害第一战团:" .. result.FKvList[1].FKey)
		end
	elseif result.FMKey == "七怖天活动杀怪数量" then
		-- block empty
	end
end

function mainui:onSM_ChangeUnrealFeture(result)
	if result then
		g_data.player.isShowUnrealFeture = result.FBoUseUnreal

		g_data.eventDispatcher:dispatch("UNREAL_FETURE_CHANGED", result)
	end
end

function mainui:onSM_SCSetSkill(result)
	if result then
		local rsb = DefaultClientMessage(CM_SCQueryInfo)

		MirTcpClient:getInstance():postRsb(rsb)
	end
end

function mainui:onSM_SCQueryInfo(result)
	if result and result.FList then
		g_data.combo:setQueryInfo(result.FList)
	end
end

function mainui:onSM_sendUnrealBannerInfo(result)
	if g_data.unrealBanner:isOpen() and not g_data.unrealBanner:isInitialized() and result and result.FBannerList then
		g_data.unrealBanner:alloc()
		g_data.unrealBanner:update(result.FBannerList)
	end
end

function mainui:onSM_AnimalHpChange(result)
	self:updateBossHP(result)
end

function mainui:updateBossHP(params)
	local bar = self.bossHPBar[params.FSelfName]

	if tonumber(params.FHp) <= 0 then
		if bar then
			bar:removeSelf()

			self.bossHPBar[params.FSelfName] = nil
		end

		return
	end

	if not bar or tolua.isnull(bar) then
		bar = self:createBossHPBar(params)
		self.bossHPBar[params.FSelfName] = bar
	end

	local hpbar = bar:getChildByName("hpBar")
	local hpbg = bar:getChildByName("hpbg")
	local hpstr = bar:getChildByName("hpstr")
	local percent = tonumber(params.FHp) / tonumber(params.FMaxHP)
	local len = params.FWXType == 0 and 393 or 188

	hpbar:setTextureRect(cc.rect(0, 0, len * percent, hpbar:geth()))

	local percentStr = math.floor(percent * 100)

	hpstr:setString(percentStr .. "%")
end

function mainui:createBossHPBar(params)
	local hpbg, hpbar
	local bossHead = {
		魔军都统 = "mjdt",
		七怖天 = "qbt",
		魔军先锋 = "mjxf"
	}

	if params.FWXType == 0 then
		hpbg = res.get2("pic/panels/bosshp/hp_bg2.png"):anchor(0.5, 1):addTo(self):pos(560, 570)

		hpbg:setName("hpbg")

		local hpbar = display.newSprite(res.gettex2("pic/panels/bosshp/hp2.png")):anchor(0, 1):pos(97, 48):addto(hpbg)

		hpbar:setName("hpBar")

		local hpstr = an.newLabel("", 18, 1):anchor(0.5, 0.5):add2(hpbg):pos(293, 44)

		hpstr:setName("hpstr")
		res.get2("pic/panels/bosshp/" .. bossHead[params.FSelfName] .. ".png"):anchor(0.5, 0.5):addTo(hpbg):pos(47, 40)
	else
		local wxnum = 0

		for k, v in pairs(self.bossHPBar) do
			if k ~= "七怖天" then
				wxnum = wxnum + 1
			end
		end

		hpbg = res.get2("pic/panels/bosshp/hp_bg1.png"):anchor(0.5, 0.5):addTo(self):pos(180, 480 - wxnum * 70)

		hpbg:setName("hpbg")

		local hpbar = display.newSprite(res.gettex2("pic/panels/bosshp/hp1.png")):anchor(0, 1):pos(70, 31):addto(hpbg)

		hpbar:setName("hpBar")

		local hpstr = an.newLabel("", 18, 1):anchor(0.5, 0.5):add2(hpbg):pos(164, 25)

		hpstr:setName("hpstr")
		res.get2("pic/panels/bosshp/" .. bossHead[params.FSelfName] .. ".png"):anchor(0.5, 0.5):scale(0.9):addTo(hpbg):pos(31, 30)

		local wxName = {
			"jin",
			"mu",
			"shui",
			"huo",
			"tu"
		}
		local frameEffectPath = "animation/ui_wuxing/ui_wuxing.csb"

		ccs.ArmatureDataManager:getInstance():addArmatureFileInfo(frameEffectPath)

		local wxEffect = ccs.Armature:create("ui_wuxing")

		wxEffect:setTouchEnabled(false)
		wxEffect:setTouchSwallowEnabled(false)
		wxEffect:add2(hpbg, 2):anchor(0.5, 0.5)
		wxEffect:getAnimation():play(wxName[params.FWXType])
		wxEffect:size(0, 0)

		wxEffect.isAni = true

		wxEffect:setName("wxEffect")
		wxEffect:pos(31, 30)
	end

	return hpbg
end

function mainui:onSM_SwitchWuXingType(result)
	if result and result.FRet >= 1 and result.FRet <= 5 then
		g_data.wuxing:setSwitchWuxingType(result.FRet)

		local btn = main_scene.ui.console:get("btnWuxing")

		if not btn then
			g_data.eventDispatcher:dispatch("WUXING_SWITCH_TYPE_CHANGED", result)

			return
		end

		local iconPath = g_data.wuxing:getIconSpritePath()
		local tx = res.gettex2(iconPath)

		btn.btn.sprite:setTexture(tx)
		btn:changeProgressTimerTexture(g_data.wuxing:getIconSpritePath(true))
	end

	g_data.eventDispatcher:dispatch("WUXING_SWITCH_TYPE_CHANGED", result)
end

function mainui:onMapChange()
	self:clearGildInfos()
end

function mainui:clearGildInfos()
	for i, v in ipairs(self.bossHPBar) do
		v:removeSelf()
	end

	self.bossHPBar = {}

	if self.gildRankLabel then
		for i, v in ipairs(self.gildRankLabel) do
			v:removeSelf()
		end

		self.gildRankLabel = nil
	end

	if self.gildBossFirstNameLabel and not tolua.isnull(self.gildBossFirstNameLabel) then
		self.gildBossFirstNameLabel:removeSelf()

		self.gildBossFirstNameLabel = nil
	end
end

function mainui:onSM_InitStruct(result)
	if result.SM_PLevelNpcSendIcon_leftsec ~= nil and result.SM_PLevelNpcSendIcon_contleft ~= nil then
		self:onSM_PLevelNpcSendIcon({
			Fleftsec = result.SM_PLevelNpcSendIcon_leftsec,
			Fcontleft = result.SM_PLevelNpcSendIcon_contleft
		})
	end

	if result.SM_SendFlag_1 ~= nil then
		g_data.moh:setActive(result.SM_SendFlag_1 == 1)
	end

	if result.SM_SendFlag_2 ~= nil then
		g_data.moh:setCanHangUp(result.SM_SendFlag_2 > 0)
	end

	if result.SM_SendFlag_3 ~= nil then
		if result.SM_SendFlag_3 == 100 then
			g_data.godItem:setIsOpen(true)
			self:queryGodItemInfo()
		else
			g_data.godItem:setIsOpen(false)
		end

		if not g_data.firstOpen:get("godItem") then
			self:checkFirstGodItem()
		end
	end

	if result.SM_SendFlag_4 ~= nil then
		self.__starOpen = result.SM_SendFlag_4 == 100

		if not g_data.firstOpen:get("starChart") then
			self:checkFirstStarChart()
		end
	end

	if result.SM_SendFlag_5 ~= nil then
		self.__godSwordOpen = result.SM_SendFlag_5 == 100
	end

	if result.SM_SendFlag_6 ~= nil then
		self.__meridianOpen = result.SM_SendFlag_6 == 100

		if not g_data.firstOpen:get("meridian") then
			self:checkFirstMeridian()
		end
	end

	if result.SM_SendFlag_7 ~= nil then
		self.__birthSignOpen = result.SM_SendFlag_7 == 100

		if not g_data.firstOpen:get("birthsign") then
			self:checkFirstBirthSign()
		end
	end

	if result.SM_SendFlag_8 ~= nil and result.SM_SendFlag_8 == 100 then
		g_data.unrealBanner:setIsOpen(true)

		local rsb = DefaultClientMessage(CM_QueryUnrealBanner)

		MirTcpClient:getInstance():postRsb(rsb)
	end

	if result.SM_SendFlag_9 ~= nil then
		self.__wuxingOpen = result.SM_SendFlag_9 == 100

		if not g_data.firstOpen:get("wuxing") then
			self:checkFirstWuxing()
		end
	end

	if result.SM_SendFlag_11 ~= nil then
		self.__penDantOpen = result.SM_SendFlag_11 == 100

		if not g_data.firstOpen:get("pendantPanel") then
			self:checkFirstPenDant()
		end
	end

	if result.SM_SendFlag_12 ~= nil then
		g_data.crossServerScoreShop:setActive(result.SM_SendFlag_12 == 1)
	end

	if result.SM_TowelInfo_TowelLv ~= nil and result.SM_TowelInfo_TowelVaryLv ~= nil and result.SM_TowelInfo_TowelStepLv ~= nil then
		self:onSM_TowelLv({
			FTowelLv = result.SM_TowelInfo_TowelLv,
			FTowelVaryLv = result.SM_TowelInfo_TowelVaryLv,
			FTowelStepLv = result.SM_TowelInfo_TowelStepLv
		})
	end

	if result.SM_SwitchWuXingType_Ret ~= nil then
		self:onSM_SwitchWuXingType({
			FRet = result.SM_SwitchWuXingType_Ret
		})
	end

	if result.SM_RideState_RideState ~= nil and (result.SM_RideState_RideState ~= 1 or true) then
		self:onSM_RideState({
			FRideState = result.SM_RideState_RideState
		})
	end

	if result.SM_UserRegDate_RegDate ~= nil then
		self:onSM_UserRegDate({
			FRegDate = result.SM_UserRegDate_RegDate
		})
	end

	if result.SM_BoReviveRingOk_CanRevive ~= nil or result.SM_BoReviveRingOk_CanBeRedemption ~= nil then
		self:onSM_BoReviveRingOk({
			FCanRevive = result.SM_BoReviveRingOk_CanRevive,
			FCanBeRedemption = result.SM_BoReviveRingOk_CanBeRedemption
		})
	end

	if result.SM_ReviveProtect_BoReviveProtect ~= nil or result.SM_ReviveProtect_BoRedemptionProtect ~= nil then
		self:onSM_ReviveProtect({
			FBoReviveProtect = result.SM_ReviveProtect_BoReviveProtect,
			FBoRedemptionProtect = result.SM_ReviveProtect_BoRedemptionProtect
		})
	end

	if result.SM_DrumLevel_DrumLevel ~= nil and result.SM_DrumLevel_DrumUnSealLv ~= nil and result.SM_DrumLevel_DrumUpgradeLevel ~= nil then
		self:onSM_DrumLevel({
			FDrumLevel = result.SM_DrumLevel_DrumLevel,
			FDrumUnSealLv = result.SM_DrumLevel_DrumUnSealLv,
			FDrumUpgradeLevel = result.SM_DrumLevel_DrumUpgradeLevel
		})
	end

	if result.SM_MonCrystalLv_MonCrystalLv ~= nil then
		self:onSM_MonCrystalLv({
			FMonCrystalLv = result.SM_MonCrystalLv_MonCrystalLv
		})
	end

	if result.SM_QuaryMonSoulInfo_MonSoulLevel ~= nil and result.SM_QuaryMonSoulInfo_MonSoulHaveStuff then
		self:onSM_QuaryMonSoulInfo({
			FMonSoulLevel = result.SM_QuaryMonSoulInfo_MonSoulLevel,
			FMonSoulHaveStuff = result.SM_QuaryMonSoulInfo_MonSoulHaveStuff
		})
	end

	if result.SM_SeverLevel_ServerStep ~= nil then
		self:onSM_SeverLevel({
			FServerStep = result.SM_SeverLevel_ServerStep
		})
	end

	if result.SM_PlayerGildInfo_GildName ~= nil and result.SM_PlayerGildInfo_Position then
		self:onSM_PlayerGildInfo({
			FGildName = result.SM_PlayerGildInfo_GildName,
			FPosition = result.SM_PlayerGildInfo_Position
		})
	end

	if result.SM_NOREAD_MAIL_COUNT_Count ~= nil then
		self:onSM_NOREAD_MAIL_COUNT({
			Count = result.SM_NOREAD_MAIL_COUNT_Count
		})
	end

	if result.SM_CanGet_CashGift_COUNT_Count ~= nil then
		self:onSM_CanGet_CashGift_COUNT({
			FCount = result.SM_CanGet_CashGift_COUNT_Count
		})
	end

	if result.SM_ATTACKMODE_AttackMode ~= nil then
		self:onSM_ATTACKMODE({
			AttackMode = result.SM_ATTACKMODE_AttackMode
		})
	end

	if result.SM_MilitaryRankLv_MilitaryRankLv ~= nil then
		self:onSM_MilitaryRankLv({
			FMilitaryRankLv = result.SM_MilitaryRankLv_MilitaryRankLv
		})
	end

	if result.SM_HorseBaseSkillLV_SkillLevel_2 ~= nil then
		g_data.horse:setHorseSkillData({
			FSkillType = 2,
			FSkillLevel = result.SM_HorseBaseSkillLV_SkillLevel_2
		})
	end

	if result.SM_HorseBaseSkillLV_SkillLevel_3 ~= nil then
		g_data.horse:setHorseSkillData({
			FSkillType = 3,
			FSkillLevel = result.SM_HorseBaseSkillLV_SkillLevel_3
		})
	end

	if result.SM_HorseBaseSkillLV_SkillLevel_4 ~= nil then
		g_data.horse:setHorseSkillData({
			FSkillType = 4,
			FSkillLevel = result.SM_HorseBaseSkillLV_SkillLevel_4
		})
	end

	if result.SM_HorseBaseSkillLV_SkillLevel_5 ~= nil then
		g_data.horse:setHorseSkillData({
			FSkillType = 5,
			FSkillLevel = result.SM_HorseBaseSkillLV_SkillLevel_5
		})
	end

	if result.SM_ChangeUnrealFeture_BoUseUnreal ~= nil then
		self:onSM_ChangeUnrealFeture({
			FBoUseUnreal = result.SM_ChangeUnrealFeture_BoUseUnreal
		})
	end

	if result.SM_SendFlag_BaguaOpen ~= nil then
		self.__baguaOpen = result.SM_SendFlag_BaguaOpen == 100

		if self.__baguaOpen and not g_data.firstOpen:get("baguaUpgrade") then
			g_data.badgeSystem:set(badge.B_ROLE_BAGUA, true)
		end
	end

	if result.SM_ClientMapState ~= nil then
		g_data.map.state = result.SM_ClientMapState

		if main_scene.ui.worldBossChallenge then
			main_scene.ui.worldBossChallenge:onM_CHANGEMAP()
		end
	end

	if result.SM_SendFlag_14 ~= nil then
		self.__godArmorOpen = result.SM_SendFlag_14 == 100
	end

	if result.SM_SendFlag_15 ~= nil then
		g_data.territoryBattle:setActive(result.SM_SendFlag_15 ~= 0)
	end

	if result.SM_CurrSeason ~= nil then
		g_data.seasonFashion:setCurPlayerPeriod(result.SM_CurrSeason)
	end
end

function mainui:onSM_CenSerUPRedPoint(result)
	if result and result.FInfoList and type(result.FInfoList) == "table" and #result.FInfoList > 0 and FuncOpen.isFuncOpen(gameFunc.FUNC1051) then
		g_data.firstOpen:set("temple", false)
		g_data.badgeSystem:set(badge.B_ROLE_TEMPLE, true)
	end
end

function mainui:onSM_DiamUseAllDiamBag(result, protoId)
	if result then
		local ret = result.FRet

		if ret == 1 then
			if result.FDesDiam ~= "" then
				local itemList = string.split(result.FDesDiam, ";")
				local strList = {}

				table.insert(strList, {
					"恭喜获得:\n",
					display.COLOR_WHITE
				})

				local item = {}
				local itemNum = #itemList

				for i, v in ipairs(itemList) do
					item = string.split(v, "|")

					table.insert(strList, {
						item[1],
						display.COLOR_RED
					})
					table.insert(strList, {
						"*",
						display.COLOR_WHITE
					})
					table.insert(strList, {
						item[2],
						display.COLOR_RED
					})

					if i ~= itemNum then
						table.insert(strList, {
							"、",
							display.COLOR_WHITE
						})
					end
				end

				an.newMsgbox(strList, function(idx)
					if idx == 1 then
						-- block empty
					end
				end, {
					center = true,
					btnTexts = {
						"确定",
						"取消"
					}
				})
			end
		else
			local opStr = "使用失败！"
			local reasonStr = ""

			if ret == -1 or ret == -4 then
				reasonStr = "宝石袋不存在"
			elseif ret == -2 then
				reasonStr = "异常操作"
			elseif ret == -3 then
				reasonStr = "背包空间不足"
			end

			if reasonStr ~= "" then
				opStr = opStr .. reasonStr
			end

			main_scene.ui:tip(opStr)
		end
	end
end

function mainui:showAirborneSoliderIcon(isShow)
	main_scene.ui.__airborneSoliderOpen = isShow

	if g_data.player.crossServerState == 1 then
		main_scene.ui.__airborneSoliderOpen = true
	end

	main_scene.ui.console:checkOpen({
		{
			name = "天降神兵",
			funcId = 1052,
			info = {
				main_scene.ui.__airborneSoliderOpen and 1 or 0
			}
		}
	})
	main_scene.ui:hidePanel("airborneSolider")
end

function mainui:onSM_NewHellPK_BoxSite(result, protoId)
	if not result or not result.FBoxList then
		return
	end

	self.newCrossChallenge:setBoxList(result.FBoxList)

	if main_scene.ui.panels.minimap then
		main_scene.ui.panels.minimap:uptNewHellPkBox(result.FBoxList)
	end
end

function mainui:onSM_DelayActionOP(result, protoId)
	if not result then
		return
	end

	if result.FIDx == 0 then
		main_scene.ui.loading:hide()

		main_scene.ui.console.controller.delayPickupItems = {}

		return
	end

	if def.playerDelayActionCfg[result.FIDx] then
		local cfg = def.playerDelayActionCfg[result.FIDx]

		if cfg.ActionType == 1 then
			local player = main_scene.ground.player

			if result.FCurr_X == player.x and result.FCurr_Y == player.y then
				local rsb = DefaultClientMessage(CM_PICKUP)

				rsb.Fx = player.x
				rsb.Fy = player.y

				MirTcpClient:getInstance():postRsb(rsb)
			end
		end
	end
end

function mainui:onTeamMemChange(roleid)
	if type(roleid) == "table" then
		for i, v in pairs(roleid) do
			local role = main_scene.ground.map:findRole(v)

			if role then
				local last = role.last

				role:processMsg(SM_CHARSTATUSCHANGED, nil, nil, nil, last.state)
			end
		end
	else
		local role = main_scene.ground.map:findRole(roleid)

		if not role then
			return
		end

		local last = role.last

		role:processMsg(SM_CHARSTATUSCHANGED, nil, nil, nil, last.state)
	end
end

function mainui:onSM_WuXueList(result)
	if not result then
		return
	end

	if not result.Fwxid then
		return
	end

	if not result.FWuXueList then
		return
	end

	if result.Fwxid == 0 then
		if result.FUserid == g_data.player.roleid then
			g_data.wuxue:setAllData(result.FWuXueList)
		else
			g_data.wuxue:setOtherAllData(result.FWuXueList)
		end
	else
		g_data.wuxue:setWuxueData(result.Fwxid, result.FWuXueList)
	end

	if (result.Fwxid == 1119 or result.Fwxid == 1004) and result.FUserid == g_data.player.roleid and result.FWuXueList[1] and result.FWuXueList[1].FEquipIdx == 0 and main_scene.ui.console.controller.lock.skill.data and checkExist(main_scene.ui.console.controller.lock.skill.data.FMagicId, 1003, 1004) then
		main_scene.ui.console.controller.lock:stop()
	end
end

function mainui:onSM_Sel_Exchg_Item(result)
	if not result then
		return
	end

	if main_scene.ui.panels.selectExchange then
		main_scene.ui:togglePanel("selectExchange")
	end

	local _, data = g_data.bag:getItem(result.Fident or 0)

	if data then
		main_scene.ui:togglePanel("selectExchange", {
			result,
			data
		})
	end
end

function mainui:onSM_TTWAR_Info(result)
	main_scene.ui.waiting:close("TERRITORYBATTLE")

	if not result then
		return
	end

	g_data.territoryBattle:setData(result)
end

function mainui:onSM_GildManor_Res(result)
	if result then
		if result.FOpId == 1 then
			if not result.FListInfo or #result.FListInfo == 0 then
				-- block empty
			else
				g_data.guildNewWorld:setCurBuildingData(result.FListInfo)
			end
		elseif result.FOpId ~= 2 or not result.FListInfo or #result.FListInfo == 0 then
			-- block empty
		else
			g_data.guildNewWorld:setCurStashData(result.FListInfo)
		end
	end
end

function mainui:onSM_PalGrow_OP(result, protoId)
	g_data.pal:updateByNetData(result, protoId)
	g_data.eventDispatcher:dispatch("PAL_GROW_CHG", result.FParam, result.FOpId)
end

return mainui
